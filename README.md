# 🏢 蔚之领域智能科技项目

基于现代化技术栈的企业级全栈项目，包含企业官网、管理后台和后端服务。

## 📁 项目结构

```
Nuxt3Web/
├── 📚 docs/                   # 项目文档中心
├── 🌐 web/                    # Nuxt3 企业官网前端
├── 🔧 admin/                  # Vue3 管理后台
├── ⚙️ server-go/              # Go 后端服务
├── 🚀 deployment/             # 生产部署配置
├── 🔨 scripts/                # 构建和部署脚本
└── 🔄 caddy/                  # 反向代理配置
```

## 🚀 快速开始

### 环境要求

- **Node.js** >= 18
- **Go** >= 1.23
- **MySQL** >= 8.0
- **pnpm** >= 8

### 启动服务

```bash
# 安装依赖
pnpm install

# 配置数据库
cd server-go
make create-database
make init-admin
make seed-data

# 启动所有服务
pnpm dev:all
```

### 访问地址

- **企业官网**: http://localhost:4000
- **管理后台**: http://localhost:3001
- **后端API**: http://localhost:3000
- **API文档**: http://localhost:3000/swagger/index.html

## 📚 项目文档

完整的文档请访问 [文档中心](./docs/README.md)

### 📖 主要文档

- 📋 [项目概述](./docs/getting-started/project-overview.md) - 项目简介、技术栈、架构概览
- 🚀 [快速开始](./docs/02-quick-start.md) - 环境搭建、项目启动指南
- 💻 [开发指南](./docs/development/) - 开发环境配置、代码规范、工具使用
- 📡 [API文档](./docs/api/) - 接口文档、API设计规范
- 🚀 [部署指南](./docs/deployment/) - 生产环境部署、容器化部署
- 🔧 [CI/CD配置](./docs/ci-cd/) - Gitea Actions、自动化构建部署

## 🛠️ 技术栈

### 前端技术栈
- **Vue 3** - 前端框架
- **Nuxt 3** - 全栈框架
- **TypeScript** - 类型系统
- **Element Plus** - UI组件库
- **UnoCSS** - CSS框架
- **Pinia** - 状态管理

### 后端技术栈
- **Go 1.23** - 编程语言
- **Gin** - Web框架
- **GORM** - ORM框架
- **MySQL 8.0** - 数据库
- **JWT** - 认证方案
- **Swagger** - API文档

## 🏗️ 构建部署

### 本地构建

```bash
# 构建所有项目
pnpm build

# 构建特定项目
pnpm build:web      # 企业官网
pnpm build:admin    # 管理后台
cd server-go && make build  # 后端服务
```

### 生产部署

生产环境使用Docker容器部署，详见 `deployment/` 目录：

```bash
# 生产环境部署
cd deployment
docker-compose up -d --build
```

## 📋 项目配置

### 环境变量配置
- **web/.env.development** - 前端开发环境配置
- **admin/.env** - 管理后台环境配置
- **server-go/config.yaml** - 后端服务配置
- **deployment/docker.env** - 生产环境Docker配置

### 数据库配置

```bash
# 创建数据库
make create-database

# 初始化表结构
make init-tables

# 导入种子数据
make seed-data

# 重置数据库
make reset-db
```

## ⚠️ 注意事项

1. **数据库配置**: 确保MySQL服务正常运行，配置正确的连接信息
2. **端口冲突**: 检查端口占用情况，避免端口冲突
3. **环境变量**: 根据实际环境配置相应的环境变量
4. **依赖安装**: 使用pnpm作为包管理器，确保依赖正确安装
5. **代码规范**: 遵循项目代码规范和Git提交规范

## 📞 获取帮助

- 📖 [完整文档](./docs/README.md) - 详细的项目文档
- 🐛 [问题反馈](https://github.com/your-org/weishi-project/issues) - 报告问题
- 💬 [技术支持](mailto:<EMAIL>) - 技术咨询

---

*最后更新: 2025-08-01*