name: 自动化部署

on:
  workflow_run:
    workflows: ["构建并推送"]
    types:
      - completed
  workflow_dispatch:
    inputs:
      environment:
        description: '部署环境'
        required: true
        default: 'production'
        type: choice
        options:
          - production
      services:
        description: '部署服务'
        required: true
        default: 'all'
        type: choice
        options:
          - all
          - web
          - admin
          - server-go
      version:
        description: '版本号 (如: 20241201-143022-a1b2c3d4, 留空使用latest)'
        required: false
        default: 'latest'
        type: string
      force_deploy:
        description: '强制部署（跳过健康检查）'
        required: false
        default: false
        type: boolean
      first_deployment:
        description: '首次部署（启用数据导入）'
        required: false
        default: false
        type: boolean
  


env:
  DEPLOY_TIMEOUT: 600
  HEALTH_CHECK_TIMEOUT: 300

jobs:
  # 部署条件检查
  check-deploy-conditions:
    runs-on: ubuntu-latest
    outputs:
      should_deploy: ${{ steps.check.outputs.should_deploy }}
      environment: ${{ steps.check.outputs.environment }}
      services: ${{ steps.check.outputs.services }}
      version: ${{ steps.resolve-version.outputs.version }}
      first_deployment: ${{ steps.check.outputs.first_deployment }}
    steps:
      - name: 检查部署条件
        id: check
        run: |
          if [ "${{ github.event_name }}" = "workflow_dispatch" ]; then
            # 手动触发部署
            echo "🔧 手动触发部署"
            echo "should_deploy=true" >> $GITHUB_OUTPUT
            echo "environment=${{ inputs.environment }}" >> $GITHUB_OUTPUT
            echo "services=${{ inputs.services }}" >> $GITHUB_OUTPUT
            echo "version=${{ inputs.version || 'latest' }}" >> $GITHUB_OUTPUT
            echo "first_deployment=${{ inputs.first_deployment || 'false' }}" >> $GITHUB_OUTPUT
            echo "trigger_type=manual" >> $GITHUB_OUTPUT
          elif [ "${{ github.event_name }}" = "workflow_run" ]; then
            # 构建完成后自动触发
            if [ "${{ github.event.workflow_run.conclusion }}" = "success" ]; then
              echo "🚀 构建成功，自动触发部署"
              echo "should_deploy=true" >> $GITHUB_OUTPUT
              echo "environment=production" >> $GITHUB_OUTPUT
              echo "services=all" >> $GITHUB_OUTPUT
              echo "version=latest" >> $GITHUB_OUTPUT
              echo "first_deployment=false" >> $GITHUB_OUTPUT
              echo "trigger_type=auto" >> $GITHUB_OUTPUT
            else
              echo "❌ 构建失败，跳过部署"
              echo "should_deploy=false" >> $GITHUB_OUTPUT
            fi
          else
            echo "should_deploy=false" >> $GITHUB_OUTPUT
            echo "first_deployment=false" >> $GITHUB_OUTPUT
          fi

  # 生产环境部署
  deploy-production:
    if: needs.check-deploy-conditions.outputs.should_deploy == 'true'
    needs: check-deploy-conditions
    runs-on: ubuntu-latest
    environment: production
    steps:
      - name: 下载构建制品
        uses: actions/download-artifact@v3
        with:
          name: weizhi-deploy-${{ needs.check-deploy-conditions.outputs.version }}
          path: ./artifacts

      - name: 配置部署环境
        run: |
          echo "ENVIRONMENT=production" >> $GITHUB_ENV
          echo "SERVICES=${{ needs.check-deploy-conditions.outputs.services }}" >> $GITHUB_ENV
          echo "VERSION=${{ needs.check-deploy-conditions.outputs.version }}" >> $GITHUB_ENV
          echo "DEPLOY_HOST=${{ vars.PROD_DEPLOY_HOST }}" >> $GITHUB_ENV
          echo "DEPLOY_USER=${{ vars.PROD_DEPLOY_USER }}" >> $GITHUB_ENV
          echo "DEPLOY_PATH=${{ vars.PROD_DEPLOY_PATH }}" >> $GITHUB_ENV

      - name: 设置 SSH 密钥
        run: |
          mkdir -p ~/.ssh
          echo "${{ secrets.PROD_SSH_PRIVATE_KEY }}" > ~/.ssh/id_rsa
          chmod 600 ~/.ssh/id_rsa
          ssh-keyscan -H ${{ env.DEPLOY_HOST }} >> ~/.ssh/known_hosts

      - name: 验证服务器连接
        run: |
          ssh -o ConnectTimeout=10 ${{ env.DEPLOY_USER }}@${{ env.DEPLOY_HOST }} "echo '服务器连接成功'"

      - name: 备份当前版本
        run: |
          ssh ${{ env.DEPLOY_USER }}@${{ env.DEPLOY_HOST }} "
            cd ${{ env.DEPLOY_PATH }}
            
            # 创建备份目录
            mkdir -p backups/$(date +%Y%m%d_%H%M%S)
            BACKUP_DIR=backups/$(date +%Y%m%d_%H%M%S)
            
            # 备份当前配置
            if [ -f docker-compose.prod.yml ]; then
              cp docker-compose.prod.yml \$BACKUP_DIR/
            fi
            if [ -f production.env ]; then
              cp production.env \$BACKUP_DIR/
            fi
            
            # 备份当前镜像信息
            docker images --format 'table {{.Repository}}:{{.Tag}}\t{{.ID}}\t{{.CreatedAt}}' | grep weizhi > \$BACKUP_DIR/images.txt || true
            
            echo '备份完成: '\$BACKUP_DIR
          "

      - name: 生成环境配置
        run: |
          # 设置必需的环境变量
          export REGISTRY_URL="${{ vars.REGISTRY_URL }}"
          export NAMESPACE="${{ vars.NAMESPACE }}"
          export VERSION="${{ env.VERSION }}"
          export MYSQL_ROOT_PASSWORD="${{ secrets.MYSQL_ROOT_PASSWORD }}"
          export MYSQL_PASSWORD="${{ secrets.MYSQL_PASSWORD }}"
          export JWT_SECRET="${{ secrets.JWT_SECRET }}"
          export ADMIN_PASSWORD="${{ secrets.ADMIN_PASSWORD }}"
          export PROD_DOMAIN="${{ vars.PROD_DOMAIN }}"
          export SSL_EMAIL="${{ vars.SSL_EMAIL }}"

          # 数据库配置
          export MYSQL_DATABASE="${{ vars.MYSQL_DATABASE }}"
          export MYSQL_USER="${{ vars.MYSQL_USER }}"
          export MYSQL_PORT="${{ vars.MYSQL_PORT }}"

          # 应用配置
          export ADMIN_USERNAME="${{ vars.ADMIN_USERNAME }}"
          export ADMIN_INIT_PASSWORD="${{ vars.ADMIN_INIT_PASSWORD }}"

          # 文件上传配置
          export MAX_FILE_SIZE="${{ vars.MAX_FILE_SIZE }}"
          export ALLOWED_FILE_TYPES="${{ vars.ALLOWED_FILE_TYPES }}"

          # 腾讯云COS配置
          export COS_REGION="${{ vars.COS_REGION }}"
          export COS_BUCKET="${{ vars.COS_BUCKET }}"
          export COS_SECRET_ID="${{ secrets.COS_SECRET_ID }}"
          export COS_SECRET_KEY="${{ secrets.COS_SECRET_KEY }}"
          export COS_DOMAIN="${{ vars.COS_DOMAIN }}"

          # 生成生产环境配置
          chmod +x deployment/scripts/generate-config.sh
          deployment/scripts/generate-config.sh production -o deployment/production.env

          echo "✅ 生产环境配置已生成"

      - name: 生成环境配置文件
        run: |
          # 创建临时环境配置文件
          cat > deployment.env << EOF
          # 镜像仓库配置
          REGISTRY_URL=${{ vars.REGISTRY_URL }}
          NAMESPACE=${{ vars.NAMESPACE }}
          VERSION=${{ env.VERSION }}

          # 数据库配置
          MYSQL_ROOT_PASSWORD=${{ secrets.MYSQL_ROOT_PASSWORD }}
          MYSQL_PASSWORD=${{ secrets.MYSQL_PASSWORD }}
          MYSQL_DATABASE=${{ vars.MYSQL_DATABASE }}
          MYSQL_USER=${{ vars.MYSQL_USER }}
          MYSQL_PORT=${{ vars.MYSQL_PORT }}

          # 应用配置
          JWT_SECRET=${{ secrets.JWT_SECRET }}
          ADMIN_PASSWORD=${{ secrets.ADMIN_PASSWORD }}
          DOMAIN=${{ vars.DOMAIN }}
          ADMIN_DOMAIN=${{ vars.ADMIN_DOMAIN }}
          WEB_PORT=${{ vars.WEB_PORT }}
          SERVER_PORT=${{ vars.SERVER_PORT }}

          # 可选配置
          MAX_FILE_SIZE=${{ vars.MAX_FILE_SIZE }}
          ALLOWED_FILE_TYPES=${{ vars.ALLOWED_FILE_TYPES }}
          JWT_EXPIRE_TIME=${{ vars.JWT_EXPIRE_TIME }}

          # 第三方服务配置（如果配置了）
          COS_SECRET_ID=${{ secrets.COS_SECRET_ID }}
          COS_SECRET_KEY=${{ secrets.COS_SECRET_KEY }}
          COS_REGION=${{ vars.COS_REGION }}
          COS_BUCKET=${{ vars.COS_BUCKET }}
          EOF

          echo "✅ 环境配置文件生成完成"
          echo "使用版本: ${{ env.VERSION }}"

      - name: 上传部署制品
        run: |
          echo "=== 上传部署制品到服务器 ==="
          echo "当前版本: ${{ env.VERSION }}"

          # 检查制品文件是否存在
          ARTIFACT_FILE="artifacts/weizhi-deploy-${{ env.VERSION }}.tar.gz"
          echo "查找制品文件: $ARTIFACT_FILE"
          
          if [ ! -f "$ARTIFACT_FILE" ]; then
            echo "❌ 制品文件不存在: $ARTIFACT_FILE"
            echo "当前目录内容:"
            ls -la artifacts/
            exit 1
          fi

          echo "✅ 找到制品文件: $ARTIFACT_FILE"
          echo "文件大小: $(du -h $ARTIFACT_FILE | cut -f1)"

          # 上传制品到服务器
          scp "$ARTIFACT_FILE" ${{ env.DEPLOY_USER }}@${{ env.DEPLOY_HOST }}:${{ env.DEPLOY_PATH }}/

          # 上传环境配置文件
          scp deployment.env ${{ env.DEPLOY_USER }}@${{ env.DEPLOY_HOST }}:${{ env.DEPLOY_PATH }}/production.env

          echo "✅ 制品上传完成"

      - name: 解压部署制品
        run: |
          ssh ${{ env.DEPLOY_USER }}@${{ env.DEPLOY_HOST }} "
            cd ${{ env.DEPLOY_PATH }}
            VERSION='${{ env.VERSION }}'
            echo '使用版本: '\$VERSION

            echo '=== 解压部署制品 ==='

            # 备份当前配置（如果存在）
            if [ -f docker-compose.prod.yml ]; then
              cp docker-compose.prod.yml docker-compose.prod.yml.backup
              echo '✅ 已备份当前配置'
            fi

            # 解压制品
            ARTIFACT_FILE='weizhi-deploy-'\$VERSION'.tar.gz'
            echo '查找制品文件: '\$ARTIFACT_FILE
            if [ -f \"\$ARTIFACT_FILE\" ]; then
              tar -xzf \"\$ARTIFACT_FILE\"
              echo '✅ 制品解压完成'

              # 显示解压内容
              echo '解压内容:'
              ls -la

              # 验证关键文件
              if [ -f docker-compose.prod.yml ]; then
                echo '✅ Docker Compose 文件存在'
              else
                echo '❌ Docker Compose 文件缺失'
                exit 1
              fi

              if [ -f VERSION ]; then
                echo '✅ 版本文件存在'
                echo '版本信息:'
                cat VERSION
              else
                echo '❌ 版本文件缺失'
              fi

              # 设置脚本执行权限
              if [ -f quick-deploy.sh ]; then
                chmod +x quick-deploy.sh
                echo '✅ 快速部署脚本权限已设置'
              fi

              # 清理制品文件
              rm \"\$ARTIFACT_FILE\"
              echo '✅ 制品文件已清理'
            else
              echo '❌ 制品文件不存在: \$ARTIFACT_FILE'
              exit 1
            fi
          "

      - name: 拉取最新镜像
        run: |
          ssh ${{ env.DEPLOY_USER }}@${{ env.DEPLOY_HOST }} "
            cd ${{ env.DEPLOY_PATH }}

            # 加载环境配置文件
            if [ -f production.env ]; then
              set -a
              source production.env
              set +a
              echo '✅ 已加载环境配置'
              echo '使用版本: '\$VERSION
            else
              echo '❌ 未找到环境配置文件'
              exit 1
            fi

            # 设置镜像仓库认证
            echo '${{ secrets.REGISTRY_PASSWORD }}' | docker login ${{ vars.REGISTRY_URL }} -u '${{ secrets.REGISTRY_USERNAME }}' --password-stdin

            # 拉取指定服务的镜像
            if [ '${{ env.SERVICES }}' = 'all' ]; then
              echo '拉取所有服务镜像...'
              docker compose -f docker-compose.prod.yml pull
            else
              echo '拉取服务镜像: ${{ env.SERVICES }}'
              case '${{ env.SERVICES }}' in
                'web')
                  docker compose -f docker-compose.prod.yml pull web
                  ;;
                'admin')
                  docker compose -f docker-compose.prod.yml pull caddy
                  ;;
                'server-go')
                  docker compose -f docker-compose.prod.yml pull server
                  ;;
              esac
            fi

            echo '✅ 镜像拉取完成'
          "

      - name: 验证镜像版本
        run: |
          ssh ${{ env.DEPLOY_USER }}@${{ env.DEPLOY_HOST }} "
            cd ${{ env.DEPLOY_PATH }}

            # 加载环境配置
            source production.env

            echo '=== 验证镜像版本一致性 ==='
            echo '期望版本: '\$VERSION
            echo ''

            # 检查已拉取的镜像
            echo '已拉取的镜像:'
            docker images | grep \$NAMESPACE | grep weizhi
            echo ''

            # 验证关键镜像是否存在
            if docker images | grep -q \"\$REGISTRY_URL/\$NAMESPACE/weizhi_server:\$VERSION\"; then
              echo '✅ 后端镜像版本正确'
            else
              echo '❌ 后端镜像版本不匹配'
            fi

            if docker images | grep -q \"\$REGISTRY_URL/\$NAMESPACE/weizhi_web:\$VERSION\"; then
              echo '✅ 前端镜像版本正确'
            else
              echo '❌ 前端镜像版本不匹配'
            fi

            if docker images | grep -q \"\$REGISTRY_URL/\$NAMESPACE/weizhi_admin_caddy:\$VERSION\"; then
              echo '✅ 管理后台镜像版本正确'
            else
              echo '❌ 管理后台镜像版本不匹配'
            fi
          "

      - name: 部署服务
        run: |
          ssh ${{ env.DEPLOY_USER }}@${{ env.DEPLOY_HOST }} "
            cd ${{ env.DEPLOY_PATH }}

            # 加载生产环境配置
            if [ -f production.env ]; then
              set -a
              source production.env
              set +a
              echo '✅ 已加载生产环境配置'
              echo '部署版本: '\$VERSION
            else
              echo '❌ 未找到生产环境配置文件'
              exit 1
            fi
            
            # 部署指定服务
            if [ '${{ env.SERVICES }}' = 'all' ]; then
              echo '部署所有服务...'
              docker compose -f docker-compose.prod.yml up -d
            else
              echo '部署服务: ${{ env.SERVICES }}'
              case '${{ env.SERVICES }}' in
                'web')
                  docker compose -f docker-compose.prod.yml up -d web
                  ;;
                'admin')
                  docker compose -f docker-compose.prod.yml up -d caddy
                  ;;
                'server-go')
                  docker compose -f docker-compose.prod.yml up -d server
                  ;;
              esac
            fi
          "

      - name: 等待服务启动
        run: |
          echo "等待服务启动..."
          sleep 30

      - name: 健康检查
        if: ${{ !inputs.force_deploy }}
        run: |
          ssh ${{ env.DEPLOY_USER }}@${{ env.DEPLOY_HOST }} "
            cd ${{ env.DEPLOY_PATH }}
            
            # 检查容器状态
            echo '=== 容器状态 ==='
            docker compose -f docker-compose.prod.yml ps
            
            # 检查服务健康状态
            echo '=== 健康检查 ==='
            timeout ${{ env.HEALTH_CHECK_TIMEOUT }} bash -c \'
              while true; do
                # 检查后端API
                if curl -f -s http://localhost:3001/api/health > /dev/null; then
                  echo \"✅ 后端服务健康\"
                  break
                else
                  echo \"⏳ 等待后端服务启动...\"
                  sleep 10
                fi
              done
            \'
            
            timeout ${{ env.HEALTH_CHECK_TIMEOUT }} bash -c \'
              while true; do
                # 检查前端服务
                if curl -f -s http://localhost:3000 > /dev/null; then
                  echo \"✅ 前端服务健康\"
                  break
                else
                  echo \"⏳ 等待前端服务启动...\"
                  sleep 10
                fi
              done
            \'
          "

      - name: 首次部署数据导入
        if: needs.check-deploy-conditions.outputs.first_deployment == 'true'
        run: |
          echo "🔄 执行首次部署数据导入..."
          ssh ${{ env.DEPLOY_USER }}@${{ env.DEPLOY_HOST }} "
            cd ${{ env.DEPLOY_PATH }}

            echo '=== 首次部署数据导入 ==='
            echo '启动数据导入服务...'

            # 设置数据导入环境变量
            export IMPORT_FULL_DATA=true

            # 启动数据导入服务
            docker compose --profile data-import up data-import

            # 检查导入结果
            echo '=== 数据导入结果 ==='
            docker compose logs data-import

            # 检查数据导入是否成功
            if docker compose ps data-import | grep -q 'exited (0)'; then
              echo '✅ 数据导入成功完成'
            else
              echo '⚠️ 数据导入可能失败，请检查日志'
              docker compose logs data-import
            fi

            # 清理数据导入容器
            docker compose rm -f data-import || true
          "

      - name: 清理旧镜像
        run: |
          ssh ${{ env.DEPLOY_USER }}@${{ env.DEPLOY_HOST }} "
            # 清理未使用的镜像
            docker image prune -f
            
            # 清理旧的备份（保留最近10个）
            cd ${{ env.DEPLOY_PATH }}/backups
            ls -t | tail -n +11 | xargs -r rm -rf
          "

      - name: 部署成功通知
        run: |
          echo "🎉 生产环境部署成功!"
          echo "环境: ${{ env.ENVIRONMENT }}"
          echo "服务: ${{ env.SERVICES }}"
          echo "版本: ${{ env.VERSION }}"
          echo "时间: $(date)"



  # 部署失败处理
  handle-deploy-failure:
    if: failure()
    needs: [deploy-production]
    runs-on: ubuntu-latest
    steps:
      - name: 部署失败通知
        run: |
          echo "❌ 部署失败!"
          echo "请检查部署日志并考虑回滚操作"
          echo "回滚命令: 在服务器上运行 'docker compose -f docker-compose.prod.yml down && docker compose -f docker-compose.prod.yml up -d'"
