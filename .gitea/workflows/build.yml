name: 构建并推送

on:
  workflow_dispatch:
    inputs:
      services:
        description: '要构建的服务'
        required: true
        default: 'core'
        type: choice
        options:
          - core
          - all
          - web
          - admin
          - server-go
      custom_tag:
        description: '自定义标签 (留空则使用自动生成的标签)'
        required: false
        default: ''
        type: string
      push_to_registry:
        description: '推送到外部镜像仓库'
        required: false
        default: true
        type: boolean
      push_to_gitea:
        description: '推送到 Gitea 软件包仓库'
        required: false
        default: true
        type: boolean
      create_artifacts:
        description: '创建制品包'
        required: false
        default: true
        type: boolean
      deploy_after_build:
        description: '构建完成后自动部署'
        required: false
        default: false
        type: boolean
      first_deployment:
        description: '首次部署（启用数据导入）'
        required: false
        default: false
        type: boolean

env:
  # 只保留非敏感的Variables
  NODE_VERSION: ${{ vars.NODE_VERSION || '18' }}
  GO_VERSION: ${{ vars.GO_VERSION || '1.23' }}
  PACKAGE_REGISTRY: ${{ vars.PACKAGE_REGISTRY || 'gitea.example.com' }}

jobs:
  # 解析参数
  parse-params:
    name: 解析参数
    runs-on: ubuntu-latest
    outputs:
      services: ${{ steps.trigger_params.outputs.services }}
      custom_tag: ${{ steps.trigger_params.outputs.custom_tag }}
      push_to_registry: ${{ steps.trigger_params.outputs.push_to_registry }}
      push_to_gitea: ${{ steps.trigger_params.outputs.push_to_gitea }}
      create_artifacts: ${{ steps.trigger_params.outputs.create_artifacts }}
      deploy_after_build: ${{ steps.trigger_params.outputs.deploy_after_build }}
      first_deployment: ${{ steps.trigger_params.outputs.first_deployment }}
      version: ${{ steps.build_info.outputs.version }}
      build_time: ${{ steps.build_info.outputs.build_time }}
    steps:
      - name: 检出代码
        uses: actions/checkout@v4

      - name: 解析手动触发参数
        id: trigger_params
        run: |
          # 手动触发，使用输入参数
          services="${{ github.event.inputs.services || 'core' }}"

          # 根据选择决定构建哪些服务
          if [ "$services" = "core" ] || [ "$services" = "all" ]; then
            echo "services=[\"web\",\"admin\",\"server-go\"]" >> $GITHUB_OUTPUT
            echo "构建所有服务"
          else
            echo "services=[\"$services\"]" >> $GITHUB_OUTPUT
            echo "构建单个服务: $services"
          fi
          
          echo "custom_tag=${{ github.event.inputs.custom_tag || '' }}" >> $GITHUB_OUTPUT
          echo "push_to_registry=${{ github.event.inputs.push_to_registry || 'true' }}" >> $GITHUB_OUTPUT
          echo "push_to_gitea=${{ github.event.inputs.push_to_gitea || 'true' }}" >> $GITHUB_OUTPUT
          echo "create_artifacts=${{ github.event.inputs.create_artifacts || 'true' }}" >> $GITHUB_OUTPUT
          echo "deploy_after_build=${{ github.event.inputs.deploy_after_build || 'false' }}" >> $GITHUB_OUTPUT
          echo "first_deployment=${{ github.event.inputs.first_deployment || 'false' }}" >> $GITHUB_OUTPUT
          
          echo "触发方式: 手动触发"
          echo "构建服务: $services"
          echo "自定义标签: ${{ github.event.inputs.custom_tag || '自动生成' }}"
          echo "推送镜像: ${{ github.event.inputs.push_to_registry || 'true' }}"
          echo "创建制品: ${{ github.event.inputs.create_artifacts || 'true' }}"
          echo "自动部署: ${{ github.event.inputs.deploy_after_build || 'false' }}"
          echo "首次部署: ${{ github.event.inputs.first_deployment || 'false' }}"

      - name: 生成构建信息
        id: build_info
        run: |
          # 检查是否有自定义标签
          if [ -n "${{ github.event.inputs.custom_tag }}" ]; then
            # 使用自定义标签
            VERSION="${{ github.event.inputs.custom_tag }}"
            echo "使用自定义标签: $VERSION"
          else
            # 使用自动生成的标签
            VERSION="$(date +%Y%m%d-%H%M%S)-${GITHUB_SHA:0:8}"
            echo "使用自动生成标签: $VERSION"
          fi
          
          echo "version=${VERSION}" >> $GITHUB_OUTPUT
          echo "build_time=$(date -u +%Y-%m-%dT%H:%M:%SZ)" >> $GITHUB_OUTPUT

  # 构建阶段
  build:
    name: 构建服务
    runs-on: ubuntu-latest
    needs: parse-params
    strategy:
      matrix:
        service: [web, admin, server-go]
      fail-fast: false        # 一个服务失败不停止其他服务构建
      max-parallel: 3         # 最多同时运行3个构建任务
    if: needs.parse-params.outputs.services != 'none'
    steps:
      - name: 检出代码
        uses: actions/checkout@v4

      - name: 设置Docker Buildx
        uses: docker/setup-buildx-action@v3
        with:
          config-inline: |
            [registry."${{ env.PACKAGE_REGISTRY }}"]
              http = true
              insecure = true

      - name: 并行构建状态
        run: |
          echo "🚀 开始并行构建服务: ${{ matrix.service }}"
          echo "构建开始时间: $(date '+%Y-%m-%d %H:%M:%S')"
          echo "Runner ID: ${{ github.run_id }}-${{ strategy.job-index }}"
          echo "当前任务索引: ${{ strategy.job-index }}"
          echo "总任务数: ${{ strategy.job-total }}"

          # 显示并行构建信息
          echo ""
          echo "=== 并行构建配置 ==="
          echo "最大并行数: 3"
          echo "失败快速停止: false"
          echo "当前服务: ${{ matrix.service }}"
          echo ""
          echo "=== 调试信息 ==="
          echo "matrix.service: '${{ matrix.service }}'"
          SERVICE="${{ matrix.service }}"
          echo "SERVICE变量: '$SERVICE'"
          echo "services: '${{ needs.parse-params.outputs.services }}'"
          echo "version: '${{ needs.parse-params.outputs.version }}'"
          echo "检查是否应该构建当前服务:"
          if [[ "${{ needs.parse-params.outputs.services }}" == *"${{ matrix.service }}"* ]] || [[ "${{ needs.parse-params.outputs.services }}" == *"all"* ]]; then
            echo "✅ 应该构建 ${{ matrix.service }}"
          else
            echo "❌ 不应该构建 ${{ matrix.service }}"
          fi

      - name: 检查是否应该构建当前服务
        id: should_build
        run: |
          echo "=== 检查是否应该构建当前服务 ==="
          services="${{ needs.parse-params.outputs.services }}"
          current_service="${{ matrix.service }}"
          
          echo "请求构建的服务: $services"
          echo "当前服务: $current_service"
          
          # 检查是否应该构建当前服务
          if [[ "$services" == *"$current_service"* ]] || [[ "$services" == *"all"* ]]; then
            echo "should_build=true" >> $GITHUB_OUTPUT
            echo "✅ 应该构建 $current_service"
          else
            echo "should_build=false" >> $GITHUB_OUTPUT
            echo "❌ 跳过构建 $current_service"
          fi

      - name: 获取仓库名称
        if: steps.should_build.outputs.should_build == 'true'
        id: repo_name
        run: |
          echo "=== 获取仓库名称 ==="
          echo "当前服务: '${{ matrix.service }}'"
          
          # 根据服务类型设置固定的镜像名
          case "${{ matrix.service }}" in
            "web")
              echo "name=weizhi_web" >> $GITHUB_OUTPUT
              echo "设置镜像名为: weizhi_web"
              ;;
            "admin")
              echo "name=weizhi_admin" >> $GITHUB_OUTPUT
              echo "设置镜像名为: weizhi_admin"
              ;;
            "server-go")
              echo "name=weizhi_server" >> $GITHUB_OUTPUT
              echo "设置镜像名为: weizhi_server"
              ;;
            *)
              echo "name=weizhi_${{ matrix.service }}" >> $GITHUB_OUTPUT
              echo "设置镜像名为: weizhi_${{ matrix.service }}"
              ;;
          esac
          
          echo "最终镜像名: ${{ steps.repo_name.outputs.name }}"

      - name: 设置Node.js (前端服务)
        if: steps.should_build.outputs.should_build == 'true' && (matrix.service == 'web' || matrix.service == 'admin')
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: 设置Go (后端服务)
        if: steps.should_build.outputs.should_build == 'true' && matrix.service == 'server-go'
        uses: actions/setup-go@v4
        with:
          go-version: ${{ env.GO_VERSION }}
          cache-dependency-path: server-go/go.sum

      - name: 登录外部容器镜像仓库
        if: steps.should_build.outputs.should_build == 'true' && needs.parse-params.outputs.push_to_registry == 'true'
        uses: docker/login-action@v3
        with:
          registry: ${{ vars.REGISTRY_URL }}
          username: ${{ secrets.REGISTRY_USERNAME }}
          password: ${{ secrets.REGISTRY_PASSWORD }}

      - name: 验证 Gitea 配置
        if: steps.should_build.outputs.should_build == 'true' && needs.parse-params.outputs.push_to_gitea == 'true'
        run: |
          echo "🔍 验证 Gitea 软件包仓库配置..."
          echo "Registry: ${{ env.PACKAGE_REGISTRY }}"
          echo "Username: ${{ github.actor }}"
          echo "Token configured: ${{ secrets.PACKAGE_TOKEN != '' }}"

          if [ -z "${{ env.PACKAGE_REGISTRY }}" ]; then
            echo "❌ PACKAGE_REGISTRY 变量未配置"
            exit 1
          fi

          if [ -z "${{ secrets.PACKAGE_TOKEN }}" ]; then
            echo "❌ PACKAGE_TOKEN 密钥未配置"
            exit 1
          fi

          echo "✅ 配置验证通过"

      - name: 登录 Gitea 软件包仓库
        if: steps.should_build.outputs.should_build == 'true' && needs.parse-params.outputs.push_to_gitea == 'true'
        uses: docker/login-action@v3
        with:
          registry: ${{ env.PACKAGE_REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.PACKAGE_TOKEN }}

      - name: 调试镜像标签信息
        if: steps.should_build.outputs.should_build == 'true' && needs.parse-params.outputs.push_to_registry == 'true'
        run: |
          echo "=== 调试镜像标签信息 ==="
          echo "REGISTRY_URL: ${{ vars.REGISTRY_URL }}"
          echo "NAMESPACE: ${{ vars.NAMESPACE }}"
          echo "镜像名: ${{ steps.repo_name.outputs.name }}"
          echo "版本: ${{ needs.parse-params.outputs.version }}"
          echo ""
          echo "完整镜像标签:"
          echo "${{ vars.REGISTRY_URL }}/${{ vars.NAMESPACE }}/${{ steps.repo_name.outputs.name }}:${{ needs.parse-params.outputs.version }}"
          echo "${{ vars.REGISTRY_URL }}/${{ vars.NAMESPACE }}/${{ steps.repo_name.outputs.name }}:latest"
          echo ""
          echo "请检查上述标签格式是否正确"

      - name: 检查Variables和Secrets
        if: steps.should_build.outputs.should_build == 'true' && needs.parse-params.outputs.push_to_registry == 'true'
        run: |
          echo "=== 检查Variables和Secrets ==="
          echo "检查REGISTRY_URL (Variables):"
          if [ -n "${{ vars.REGISTRY_URL }}" ]; then
            echo "✅ REGISTRY_URL 在 Variables 中已设置"
            echo "值: ${{ vars.REGISTRY_URL }}"
          else
            echo "❌ REGISTRY_URL 在 Variables 中未设置"
          fi
          
          echo ""
          echo "检查NAMESPACE (Variables):"
          if [ -n "${{ vars.NAMESPACE }}" ]; then
            echo "✅ NAMESPACE 在 Variables 中已设置"
            echo "值: ${{ vars.NAMESPACE }}"
          else
            echo "❌ NAMESPACE 在 Variables 中未设置"
          fi
          
          echo ""
          echo "检查REGISTRY_USERNAME (Secrets):"
          if [ -n "${{ secrets.REGISTRY_USERNAME }}" ]; then
            echo "✅ REGISTRY_USERNAME 在 Secrets 中已设置"
          else
            echo "❌ REGISTRY_USERNAME 在 Secrets 中未设置"
          fi
          
          echo ""
          echo "检查REGISTRY_PASSWORD (Secrets):"
          if [ -n "${{ secrets.REGISTRY_PASSWORD }}" ]; then
            echo "✅ REGISTRY_PASSWORD 在 Secrets 中已设置"
          else
            echo "❌ REGISTRY_PASSWORD 在 Secrets 中未设置"
          fi

      - name: 直接显示配置值
        if: steps.should_build.outputs.should_build == 'true' && needs.parse-params.outputs.push_to_registry == 'true'
        run: |
          echo "=== 直接显示配置值 ==="
          echo "检查REGISTRY_URL是否为空:"
          if [ -z "${{ vars.REGISTRY_URL }}" ]; then
            echo "❌ REGISTRY_URL 为空"
          else
            echo "✅ REGISTRY_URL 已设置"
          fi
          
          echo "检查NAMESPACE是否为空:"
          if [ -z "${{ vars.NAMESPACE }}" ]; then
            echo "❌ NAMESPACE 为空"
          else
            echo "✅ NAMESPACE 已设置"
          fi
          
          echo ""
          echo "当前配置:"
          echo "REGISTRY_URL='${{ vars.REGISTRY_URL }}'"
          echo "NAMESPACE='${{ vars.NAMESPACE }}'"
          echo "镜像名='${{ steps.repo_name.outputs.name }}'"
          echo "版本='${{ needs.parse-params.outputs.version }}'"
          
          echo ""
          echo "生成的标签:"
          echo "'${{ vars.REGISTRY_URL }}/${{ vars.NAMESPACE }}/${{ steps.repo_name.outputs.name }}:${{ needs.parse-params.outputs.version }}'"
          echo "'${{ vars.REGISTRY_URL }}/${{ vars.NAMESPACE }}/${{ steps.repo_name.outputs.name }}:latest'"

      - name: 构建并推送到外部镜像仓库
        if: steps.should_build.outputs.should_build == 'true' && needs.parse-params.outputs.push_to_registry == 'true'
        uses: docker/build-push-action@v5
        with:
          context: .
          file: ./deployment/Dockerfile.${{ matrix.service }}
          platforms: linux/amd64
          push: true
          tags: |
            ${{ vars.REGISTRY_URL }}/${{ vars.NAMESPACE }}/${{ steps.repo_name.outputs.name }}:${{ needs.parse-params.outputs.version }}
            ${{ vars.REGISTRY_URL }}/${{ vars.NAMESPACE }}/${{ steps.repo_name.outputs.name }}:latest
          build-args: |
            VERSION=${{ needs.parse-params.outputs.version }}
            BUILD_TIME=${{ needs.parse-params.outputs.build_time }}
            NODE_ENV=production
            NITRO_PRESET=node-server
            VITE_API_BASE_URL=/api
          cache-from: type=gha
          cache-to: type=gha,mode=max

      - name: 构建并推送到 Gitea 软件包仓库
        if: steps.should_build.outputs.should_build == 'true' && needs.parse-params.outputs.push_to_gitea == 'true'
        uses: docker/build-push-action@v5
        with:
          context: .
          file: ./deployment/Dockerfile.${{ matrix.service }}
          platforms: linux/amd64
          push: true
          tags: |
            ${{ env.PACKAGE_REGISTRY }}/${{ github.repository_owner }}/${{ steps.repo_name.outputs.name }}:${{ needs.parse-params.outputs.version }}
            ${{ env.PACKAGE_REGISTRY }}/${{ github.repository_owner }}/${{ steps.repo_name.outputs.name }}:latest
          build-args: |
            VERSION=${{ needs.parse-params.outputs.version }}
            BUILD_TIME=${{ needs.parse-params.outputs.build_time }}
            NODE_ENV=production
            NITRO_PRESET=node-server
            VITE_API_BASE_URL=/api
          cache-from: type=gha
          cache-to: type=gha,mode=max

      - name: 生成构建元数据
        if: steps.should_build.outputs.should_build == 'true'
        run: |
          echo "✅ ${{ matrix.service }} 服务构建完成"
          echo "完成时间: $(date '+%Y-%m-%d %H:%M:%S')"

          mkdir -p artifacts/${{ matrix.service }}
          cat > artifacts/${{ matrix.service }}/metadata.json << EOF
          {
            "service": "${{ matrix.service }}",
            "version": "${{ needs.parse-params.outputs.version }}",
            "build_time": "${{ needs.parse-params.outputs.build_time }}",
            "build_start_time": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
            "commit_sha": "${{ github.sha }}",
            "branch": "${{ github.ref_name }}",
            "job_index": "${{ strategy.job-index }}",
            "external_registry": {
              "enabled": ${{ needs.parse-params.outputs.push_to_registry }},
              "image": "${{ vars.REGISTRY_URL }}/${{ vars.NAMESPACE }}/${{ steps.repo_name.outputs.name }}:${{ needs.parse-params.outputs.version }}"
            },
            "gitea_registry": {
              "enabled": ${{ needs.parse-params.outputs.push_to_gitea }},
              "image": "${{ env.PACKAGE_REGISTRY }}/${{ github.repository_owner }}/${{ steps.repo_name.outputs.name }}:${{ needs.parse-params.outputs.version }}"
            }
          }
          EOF

      - name: 上传构建元数据
        if: steps.should_build.outputs.should_build == 'true'
        uses: actions/upload-artifact@v3
        with:
          name: ${{ matrix.service }}-metadata
          path: artifacts/${{ matrix.service }}/metadata.json
          retention-days: 30

  # 创建制品
  create-artifacts:
    name: 创建制品包
    runs-on: ubuntu-latest
    needs: [parse-params, build]
    if: needs.parse-params.outputs.create_artifacts == 'true'
    steps:
      - name: 检出代码
        uses: actions/checkout@v4

      - name: 创建制品包
        run: |
          echo "=== 创建制品包 ==="
          
          # 创建部署包目录
          mkdir -p deploy-package
          
          # 复制Docker Compose文件
          if [ -f "deployment/docker-compose.prod.yml" ]; then
            cp deployment/docker-compose.prod.yml deploy-package/
          fi
          
          # 复制配置文件
          if [ -f "server-go/config.prod.yaml" ]; then
            cp server-go/config.prod.yaml deploy-package/
          fi
          
          # 创建版本信息
          cat > deploy-package/VERSION << EOF
          VERSION=${{ needs.parse-params.outputs.version }}
          BUILD_TIME=$(date -u +%Y-%m-%dT%H:%M:%SZ)
          COMMIT_SHA=${{ github.sha }}
          BRANCH=${{ github.ref_name }}
          REGISTRY_URL=${{ vars.REGISTRY_URL }}
          NAMESPACE=${{ vars.NAMESPACE }}
          EOF
          
          # 创建快速部署脚本
          cat > deploy-package/quick-deploy.sh << 'EOF'
          #!/bin/bash
          set -e

          echo "=== Weizhi 项目快速部署 ==="

          # 加载版本信息
          if [ -f VERSION ]; then
            source VERSION
            export VERSION REGISTRY_URL NAMESPACE
            echo "部署版本: $VERSION"
          else
            echo "❌ VERSION 文件不存在"
            exit 1
          fi

          # 加载生产环境配置
          if [ -f production.env ]; then
            set -a
            source production.env
            set +a
            echo "✅ 已加载生产环境配置"
          else
            echo "❌ production.env 文件不存在"
            exit 1
          fi

          # 验证 Docker Compose 文件
          if [ ! -f docker-compose.prod.yml ]; then
            echo "❌ docker-compose.prod.yml 文件不存在"
            exit 1
          fi

          echo "=== 拉取镜像 ==="
          docker compose -f docker-compose.prod.yml pull

          echo "=== 启动服务 ==="
          docker compose -f docker-compose.prod.yml up -d

          echo "=== 等待服务启动 ==="
          sleep 30

          echo "=== 检查服务状态 ==="
          docker compose -f docker-compose.prod.yml ps

          echo "✅ 快速部署完成！"
          echo "访问地址: https://$DOMAIN"
          echo "管理后台: https://$ADMIN_DOMAIN"
          EOF
          
          chmod +x deploy-package/quick-deploy.sh
          
          # 打包（使用固定名称）
          tar -czf weizhi-deploy-v0.1.0.tar.gz -C deploy-package .

      - name: 上传制品包
        uses: actions/upload-artifact@v3
        with:
          name: weizhi-deploy-v0.1.0
          path: weizhi-deploy-v0.1.0.tar.gz
          retention-days: 30

  # 构建总结
  summary:
    name: 构建总结
    runs-on: ubuntu-latest
    needs: [parse-params, build, create-artifacts]
    if: always()
    steps:
      - name: 下载构建元数据
        uses: actions/download-artifact@v3
        with:
          path: ./artifacts

      - name: 生成构建总结
        run: |
          echo "## 🚀 手动构建完成" >> $GITHUB_STEP_SUMMARY
          echo "- **触发方式**: 手动触发" >> $GITHUB_STEP_SUMMARY
          echo "- **触发时间**: $(date -u +%Y-%m-%dT%H:%M:%SZ)" >> $GITHUB_STEP_SUMMARY
          echo "- **构建服务**: ${{ github.event.inputs.services || 'core' }}" >> $GITHUB_STEP_SUMMARY
          
          # 显示标签信息
          if [ -n "${{ github.event.inputs.custom_tag }}" ]; then
            echo "- **使用标签**: 自定义标签 \`${{ github.event.inputs.custom_tag }}\`" >> $GITHUB_STEP_SUMMARY
          else
            echo "- **使用标签**: 自动生成标签" >> $GITHUB_STEP_SUMMARY
          fi
          
          echo "- **推送镜像**: ${{ github.event.inputs.push_to_registry || 'true' }}" >> $GITHUB_STEP_SUMMARY
          echo "- **创建制品**: ${{ github.event.inputs.create_artifacts || 'true' }}" >> $GITHUB_STEP_SUMMARY
          echo "- **自动部署**: ${{ github.event.inputs.deploy_after_build || 'false' }}" >> $GITHUB_STEP_SUMMARY
          echo "- **首次部署**: ${{ github.event.inputs.first_deployment || 'false' }}" >> $GITHUB_STEP_SUMMARY
          echo "- **镜像版本**: \`${{ needs.build.outputs.version }}\`" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          
          if [ "${{ needs.build.result }}" = "success" ]; then
            echo "### ✅ 构建状态" >> $GITHUB_STEP_SUMMARY
            echo "- **状态**: 成功" >> $GITHUB_STEP_SUMMARY
            
            # 显示构建的镜像信息
            echo "" >> $GITHUB_STEP_SUMMARY
            echo "### 🐳 构建的镜像" >> $GITHUB_STEP_SUMMARY

            for metadata_file in artifacts/*/metadata.json; do
              if [ -f "$metadata_file" ]; then
                service=$(jq -r '.service' "$metadata_file")
                external_enabled=$(jq -r '.external_registry.enabled' "$metadata_file")
                external_image=$(jq -r '.external_registry.image' "$metadata_file")
                gitea_enabled=$(jq -r '.gitea_registry.enabled' "$metadata_file")
                gitea_image=$(jq -r '.gitea_registry.image' "$metadata_file")

                echo "#### $service 服务" >> $GITHUB_STEP_SUMMARY
                if [ "$external_enabled" = "true" ]; then
                  echo "- **外部仓库**: \`$external_image\`" >> $GITHUB_STEP_SUMMARY
                fi
                if [ "$gitea_enabled" = "true" ]; then
                  echo "- **Gitea 软件包**: \`$gitea_image\`" >> $GITHUB_STEP_SUMMARY
                fi
                echo "" >> $GITHUB_STEP_SUMMARY
              fi
            done
            
            if [ "${{ github.event.inputs.create_artifacts }}" = "true" ] && [ "${{ needs.create-artifacts.result }}" = "success" ]; then
              echo "" >> $GITHUB_STEP_SUMMARY
              echo "### 📦 制品包" >> $GITHUB_STEP_SUMMARY
              echo "- 部署包: \`weizhi-deploy-*.tar.gz\`" >> $GITHUB_STEP_SUMMARY
            fi
          else
            echo "### ❌ 构建状态" >> $GITHUB_STEP_SUMMARY
            echo "- **状态**: 失败" >> $GITHUB_STEP_SUMMARY
            echo "- **请检查构建日志**" >> $GITHUB_STEP_SUMMARY
          fi 