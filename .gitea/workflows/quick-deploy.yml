name: 快速部署到测试环境

on:
  workflow_run:
    workflows: ["增强版构建、创建制品和推送"]
    types: [completed]
    branches: [main, master]

env:
  # 只保留非敏感的Variables
  DEPLOY_PATH: ${{ vars.DEPLOY_PATH || '/opt/weizhi' }}

jobs:
  # 部署准备
  prepare-deploy:
    name: 准备部署
    runs-on: ubuntu-latest
    if: ${{ github.event.workflow_run.conclusion == 'success' || github.event_name == 'workflow_dispatch' }}
    outputs:
      build_version: ${{ steps.version.outputs.version }}
      services: ${{ steps.services.outputs.list }}
    steps:
      - name: 获取构建信息
        id: version
        run: |
          VERSION="$(date +%Y%m%d-%H%M%S)-${GITHUB_SHA:0:8}"
          echo "version=${VERSION}" >> $GITHUB_OUTPUT
          echo "构建版本: ${VERSION}"

      - name: 获取更新的服务
        id: services
        run: |
          # 这里可以从构建工作流获取服务信息
          # 暂时使用默认值
          echo "list=[\"web\",\"admin\",\"server-go\"]" >> $GITHUB_OUTPUT
          echo "更新的服务: web, admin, server-go"

  # 部署到测试环境
  deploy-staging:
    name: 部署到测试环境
    runs-on: ubuntu-latest
    needs: prepare-deploy
    environment: staging
    steps:
      - name: 检出代码
        uses: actions/checkout@v4

      - name: 设置SSH密钥
        uses: webfactory/ssh-agent@v0.8.0
        with:
          ssh-private-key: ${{ secrets.DEPLOY_KEY }}

      - name: 添加服务器到已知主机
        run: |
          ssh-keyscan -H ${{ secrets.DEPLOY_HOST }} >> ~/.ssh/known_hosts

      - name: 创建部署脚本
        run: |
          cat > deploy-staging.sh << 'EOF'
          #!/bin/bash
          set -e
          
          echo "=== 开始部署到测试环境 ==="
          echo "版本: ${{ needs.prepare-deploy.outputs.build_version }}"
          echo "服务: ${{ needs.prepare-deploy.outputs.services }}"
          
          # 设置环境变量
          export VERSION="${{ needs.prepare-deploy.outputs.build_version }}"
          export REGISTRY_URL="${{ secrets.REGISTRY_URL }}"
          export NAMESPACE="${{ secrets.NAMESPACE }}"
          
          # 进入部署目录
          cd ${{ env.DEPLOY_PATH }}
          
          # 备份当前配置
          if [ -f "docker-compose.prod.yml" ]; then
            cp docker-compose.prod.yml docker-compose.prod.yml.backup.$(date +%Y%m%d-%H%M%S)
          fi
          
          # 拉取最新镜像
          echo "拉取最新镜像..."
          docker-compose -f docker-compose.prod.yml pull
          
          # 停止旧服务
          echo "停止旧服务..."
          docker-compose -f docker-compose.prod.yml down
          
          # 启动新服务
          echo "启动新服务..."
          docker-compose -f docker-compose.prod.yml up -d
          
          # 等待服务启动
          echo "等待服务启动..."
          sleep 15
          
          # 检查服务状态
          echo "检查服务状态..."
          docker-compose -f docker-compose.prod.yml ps
          
          # 健康检查
          echo "执行健康检查..."
          if curl -f http://localhost:3000/health; then
            echo "✅ Web服务健康检查通过"
          else
            echo "❌ Web服务健康检查失败"
            exit 1
          fi
          
          if curl -f http://localhost:3001/health; then
            echo "✅ 后端服务健康检查通过"
          else
            echo "❌ 后端服务健康检查失败"
            exit 1
          fi
          
          echo "✅ 部署完成！"
          EOF
          
          chmod +x deploy-staging.sh

      - name: 执行部署
        run: |
          scp deploy-staging.sh ${{ secrets.DEPLOY_USER }}@${{ secrets.DEPLOY_HOST }}:/tmp/
          ssh ${{ secrets.DEPLOY_USER }}@${{ secrets.DEPLOY_HOST }} "bash /tmp/deploy-staging.sh"

      - name: 清理临时文件
        if: always()
        run: |
          ssh ${{ secrets.DEPLOY_USER }}@${{ secrets.DEPLOY_HOST }} "rm -f /tmp/deploy-staging.sh"

  # 部署验证
  verify-deploy:
    name: 验证部署
    runs-on: ubuntu-latest
    needs: [prepare-deploy, deploy-staging]
    if: always()
    steps:
      - name: 等待服务启动
        run: |
          echo "等待服务完全启动..."
          sleep 30

      - name: 验证Web服务
        run: |
          echo "验证Web服务..."
          if curl -f -s http://${{ secrets.DEPLOY_HOST }}:3000/health; then
            echo "✅ Web服务验证成功"
          else
            echo "❌ Web服务验证失败"
            exit 1
          fi

      - name: 验证后端服务
        run: |
          echo "验证后端服务..."
          if curl -f -s http://${{ secrets.DEPLOY_HOST }}:3001/health; then
            echo "✅ 后端服务验证成功"
          else
            echo "❌ 后端服务验证失败"
            exit 1
          fi

      - name: 验证管理后台
        run: |
          echo "验证管理后台..."
          if curl -f -s http://${{ secrets.DEPLOY_HOST }}:5173/; then
            echo "✅ 管理后台验证成功"
          else
            echo "❌ 管理后台验证失败"
            exit 1
          fi

  # 部署通知
  notify-deploy:
    name: 部署通知
    runs-on: ubuntu-latest
    needs: [prepare-deploy, deploy-staging, verify-deploy]
    if: always()
    steps:
      - name: 生成部署报告
        run: |
          echo "## 🚀 部署完成报告" >> $GITHUB_STEP_SUMMARY
          echo "- **版本**: ${{ needs.prepare-deploy.outputs.build_version }}" >> $GITHUB_STEP_SUMMARY
          echo "- **部署时间**: $(date -u +%Y-%m-%dT%H:%M:%SZ)" >> $GITHUB_STEP_SUMMARY
          echo "- **目标环境**: 测试环境" >> $GITHUB_STEP_SUMMARY
          echo "- **服务器**: ${{ secrets.DEPLOY_HOST }}" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          
          if [ "${{ needs.deploy-staging.result }}" = "success" ] && [ "${{ needs.verify-deploy.result }}" = "success" ]; then
            echo "### ✅ 部署状态" >> $GITHUB_STEP_SUMMARY
            echo "- **状态**: 成功" >> $GITHUB_STEP_SUMMARY
            echo "- **Web服务**: ✅ 正常" >> $GITHUB_STEP_SUMMARY
            echo "- **后端服务**: ✅ 正常" >> $GITHUB_STEP_SUMMARY
            echo "- **管理后台**: ✅ 正常" >> $GITHUB_STEP_SUMMARY
            echo "" >> $GITHUB_STEP_SUMMARY
            echo "### 🌐 访问地址" >> $GITHUB_STEP_SUMMARY
            echo "- **Web服务**: http://${{ secrets.DEPLOY_HOST }}:3000" >> $GITHUB_STEP_SUMMARY
            echo "- **管理后台**: http://${{ secrets.DEPLOY_HOST }}:5173" >> $GITHUB_STEP_SUMMARY
            echo "- **后端API**: http://${{ secrets.DEPLOY_HOST }}:3001" >> $GITHUB_STEP_SUMMARY
          else
            echo "### ❌ 部署状态" >> $GITHUB_STEP_SUMMARY
            echo "- **状态**: 失败" >> $GITHUB_STEP_SUMMARY
            echo "- **请检查部署日志**" >> $GITHUB_STEP_SUMMARY
          fi

      - name: 发送部署通知
        if: always()
        run: |
          echo "发送部署通知..."
          # 这里可以添加发送到钉钉、企业微信等的通知逻辑
          
          # 示例：发送到钉钉
          # if [ -n "${{ secrets.DINGTALK_WEBHOOK }}" ]; then
          #   if [ "${{ needs.deploy-staging.result }}" = "success" ] && [ "${{ needs.verify-deploy.result }}" = "success" ]; then
          #     message="✅ Weizhi项目测试环境部署成功\n版本: ${{ needs.prepare-deploy.outputs.build_version }}\n时间: $(date)"
          #   else
          #     message="❌ Weizhi项目测试环境部署失败\n版本: ${{ needs.prepare-deploy.outputs.build_version }}\n时间: $(date)"
          #   fi
          #   
          #   curl -H "Content-Type: application/json" \
          #        -X POST \
          #        -d "{\"text\":\"$message\"}" \
          #        "${{ secrets.DINGTALK_WEBHOOK }}"
          # fi 