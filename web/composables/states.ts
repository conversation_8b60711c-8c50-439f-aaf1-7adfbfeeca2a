import type { News, OurServerConfig, ProjectCase } from '~/composables/useApi';

// 轮播图配置状态
export const useSwiperState = () => useState<any[]>('swiperUrls', () => []);

// 服务配置状态
export const useOurServerState = () => {
  const state = useState<OurServerConfig[]>('ourServerUrl', () => []);

  // 返回按order字段排序的服务列表
  const sortedServices = computed(() => {
    return state.value.sort((a, b) => (a.order || 0) - (b.order || 0));
  });

  return {
    value: state.value,
    sorted: sortedServices
  };
};

// 友情链接状态
export const useFriendsLinkState = () => useState<any[]>('friendslink', () => []);

// 合作伙伴状态
export const usePartnersState = () => useState<any[]>('partners', () => []);

// 新闻状态
export const useNewsState = () => useState<News[]>('news', () => []);

// 零件平台状态
export const usePartPlatformState = () => useState<any[]>('partPlatform', () => []);

// 项目案例状态
export const useProjectCaseState = () => useState<ProjectCase[]>('projectCase', () => []);

// 初始化所有状态数据
export const useInitAppState = async () => {
  // const api = useApi();
  // const swiperUrls = useSwiperState();
  // const ourServerUrl = useOurServerState();
  // const news = useNewsState();
  // const projectCase = useProjectCaseState();

  try {
    // 初始化轮播图数据
    // if (swiperUrls.value.length === 0) {
    //   const { data: swipersResponse } = await api.getSwipers();
    //   if (swipersResponse.value?.data?.swipers) {
    //     swiperUrls.value = swipersResponse.value.data.swipers;
    //   }
    // }

    // // 初始化服务数据
    // if (ourServerUrl.value.length === 0) {
    //   const { data: configResponse } = await api.getOurServerConfig();
    //   if (configResponse.value?.data?.configs) {
    //     ourServerUrl.value = configResponse.value.data.configs;
    //   }
    // }

    // // 初始化新闻数据
    // if (news.value.length === 0) {
    //   const { data: newsResponse } = await api.getNews({ isHomePage: true });
    //   if (newsResponse.value?.data?.news) {
    //     news.value = newsResponse.value.data.news;
    //   }
    // }

    // // 初始化项目案例数据
    // if (projectCase.value.length === 0) {
    //   const { data: casesResponse } = await api.getProjectCases();
    //   if (casesResponse.value?.data?.cases) {
    //     projectCase.value = casesResponse.value.data.cases;
    //   }
    // }
  } catch (error) {
    console.error('初始化应用状态失败:', error);
  }
}; 