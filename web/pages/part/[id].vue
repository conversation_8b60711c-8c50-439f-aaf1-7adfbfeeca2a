<template>
  <div class="w-full h-full">
    <main class="w-full flex flex-col items-center">
      <img
        class="w-full h-auto"
        alt=""
        src="https://images-1305444037.cos.ap-nanjing.myqcloud.com/background/3.jpg"
      />
      <div class="w-11/12 sm:w-main-width flex justify-center">
        <div class="w-full flex flex-col items-center my-10">
          <div class="font-black text-4xl my-10">零件详情</div>
          <div v-if="partData" class="w-full">
            <div class="flex flex-col md:flex-row gap-8">
              <!-- 左侧图片 -->
              <div class="md:w-1/2">
                <div class="bg-white p-4 rounded-lg">
                  <img
                    :src="partData.url"
                    :alt="partData.name"
                    class="w-full h-auto object-contain"
                  />
                </div>
              </div>
              
              <!-- 右侧信息 -->
              <div class="md:w-1/2">
                <div class="bg-white p-6 rounded-lg">
                  <h1 class="text-2xl font-bold mb-4">{{ partData.name }}</h1>
                  <div class="space-y-4">
                    <div class="text-gray-700">
                      <h2 class="text-lg font-semibold mb-2">产品描述</h2>
                      <p class="whitespace-pre-wrap">{{ partData.description }}</p>
                    </div>
                    
                    <div class="text-gray-700">
                      <h2 class="text-lg font-semibold mb-2">技术参数</h2>
                      <div class="whitespace-pre-wrap">{{ partData.parameters }}</div>
                    </div>
                    
                    <div class="text-gray-700">
                      <h2 class="text-lg font-semibold mb-2">应用范围</h2>
                      <div class="whitespace-pre-wrap">{{ partData.applications }}</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div v-else class="text-center py-8">
            <el-empty description="未找到相关零件信息" />
          </div>
        </div>
      </div>
    </main>
  </div>
</template>

<script setup lang="ts">
import type { PartPlatform } from '@weishi/types'
import { onMounted, ref } from 'vue'
import { useRoute } from 'vue-router'
import { useApi } from '~/utils/api'

const route = useRoute()
const api = useApi()
const partData = ref<PartPlatform | null>(null)

const getPartDetail = async () => {
  const id = Number(route.params.id)
  const { data: partDetail } = await api.getPartPlatformDetail(id)
  if (partDetail) {
    partData.value = partDetail
  }
}

onMounted(() => {
  getPartDetail()
})

definePageMeta({
  layout: 'main'
})
</script>

<style lang="scss" scoped>
.img {
  overflow: hidden;
  background-position: 50% 50%;
  background-repeat: no-repeat;
  background-size: cover;
  background-image: url("https://images-1305444037.cos.ap-nanjing.myqcloud.com/our-service/part/ban.png");
}
</style> 