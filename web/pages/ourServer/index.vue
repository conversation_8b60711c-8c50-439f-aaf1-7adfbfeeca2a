<template>
  <div class="w-full h-full">
    <main class="w-full flex flex-col items-center main_images bg-contain bg-repeat">
      <img
        class="w-full h-auto"
        alt=""
        src="https://images-1305444037.cos.ap-nanjing.myqcloud.com/background/2.jpg"
      />
      <div class="xl:w-main-width w-11/12 flex justify-center mt-56 mb-20 relative">
        <div class="border border-solid" style="width: 90%; border-color: #b3b3b3">
          <div class="w-full flex justify-center pb-10" style="margin-top: 220px">
            <div class="text-left xl:block hidden" style="width: 30%">
              <div class="w-full flex justify-end">
                <img
                  style="width: 90%; height: auto; margin-top: 40%"
                  alt=""
                  src="https://images-1305444037.cos.ap-nanjing.myqcloud.com/our-service/design-and-development/%E6%A0%87%E9%A2%98.png"
                />
              </div>
              <div class="w-full text-right mt-4" style="font-size: 25px; font-weight: 500">
                我们的服务
              </div>
            </div>
            <div class="flex flex-col xl:w-8/12 w-11/12">
              <ul class="h-full box-border pl-10 flex flex-col justify-between">
                <li v-for="service in services" :key="service.id" class="relative">
                  <div
                    class="absolute w-3 h-3 border-2 border-main1-color"
                    style="left: -30px; top: 6px"
                  ></div>
                  <p class="font-semibold">{{ service.name }}</p>
                  <p>{{ service.description }}</p>
                </li>
              </ul>
            </div>
          </div>
        </div>
        <div
          class="absolute z-10 bg-white bg-opacity-80"
          style="top: -0.5px; width: 65%; height: 1.5px"
        ></div>
        <div
          class="absolute"
          style="z-index: 10; width: 50%; height: 360px; top: -180px"
        >
          <div class="w-full h-full relative">
            <div class="absolute box1_images w-full h-full" style="z-index: 12"></div>
            <div
              class="absolute"
              style="
                z-index: 11;
                height: 98%;
                width: 98%;
                background-color: #52bde1;
                bottom: -10px;
                right: -10px;
              "
            ></div>
          </div>
        </div>
      </div>
    </main>
  </div>
</template>

<script setup lang="ts">
import { useApi } from '~/utils/api'

const api = useApi()
const { data: services } = await api.getServices()

// 添加页面元数据
useHead({
  title: '我们的服务 - 江苏蔚之领域智能科技有限公司',
  meta: [
    {
      name: 'description',
      content: '蔚之领域提供全面的汽车行业服务，包括设计开发、试验验证、设备开发等专业解决方案。',
    },
  ],
})
</script>

<style lang="scss" scoped>
.main_images {
  background-image: url('https://images-1305444037.cos.ap-nanjing.myqcloud.com/our-service/design-and-development/bg1.png');
}

.box1_images {
  background-image: url('https://images-1305444037.cos.ap-nanjing.myqcloud.com/our-service/cover/1.jpg');
  background-repeat: no-repeat;
  background-position: center top;
  background-size: cover;
}
</style> 