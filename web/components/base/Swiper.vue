<script setup lang="ts">
import { onMounted } from 'vue'
import { useSwipers } from '~/composables/useWebData'

const { data: swiperUrls, fetchData, loading, error } = useSwipers()

onMounted(() => {
  fetchData()
})
</script>

<template>
  <div class="swiper-container">
    <div v-if="loading" class="loading-placeholder">
      <div class="loading-spinner"></div>
    </div>
    <div v-else-if="error" class="text-red-500">加载失败: {{ error }}</div>
    <ClientOnly>
      <swiper-container
        v-if="swiperUrls && swiperUrls.length > 0"
        class="swiper-main"
        :loop="swiperUrls.length > 1"
        :autoplay="swiperUrls.length > 1 ? { delay: 3000, disableOnInteraction: false, pauseOnMouseEnter: true } : false"
        :pagination="{ clickable: true, dynamicBullets: true }"
        :navigation="swiperUrls.length > 1"
        observer
        observe-parents
        update-on-window-resize
      >
        <swiper-slide v-for="(item, index) in swiperUrls" :key="item.id || index" class="swiper-slide-item">
          <img :src="item.url" class="swiper-img" alt="轮播图" />
        </swiper-slide>
      </swiper-container>
    </ClientOnly>
  </div>
</template>

<style scoped>
.swiper-container {
  width: 100%;
  position: relative;
  overflow: hidden;
}

.loading-placeholder {
  width: 100%;
  background: #f5f5f5;
  display: flex;
  justify-content: center;
  align-items: center;
}

.loading-spinner {
  width: 50px;
  height: 50px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg);}
  100% { transform: rotate(360deg);}
}

.swiper-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.swiper-main {
  width: 100%;
  height: 100%;
}

swiper-slide {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

::v-deep(.swiper-pagination-bullet) {
  background: #fff;
  opacity: 0.7;
}
::v-deep(.swiper-pagination-bullet-active) {
  background: #fff;
  opacity: 1;
}
::v-deep(.swiper-button-prev),
::v-deep(.swiper-button-next) {
  color: #fff;
  text-shadow: 0 0 3px rgba(0, 0, 0, 0.5);
  display: block !important;
  opacity: 1 !important;
  z-index: 10;
}
@media (max-width: 768px) {
  .loading-spinner {
    width: 30px;
    height: 30px;
  }
}
</style>