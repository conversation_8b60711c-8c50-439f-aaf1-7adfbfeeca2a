# 蔚之领域项目 Caddy 配置文件

# 全局配置
{
    # 邮箱用于 Let's Encrypt 证书申请
    email <EMAIL>
}

# www 域名重定向到主域名
www.{$DOMAIN:localhost} {
    redir https://{$DOMAIN:localhost}{uri} permanent
}

# 管理后台二级域名配置
admin.{$DOMAIN:localhost} {
    # 启用访问日志
    log {
        output file /var/log/caddy/admin-access.log
        format json
    }

    # 安全头设置
    header {
        X-Frame-Options "SAMEORIGIN"
        X-XSS-Protection "1; mode=block"
        X-Content-Type-Options "nosniff"
        Referrer-Policy "no-referrer-when-downgrade"
        Strict-Transport-Security "max-age=31536000; includeSubDomains"
        -Server
    }

    # API 路由代理到后端服务
    handle /api/* {
        reverse_proxy server:3001 {
            health_uri /api/health
            health_interval 30s
            health_timeout 10s
            header_up Host {host}
            header_up X-Real-IP {remote_host}
            header_up X-Forwarded-For {remote_host}
            header_up X-Forwarded-Proto {scheme}
        }
    }

    # 管理后台静态文件服务（根路径）
    handle {
        root * /opt/weishi/static/admin
        file_server

        # 设置正确的 MIME 类型
        @css {
            path *.css
        }
        header @css Content-Type "text/css; charset=utf-8"

        @js {
            path *.js
        }
        header @js Content-Type "application/javascript; charset=utf-8"

        # HTML 文件 - 不缓存，确保总是获取最新版本
        @html {
            path *.html /
        }
        header @html Cache-Control "no-cache, no-store, must-revalidate"
        header @html Pragma "no-cache"
        header @html Expires "0"
        header @html X-Content-Type-Options "nosniff"

        # 带版本号的静态资源 - 长期缓存（1年）
        @versioned_assets {
            path_regexp versioned ^/assets/.*-[a-zA-Z0-9_-]+\.(js|css|woff2?|ttf|eot|svg|png|jpg|jpeg|gif|webp|ico)$
        }
        header @versioned_assets Cache-Control "public, max-age=31536000, immutable"
        header @versioned_assets X-Content-Type-Options "nosniff"

        # 其他静态资源 - 中期缓存（1天）
        @other_assets {
            path /assets/*
            not path_regexp ^/assets/.*-[a-zA-Z0-9_-]+\.(js|css|woff2?|ttf|eot|svg|png|jpg|jpeg|gif|webp|ico)$
        }
        header @other_assets Cache-Control "public, max-age=86400"
        header @other_assets X-Content-Type-Options "nosniff"

        # 图标和 manifest 文件 - 短期缓存（1小时）
        @icons {
            path *.ico *.png *.svg favicon.ico logo*.svg
        }
        header @icons Cache-Control "public, max-age=3600"

        # SPA 路由支持 - 所有路径都返回 index.html
        try_files {path} /index.html
    }

    # 压缩
    encode gzip
}

# 主站点配置 - 自动支持 HTTP/HTTPS
{$DOMAIN:localhost} {
    # 启用访问日志
    log {
        output file /var/log/caddy/access.log
        format json
    }

    # 安全头设置
    header {
        X-Frame-Options "SAMEORIGIN"
        X-XSS-Protection "1; mode=block"
        X-Content-Type-Options "nosniff"
        Referrer-Policy "no-referrer-when-downgrade"
        Strict-Transport-Security "max-age=31536000; includeSubDomains"
        -Server
    }

    # API 路由代理到后端服务
    handle /api/* {
        reverse_proxy server:3001 {
            health_uri /api/health
            health_interval 30s
            health_timeout 10s
            header_up Host {host}
            header_up X-Real-IP {remote_host}
            header_up X-Forwarded-For {remote_host}
            header_up X-Forwarded-Proto {scheme}
        }
    }



    # 健康检查端点
    handle /health {
        respond "OK" 200
        header Content-Type "text/plain"
    }

    # 前端网站代理到 web 服务（默认路由）
    handle {
        # 静态资源缓存控制
        @static_files {
            path /_nuxt/* /favicon.ico *.css *.js *.png *.jpg *.jpeg *.gif *.svg *.webp *.woff *.woff2 *.ttf *.eot
        }
        header @static_files Cache-Control "public, max-age=31536000, immutable"

        # HTML 页面不缓存
        @html_pages {
            path / /about /services /cases /news /contact
            header Content-Type text/html*
        }
        header @html_pages Cache-Control "no-cache, no-store, must-revalidate"

        reverse_proxy web:3000 {
            health_uri /api/health
            health_interval 30s
            health_timeout 10s
            header_up Host {host}
            header_up X-Real-IP {remote_host}
            header_up X-Forwarded-For {remote_host}
            header_up X-Forwarded-Proto {scheme}
            header_up Connection {>Connection}
            header_up Upgrade {>Upgrade}
        }
    }

    # 压缩
    encode gzip
}

# 开发环境测试端点
:8080 {
    respond "Caddy is running! Environment: {$DOMAIN:localhost}" 200
    header Content-Type "text/plain"
} 
