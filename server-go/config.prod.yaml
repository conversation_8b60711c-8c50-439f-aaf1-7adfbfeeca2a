app:
  name: "weizhi-server"
  port: "3001"
  mode: "release"  # gin 的 release 模式

database:
  host: "mysql"
  port: "3306"
  username: "weizhi"
  password: "Ydb3344%"
  database: "weizhi"
  charset: "utf8mb4"
  # 连接池配置
  max_idle_conns: 10
  max_open_conns: 100
  conn_max_lifetime: 3600  # 秒

jwt:
  secret: "weizhi-jwt-secret-key-production-change-this"
  expire_time: 28800 # 8小时

log:
  level: "info"
  path: "./logs"
  filename: "app.log"
  max_size: 100      # MB，单个日志文件最大大小
  max_backups: 10    # 保留的旧日志文件数量
  max_age: 30        # 保留天数
  compress: true     # 压缩旧日志文件
  console: false     # 生产环境不输出到控制台
  format: "json"     # 日志格式：json 或 text
  
  # 不同级别的日志可以分别配置
  error_file: "error.log"    # 错误日志单独文件
  access_file: "access.log"  # 访问日志文件

cos:
  secret_id: ""
  secret_key: ""
  region: "ap-nanjing"
  bucket: ""
  domain: ""

file:
  max_file_size: 52428800  # 50MB
  allowed_file_types:
    - "image/jpeg"
    - "image/png"
    - "image/gif"
    - "image/webp"
    - "application/pdf"
    - "text/plain"

# 服务器配置
server:
  read_timeout: 60s
  write_timeout: 60s
  idle_timeout: 60s
  max_header_bytes: 1048576  # 1MB

# 中间件配置
middleware:
  cors:
    allow_origins: ["*"]
    allow_methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"]
    allow_headers: ["*"]
    expose_headers: ["*"]
    allow_credentials: true
    max_age: 86400
  
  rate_limit:
    enabled: true
    requests_per_minute: 100
    
  gzip:
    enabled: true
    level: 6
