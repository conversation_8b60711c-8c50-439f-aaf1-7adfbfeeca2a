package config

import (
	"log"
	"os"
	"strconv"
	"strings"

	"github.com/spf13/viper"
)

type Config struct {
	App        AppConfig        `mapstructure:"app"`
	Database   DatabaseConfig   `mapstructure:"database"`
	JWT        JWTConfig        `mapstructure:"jwt"`
	Log        LogConfig        `mapstructure:"log"`
	COS        COSConfig        `mapstructure:"cos"`
	File       FileConfig       `mapstructure:"file"`
	Admin      AdminConfig      `mapstructure:"admin"`
	DataImport DataImportConfig `mapstructure:"data_import"`
}

type AppConfig struct {
	Name string `mapstructure:"name"`
	Port string `mapstructure:"port"`
	Mode string `mapstructure:"mode"`
}

type DatabaseConfig struct {
	Host     string `mapstructure:"host"`
	Port     string `mapstructure:"port"`
	Username string `mapstructure:"username"`
	Password string `mapstructure:"password"`
	Database string `mapstructure:"database"`
	Charset  string `mapstructure:"charset"`
}

type JWTConfig struct {
	Secret     string `mapstructure:"secret"`
	ExpireTime int    `mapstructure:"expire_time"`
}

type LogConfig struct {
	Level string `mapstructure:"level"`
	Path  string `mapstructure:"path"`
}

type COSConfig struct {
	SecretID  string `mapstructure:"secret_id"`
	SecretKey string `mapstructure:"secret_key"`
	Region    string `mapstructure:"region"`
	Bucket    string `mapstructure:"bucket"`
	Domain    string `mapstructure:"domain"`
}

type FileConfig struct {
	MaxFileSize      int64    `mapstructure:"max_file_size"`
	AllowedFileTypes []string `mapstructure:"allowed_file_types"`
}

type AdminConfig struct {
	Username     string                `mapstructure:"username"`
	Password     string                `mapstructure:"password"`
	InitPassword bool                  `mapstructure:"init_password"`
	Policy       AdminPasswordPolicy   `mapstructure:"password_policy"`
	Security     AdminSecuritySettings `mapstructure:"security"`
}

type AdminPasswordPolicy struct {
	MinLength        int  `mapstructure:"min_length"`
	RequireUppercase bool `mapstructure:"require_uppercase"`
	RequireLowercase bool `mapstructure:"require_lowercase"`
	RequireNumbers   bool `mapstructure:"require_numbers"`
	RequireSymbols   bool `mapstructure:"require_symbols"`
}

type AdminSecuritySettings struct {
	ForcePasswordChange bool `mapstructure:"force_password_change"`
	PasswordExpireDays  int  `mapstructure:"password_expire_days"`
	MaxLoginAttempts    int  `mapstructure:"max_login_attempts"`
	LockoutDuration     int  `mapstructure:"lockout_duration"`
}

type DataImportConfig struct {
	ImportSeedData  bool `mapstructure:"import_seed_data"`
	ImportAdminData bool `mapstructure:"import_admin_data"`
}

func New() *Config {
	cfg := &Config{}

	// 检查是否为生产环境
	isProduction := os.Getenv("APP_MODE") == "production"

	// 设置环境变量前缀
	viper.SetEnvPrefix("WEISHI")
	viper.AutomaticEnv()

	// 设置默认值
	setDefaults()

	// 根据环境决定是否读取配置文件
	if !isProduction {
		// 开发环境：读取配置文件
		viper.SetConfigName("config")
		viper.SetConfigType("yaml")
		viper.AddConfigPath("./configs")
		viper.AddConfigPath(".")

		if err := viper.ReadInConfig(); err != nil {
			log.Printf("Config file not found, using defaults and environment variables: %v", err)
		} else {
			log.Printf("Using config file: %s", viper.ConfigFileUsed())
		}
	} else {
		// 生产环境：完全使用环境变量
		log.Printf("Production mode: using environment variables only")
	}

	// 解析配置到结构体
	if err := viper.Unmarshal(cfg); err != nil {
		log.Fatal("Failed to unmarshal config:", err)
	}

	return cfg
}

func setDefaults() {
	isProduction := os.Getenv("APP_MODE") == "production"

	// App 默认配置
	viper.SetDefault("app.name", "weishi-server")
	viper.SetDefault("app.port", getEnvOrDefault("APP_PORT", "3001"))
	viper.SetDefault("app.mode", getEnvOrDefault("APP_MODE", "debug"))

	// Database 默认配置
	if isProduction {
		// 生产环境：强制使用环境变量
		viper.Set("database.host", getEnvOrDefault("DB_HOST", "mysql"))
		viper.Set("database.port", getEnvOrDefault("DB_PORT", "3306"))
		viper.Set("database.username", getEnvOrDefault("DB_USERNAME", "weizhi"))
		viper.Set("database.password", getEnvOrDefault("DB_PASSWORD", ""))
		viper.Set("database.database", getEnvOrDefault("DB_DATABASE", "weizhi"))
		viper.Set("database.charset", "utf8mb4")
	} else {
		// 开发环境：使用默认值，允许配置文件覆盖
		viper.SetDefault("database.host", getEnvOrDefault("DB_HOST", "localhost"))
		viper.SetDefault("database.port", getEnvOrDefault("DB_PORT", "3306"))
		viper.SetDefault("database.username", getEnvOrDefault("DB_USERNAME", "root"))
		viper.SetDefault("database.password", getEnvOrDefault("DB_PASSWORD", "Ydb3344%"))
		viper.SetDefault("database.database", getEnvOrDefault("DB_DATABASE", "weizhi"))
		viper.SetDefault("database.charset", "utf8mb4")
	}

	// JWT 默认配置
	if isProduction {
		viper.Set("jwt.secret", getEnvOrDefault("JWT_SECRET", "weishi-jwt-secret-key"))
		viper.Set("jwt.expire_time", getEnvOrDefaultInt("JWT_EXPIRE_TIME", 28800))
	} else {
		viper.SetDefault("jwt.secret", getEnvOrDefault("JWT_SECRET", "weishi-jwt-secret-key"))
		viper.SetDefault("jwt.expire_time", getEnvOrDefaultInt("JWT_EXPIRE_TIME", 28800))
	}

	// Log 默认配置
	if isProduction {
		viper.Set("log.level", getEnvOrDefault("LOG_LEVEL", "info"))
		viper.Set("log.path", getEnvOrDefault("LOG_PATH", "./logs"))
	} else {
		viper.SetDefault("log.level", getEnvOrDefault("LOG_LEVEL", "info"))
		viper.SetDefault("log.path", getEnvOrDefault("LOG_PATH", "./logs"))
	}

	// COS 默认配置
	viper.SetDefault("cos.secret_id", getEnvOrDefault("COS_SECRET_ID", ""))
	viper.SetDefault("cos.secret_key", getEnvOrDefault("COS_SECRET_KEY", ""))
	viper.SetDefault("cos.region", getEnvOrDefault("COS_REGION", "ap-nanjing"))
	viper.SetDefault("cos.bucket", getEnvOrDefault("COS_BUCKET", ""))
	viper.SetDefault("cos.domain", getEnvOrDefault("COS_DOMAIN", ""))

	// File 默认配置
	viper.SetDefault("file.max_file_size", getEnvOrDefaultInt64("MAX_FILE_SIZE", 52428800)) // 50MB
	viper.SetDefault("file.allowed_file_types", getEnvOrDefaultStringSlice("ALLOWED_FILE_TYPES", []string{
		"image/jpeg", "image/png", "image/gif", "image/webp",
		"application/pdf", "text/plain",
	}))

	// Admin 默认配置
	viper.SetDefault("admin.username", getEnvOrDefault("ADMIN_USERNAME", "admin"))
	viper.SetDefault("admin.password", getEnvOrDefault("ADMIN_PASSWORD", "admin123"))
	viper.SetDefault("admin.init_password", getEnvOrDefaultBool("ADMIN_INIT_PASSWORD", true))
	viper.SetDefault("admin.password_policy.min_length", 8)
	viper.SetDefault("admin.password_policy.require_uppercase", true)
	viper.SetDefault("admin.password_policy.require_lowercase", true)
	viper.SetDefault("admin.password_policy.require_numbers", true)
	viper.SetDefault("admin.password_policy.require_symbols", false)
	viper.SetDefault("admin.security.force_password_change", true)
	viper.SetDefault("admin.security.password_expire_days", 90)
	viper.SetDefault("admin.security.max_login_attempts", 5)
	viper.SetDefault("admin.security.lockout_duration", 30)
}

func getEnvOrDefault(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

func getEnvOrDefaultInt(key string, defaultValue int) int {
	if value := os.Getenv(key); value != "" {
		if intValue, err := strconv.Atoi(value); err == nil {
			return intValue
		}
	}
	return defaultValue
}

func getEnvOrDefaultInt64(key string, defaultValue int64) int64 {
	if value := os.Getenv(key); value != "" {
		if intValue, err := strconv.ParseInt(value, 10, 64); err == nil {
			return intValue
		}
	}
	return defaultValue
}

func getEnvOrDefaultStringSlice(key string, defaultValue []string) []string {
	if value := os.Getenv(key); value != "" {
		return strings.Split(value, ",")
	}
	return defaultValue
}

func getEnvOrDefaultBool(key string, defaultValue bool) bool {
	if value := os.Getenv(key); value != "" {
		if boolValue, err := strconv.ParseBool(value); err == nil {
			return boolValue
		}
	}
	return defaultValue
}
