package model

// Swiper 轮播图模型（优化后）
type Swiper struct {
	BaseModel
	URL   string `gorm:"type:varchar(255);not null" json:"url"`   // 图片URL
	Title string `gorm:"type:varchar(255);not null" json:"title"` // 图片标题（用于alt属性）
	Order int    `gorm:"default:0" json:"order"`                  // 显示顺序
}

// TableName 自定义表名
func (Swiper) TableName() string {
	return "swipers"
}

// News 新闻模型
type News struct {
	BaseModel
	Title      string  `gorm:"type:varchar(255);not null" json:"title"`
	Content    string  `gorm:"type:text;not null" json:"content"`
	Image      *string `gorm:"type:varchar(255)" json:"image"`
	IsHomePage bool    `gorm:"default:false;column:is_home_page" json:"isHomePage"`
}

// Service 服务模型（优化后，保留排序功能）
type Service struct {
	BaseModel
	Image string `gorm:"type:varchar(255);not null" json:"image"` // 服务背景图片
	Order int    `gorm:"default:0" json:"order"`                  // 显示顺序，数字越小越靠前
}

// TableName 自定义表名
func (Service) TableName() string {
	return "our_services"
}

// ProjectCase 项目案例模型（优化后）
type ProjectCase struct {
	BaseModel
	URL string `gorm:"type:varchar(255);not null" json:"url"` // 案例图片URL
}

// Partner 合作伙伴模型
type Partner struct {
	BaseModel
	Name string `gorm:"type:varchar(255);not null" json:"name"`
	Logo string `gorm:"type:varchar(255);not null" json:"logo"`
}

// FriendLink 友情链接模型
type FriendLink struct {
	BaseModel
	Name  string `gorm:"type:varchar(255);not null" json:"name"`
	URL   string `gorm:"type:varchar(255);not null" json:"url"`
	Order int    `gorm:"default:0" json:"order"`
}

// TableName 自定义表名
func (FriendLink) TableName() string {
	return "friend_links"
}

// Recruitment 招聘信息模型（优化后）
type Recruitment struct {
	BaseModel
	Name        string  `gorm:"type:varchar(255);not null" json:"name"`       // 姓名/职位名称
	Location    string  `gorm:"type:varchar(255);not null" json:"location"`   // 工作地点
	Content     string  `gorm:"type:text;not null" json:"content"`            // 招聘内容
	Position    string  `gorm:"type:varchar(255);not null" json:"position"`   // 职位
	Department  string  `gorm:"type:varchar(255);not null" json:"department"` // 部门
	Description string  `gorm:"type:text;not null" json:"description"`        // 职位描述
	Requirement *string `gorm:"type:text" json:"requirement"`                 // 职位要求（可选）
}

// PartPlatform 平台模型（优化后）
type PartPlatform struct {
	BaseModel
	Name         string `gorm:"type:varchar(255);not null" json:"name"` // 平台名称
	URL          string `gorm:"type:varchar(255);not null" json:"url"`  // 平台图片URL
	Description  string `gorm:"type:text;not null" json:"description"`  // 平台描述
	Parameters   string `gorm:"type:text;not null" json:"parameters"`   // 技术参数
	Applications string `gorm:"type:text;not null" json:"applications"` // 应用场景
}

// TableName 自定义表名
func (PartPlatform) TableName() string {
	return "part_platform"
}

// FileUpload 文件上传模型
type FileUpload struct {
	BaseModel
	OriginalName string `gorm:"type:varchar(255);not null" json:"originalName"`  // 原始文件名
	FileName     string `gorm:"type:varchar(255);not null" json:"fileName"`      // 存储文件名
	FilePath     string `gorm:"type:varchar(500);not null" json:"filePath"`      // 文件路径
	FileSize     int64  `gorm:"not null" json:"fileSize"`                        // 文件大小（字节）
	MimeType     string `gorm:"type:varchar(100);not null" json:"mimeType"`      // 文件类型
	BucketName   string `gorm:"type:varchar(100);not null" json:"bucketName"`    // 存储桶名称
	CosURL       string `gorm:"type:varchar(500);not null" json:"cosUrl"`        // COS访问URL
	UploadedBy   uint   `gorm:"not null" json:"uploadedBy"`                      // 上传用户ID
	Module       string `gorm:"type:varchar(50);not null" json:"module"`         // 所属模块
	Status       string `gorm:"type:varchar(20);default:'active'" json:"status"` // 文件状态
}

// TableName 自定义表名
func (FileUpload) TableName() string {
	return "file_uploads"
}
