package repository

import (
	"weishi-server/internal/model"

	"gorm.io/gorm"
)

type ContentRepository interface {
	// 新闻相关方法
	GetAllNews() ([]model.News, error)
	GetHomePageNews() ([]model.News, error)
	GetNewsByID(id uint) (*model.News, error)
	CreateNews(news *model.News) error
	UpdateNews(news *model.News) error
	DeleteNews(id uint) error
	SetNewsHomePage(id uint, isHomePage bool) error

	// 服务相关方法
	GetAllServices() ([]model.Service, error)
	GetServiceByID(id uint) (*model.Service, error)
	CreateService(service *model.Service) error
	UpdateService(service *model.Service) error
	DeleteService(id uint) error
	GetServiceTypes() ([]string, error)

	// 轮播图相关方法
	GetAllSwipers() ([]model.Swiper, error)
	GetSwiperByID(id uint) (*model.Swiper, error)
	CreateSwiper(swiper *model.Swiper) error
	UpdateSwiper(swiper *model.Swiper) error
	DeleteSwiper(id uint) error

	// 合作伙伴相关方法
	GetAllPartners() ([]model.Partner, error)
	GetPartnerByID(id uint) (*model.Partner, error)
	CreatePartner(partner *model.Partner) error
	UpdatePartner(partner *model.Partner) error
	DeletePartner(id uint) error
	// 友情链接相关方法
	GetAllFriendLinks() ([]model.FriendLink, error)
	GetFriendLinkByID(id uint) (*model.FriendLink, error)
	CreateFriendLink(friendLink *model.FriendLink) error
	UpdateFriendLink(friendLink *model.FriendLink) error
	DeleteFriendLink(id uint) error

	// 招聘信息相关方法
	GetAllRecruitments() ([]model.Recruitment, error)
	GetRecruitmentByID(id uint) (*model.Recruitment, error)
	CreateRecruitment(recruitment *model.Recruitment) error
	UpdateRecruitment(recruitment *model.Recruitment) error
	DeleteRecruitment(id uint) error

	// 平台相关方法
	GetAllPartPlatforms() ([]model.PartPlatform, error)
	GetPartPlatformByID(id uint) (*model.PartPlatform, error)
	CreatePartPlatform(platform *model.PartPlatform) error
	UpdatePartPlatform(platform *model.PartPlatform) error
	DeletePartPlatform(id uint) error

	// 项目案例相关方法
	GetAllProjectCases() ([]model.ProjectCase, error)
	GetProjectCaseByID(id uint) (*model.ProjectCase, error)
	CreateProjectCase(projectCase *model.ProjectCase) error
	UpdateProjectCase(projectCase *model.ProjectCase) error
	DeleteProjectCase(id uint) error

	// 文件上传相关方法
	GetFileUploadByID(id uint) (*model.FileUpload, error)
	CreateFileUpload(fileUpload *model.FileUpload) error
	UpdateFileUpload(fileUpload *model.FileUpload) error
	DeleteFileUpload(id uint) error
	GetFileUploadsByModule(module string) ([]model.FileUpload, error)
	GetFileUploadsByUser(userID uint) ([]model.FileUpload, error)
}

type contentRepository struct {
	db *gorm.DB
}

func NewContentRepository(db *gorm.DB) ContentRepository {
	return &contentRepository{
		db: db,
	}
}

func (r *contentRepository) GetAllNews() ([]model.News, error) {
	var news []model.News
	err := r.db.Find(&news).Error
	return news, err
}

func (r *contentRepository) GetHomePageNews() ([]model.News, error) {
	var news []model.News
	err := r.db.Where("is_home_page = ?", true).Find(&news).Error
	return news, err
}

func (r *contentRepository) GetNewsByID(id uint) (*model.News, error) {
	var news model.News
	err := r.db.First(&news, id).Error
	return &news, err
}

func (r *contentRepository) CreateNews(news *model.News) error {
	return r.db.Create(news).Error
}

func (r *contentRepository) UpdateNews(news *model.News) error {
	return r.db.Save(news).Error
}

func (r *contentRepository) DeleteNews(id uint) error {
	return r.db.Delete(&model.News{}, id).Error
}

func (r *contentRepository) SetNewsHomePage(id uint, isHomePage bool) error {
	return r.db.Model(&model.News{}).Where("id = ?", id).Update("is_home_page", isHomePage).Error
}

func (r *contentRepository) GetAllServices() ([]model.Service, error) {
	var services []model.Service
	err := r.db.Order("`order` ASC").Find(&services).Error
	return services, err
}

func (r *contentRepository) GetServiceByID(id uint) (*model.Service, error) {
	var service model.Service
	err := r.db.First(&service, id).Error
	if err != nil {
		return nil, err
	}
	return &service, nil
}

func (r *contentRepository) CreateService(service *model.Service) error {
	return r.db.Create(service).Error
}

func (r *contentRepository) UpdateService(service *model.Service) error {
	return r.db.Save(service).Error
}

func (r *contentRepository) DeleteService(id uint) error {
	return r.db.Delete(&model.Service{}, id).Error
}

func (r *contentRepository) GetServiceTypes() ([]string, error) {
	var types []string
	err := r.db.Model(&model.Service{}).Distinct("type").Pluck("type", &types).Error
	return types, err
}

func (r *contentRepository) GetAllSwipers() ([]model.Swiper, error) {
	var swipers []model.Swiper
	err := r.db.Order("`order` ASC").Find(&swipers).Error
	return swipers, err
}

func (r *contentRepository) GetSwiperByID(id uint) (*model.Swiper, error) {
	var swiper model.Swiper
	err := r.db.First(&swiper, id).Error
	if err != nil {
		return nil, err
	}
	return &swiper, nil
}

func (r *contentRepository) CreateSwiper(swiper *model.Swiper) error {
	return r.db.Create(swiper).Error
}

func (r *contentRepository) UpdateSwiper(swiper *model.Swiper) error {
	return r.db.Save(swiper).Error
}

func (r *contentRepository) DeleteSwiper(id uint) error {
	return r.db.Delete(&model.Swiper{}, id).Error
}

func (r *contentRepository) GetAllPartners() ([]model.Partner, error) {
	var partners []model.Partner
	err := r.db.Find(&partners).Error
	return partners, err
}

func (r *contentRepository) GetPartnerByID(id uint) (*model.Partner, error) {
	var partner model.Partner
	err := r.db.First(&partner, id).Error
	if err != nil {
		return nil, err
	}
	return &partner, nil
}

func (r *contentRepository) CreatePartner(partner *model.Partner) error {
	return r.db.Create(partner).Error
}

func (r *contentRepository) UpdatePartner(partner *model.Partner) error {
	return r.db.Save(partner).Error
}

func (r *contentRepository) DeletePartner(id uint) error {
	return r.db.Delete(&model.Partner{}, id).Error
}

func (r *contentRepository) GetAllFriendLinks() ([]model.FriendLink, error) {
	var links []model.FriendLink
	err := r.db.Order("`order` ASC").Find(&links).Error
	return links, err
}

func (r *contentRepository) GetFriendLinkByID(id uint) (*model.FriendLink, error) {
	var friendLink model.FriendLink
	err := r.db.First(&friendLink, id).Error
	if err != nil {
		return nil, err
	}
	return &friendLink, nil
}

func (r *contentRepository) CreateFriendLink(friendLink *model.FriendLink) error {
	return r.db.Create(friendLink).Error
}

func (r *contentRepository) UpdateFriendLink(friendLink *model.FriendLink) error {
	return r.db.Save(friendLink).Error
}

func (r *contentRepository) DeleteFriendLink(id uint) error {
	return r.db.Delete(&model.FriendLink{}, id).Error
}

func (r *contentRepository) GetAllRecruitments() ([]model.Recruitment, error) {
	var recruitments []model.Recruitment
	err := r.db.Order("`order` ASC").Find(&recruitments).Error
	return recruitments, err
}

func (r *contentRepository) GetRecruitmentByID(id uint) (*model.Recruitment, error) {
	var recruitment model.Recruitment
	err := r.db.First(&recruitment, id).Error
	if err != nil {
		return nil, err
	}
	return &recruitment, nil
}

func (r *contentRepository) CreateRecruitment(recruitment *model.Recruitment) error {
	return r.db.Create(recruitment).Error
}

func (r *contentRepository) UpdateRecruitment(recruitment *model.Recruitment) error {
	return r.db.Save(recruitment).Error
}

func (r *contentRepository) DeleteRecruitment(id uint) error {
	return r.db.Delete(&model.Recruitment{}, id).Error
}

func (r *contentRepository) GetAllPartPlatforms() ([]model.PartPlatform, error) {
	var platforms []model.PartPlatform
	err := r.db.Order("`order` ASC").Find(&platforms).Error
	return platforms, err
}

func (r *contentRepository) GetPartPlatformByID(id uint) (*model.PartPlatform, error) {
	var platform model.PartPlatform
	err := r.db.First(&platform, id).Error
	if err != nil {
		return nil, err
	}
	return &platform, nil
}

func (r *contentRepository) CreatePartPlatform(platform *model.PartPlatform) error {
	return r.db.Create(platform).Error
}

func (r *contentRepository) UpdatePartPlatform(platform *model.PartPlatform) error {
	return r.db.Save(platform).Error
}

func (r *contentRepository) DeletePartPlatform(id uint) error {
	return r.db.Delete(&model.PartPlatform{}, id).Error
}

func (r *contentRepository) GetAllProjectCases() ([]model.ProjectCase, error) {
	var cases []model.ProjectCase
	err := r.db.Order("sort ASC").Find(&cases).Error
	return cases, err
}

func (r *contentRepository) GetProjectCaseByID(id uint) (*model.ProjectCase, error) {
	var projectCase model.ProjectCase
	err := r.db.First(&projectCase, id).Error
	if err != nil {
		return nil, err
	}
	return &projectCase, nil
}

func (r *contentRepository) CreateProjectCase(projectCase *model.ProjectCase) error {
	return r.db.Create(projectCase).Error
}

func (r *contentRepository) UpdateProjectCase(projectCase *model.ProjectCase) error {
	return r.db.Save(projectCase).Error
}

func (r *contentRepository) DeleteProjectCase(id uint) error {
	return r.db.Delete(&model.ProjectCase{}, id).Error
}

// 文件上传相关方法实现
func (r *contentRepository) GetFileUploadByID(id uint) (*model.FileUpload, error) {
	var fileUpload model.FileUpload
	err := r.db.First(&fileUpload, id).Error
	if err != nil {
		return nil, err
	}
	return &fileUpload, nil
}

func (r *contentRepository) CreateFileUpload(fileUpload *model.FileUpload) error {
	return r.db.Create(fileUpload).Error
}

func (r *contentRepository) UpdateFileUpload(fileUpload *model.FileUpload) error {
	return r.db.Save(fileUpload).Error
}

func (r *contentRepository) DeleteFileUpload(id uint) error {
	return r.db.Delete(&model.FileUpload{}, id).Error
}

func (r *contentRepository) GetFileUploadsByModule(module string) ([]model.FileUpload, error) {
	var fileUploads []model.FileUpload
	err := r.db.Where("module = ? AND status = ?", module, "active").
		Order("created_at DESC").Find(&fileUploads).Error
	return fileUploads, err
}

func (r *contentRepository) GetFileUploadsByUser(userID uint) ([]model.FileUpload, error) {
	var fileUploads []model.FileUpload
	err := r.db.Where("uploaded_by = ? AND status = ?", userID, "active").
		Order("created_at DESC").Find(&fileUploads).Error
	return fileUploads, err
}
