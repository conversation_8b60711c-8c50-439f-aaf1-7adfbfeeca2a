package handler

import (
	"log"
	"strconv"
	"strings"
	"weishi-server/internal/model"
	"weishi-server/internal/service"
	"weishi-server/pkg/response"

	"github.com/gin-gonic/gin"
)

// ContentService 接口定义（临时）
type ContentService interface {
	GetAllNews() (interface{}, error)
	GetHomePageNews() (interface{}, error)
	GetNewsByID(id uint) (interface{}, error)
	GetAllServices() (interface{}, error)
	GetAllSwipers() ([]model.Swiper, error)
	GetSwiperByID(id uint) (*model.Swiper, error)
	CreateSwiper(swiper *model.Swiper) error
	UpdateSwiper(id uint, swiper *model.Swiper) error
	DeleteSwiper(id uint) error
	GetAllPartners() (interface{}, error)
	GetAllFriendLinks() (interface{}, error)
	GetFriendLinkByID(id uint) (*model.FriendLink, error)
	CreateFriendLink(friendLink *model.FriendLink) error
	UpdateFriendLink(id uint, friendLink *model.FriendLink) error
	DeleteFriendLink(id uint) error

	// 招聘信息相关方法
	GetAllRecruitments() (interface{}, error)
	GetRecruitmentByID(id uint) (*model.Recruitment, error)
	CreateRecruitment(recruitment *model.Recruitment) error
	UpdateRecruitment(id uint, recruitment *model.Recruitment) error
	DeleteRecruitment(id uint) error

	// 平台相关方法
	GetAllPartPlatforms() (interface{}, error)
	GetPartPlatformByID(id uint) (*model.PartPlatform, error)
	CreatePartPlatform(platform *model.PartPlatform) error
	UpdatePartPlatform(id uint, platform *model.PartPlatform) error
	DeletePartPlatform(id uint) error

	GetAllProjectCases() (interface{}, error)
}

type ContentHandler struct {
	contentService service.ContentService
}

func NewContentHandler(contentService service.ContentService) *ContentHandler {
	return &ContentHandler{
		contentService: contentService,
	}
}

// GetNews 获取新闻列表（支持分页）
func (h *ContentHandler) GetNews(c *gin.Context) {
	// 获取分页参数
	pageStr := c.DefaultQuery("page", "1")
	sizeStr := c.DefaultQuery("size", "10")

	page, err := strconv.Atoi(pageStr)
	if err != nil || page < 1 {
		page = 1
	}

	size, err := strconv.Atoi(sizeStr)
	if err != nil || size < 1 || size > 100 {
		size = 10
	}

	// 获取搜索参数
	keyword := c.Query("keyword")
	isHomePageStr := c.Query("isHomePage")

	// 获取所有新闻
	allNews, err := h.contentService.GetAllNews()
	if err != nil {
		response.InternalServerError(c, "获取新闻列表失败")
		return
	}

	// 过滤新闻
	var filteredNews []model.News
	for _, news := range allNews {
		// 关键词过滤
		if keyword != "" {
			if !strings.Contains(news.Title, keyword) {
				continue
			}
		}

		// 首页显示过滤
		if isHomePageStr != "" {
			if isHomePageStr == "true" && !news.IsHomePage {
				continue
			}
			if isHomePageStr == "false" && news.IsHomePage {
				continue
			}
		}

		filteredNews = append(filteredNews, news)
	}

	// 计算总数
	total := len(filteredNews)

	// 计算分页
	offset := (page - 1) * size
	end := offset + size

	// 处理边界情况
	if offset >= total {
		result := map[string]interface{}{
			"list":      []model.News{},
			"total":     total,
			"page":      page,
			"page_size": size,
		}
		response.Success(c, result)
		return
	}

	if end > total {
		end = total
	}

	// 分页数据
	pagedNews := filteredNews[offset:end]

	// 构造响应数据
	result := map[string]interface{}{
		"list":      pagedNews,
		"total":     total,
		"page":      page,
		"page_size": size,
	}

	response.Success(c, result)
}

// GetHomePageNews 获取首页新闻
func (h *ContentHandler) GetHomePageNews(c *gin.Context) {
	news, err := h.contentService.GetHomePageNews()
	if err != nil {
		response.InternalServerError(c, "获取首页新闻失败")
		return
	}
	response.Success(c, news)
}

// GetNewsDetail 获取新闻详情
func (h *ContentHandler) GetNewsDetail(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "无效的新闻ID")
		return
	}

	news, err := h.contentService.GetNewsByID(uint(id))
	if err != nil {
		response.InternalServerError(c, "获取新闻详情失败")
		return
	}
	response.Success(c, news)
}

// CreateNews 创建新闻
func (h *ContentHandler) CreateNews(c *gin.Context) {
	var news model.News
	if err := c.ShouldBindJSON(&news); err != nil {
		response.BadRequest(c, "请求参数错误: "+err.Error())
		return
	}

	if err := h.contentService.CreateNews(&news); err != nil {
		response.InternalServerError(c, "创建新闻失败")
		return
	}

	response.SuccessWithMsg(c, news, "创建新闻成功")
}

// UpdateNews 更新新闻
func (h *ContentHandler) UpdateNews(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "无效的新闻ID")
		return
	}

	var news model.News
	if err := c.ShouldBindJSON(&news); err != nil {
		response.BadRequest(c, "请求参数错误: "+err.Error())
		return
	}

	if err := h.contentService.UpdateNews(uint(id), &news); err != nil {
		response.InternalServerError(c, "更新新闻失败")
		return
	}

	response.SuccessWithMsg(c, news, "更新新闻成功")
}

// DeleteNews 删除新闻
func (h *ContentHandler) DeleteNews(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "无效的新闻ID")
		return
	}

	if err := h.contentService.DeleteNews(uint(id)); err != nil {
		response.InternalServerError(c, "删除新闻失败")
		return
	}

	response.SuccessWithMsg(c, nil, "删除新闻成功")
}

// SetNewsHomePage 设置新闻首页显示
func (h *ContentHandler) SetNewsHomePage(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "无效的新闻ID")
		return
	}

	var req struct {
		IsHomePage bool `json:"isHomePage"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "请求参数错误: "+err.Error())
		return
	}

	if err := h.contentService.SetNewsHomePage(uint(id), req.IsHomePage); err != nil {
		response.InternalServerError(c, "设置首页显示失败")
		return
	}

	response.SuccessWithMsg(c, nil, "设置成功")
}

// GetServices 获取服务列表（支持分页）
func (h *ContentHandler) GetServices(c *gin.Context) {
	// 获取分页参数
	pageStr := c.DefaultQuery("page", "1")
	sizeStr := c.DefaultQuery("size", "10")

	page, err := strconv.Atoi(pageStr)
	if err != nil || page < 1 {
		page = 1
	}

	size, err := strconv.Atoi(sizeStr)
	if err != nil || size < 1 || size > 100 {
		size = 10
	}

	// 注意：由于Service模型已优化，移除了搜索参数处理

	// 获取所有服务
	allServices, err := h.contentService.GetAllServices()
	if err != nil {
		response.InternalServerError(c, "获取服务列表失败")
		return
	}

	// 过滤服务
	var filteredServices []model.Service
	for _, service := range allServices {
		// 注意：由于Service模型已优化，移除了Name和Type字段
		// 如果需要过滤功能，建议在前端实现或重新设计数据结构

		filteredServices = append(filteredServices, service)
	}

	// 计算总数
	total := len(filteredServices)

	// 计算分页
	offset := (page - 1) * size
	end := offset + size

	// 处理边界情况
	if offset >= total {
		result := map[string]interface{}{
			"list":      []model.Service{},
			"total":     total,
			"page":      page,
			"page_size": size,
		}
		response.Success(c, result)
		return
	}

	if end > total {
		end = total
	}

	// 分页数据
	pagedServices := filteredServices[offset:end]

	// 构造响应数据
	result := map[string]interface{}{
		"list":      pagedServices,
		"total":     total,
		"page":      page,
		"page_size": size,
	}

	response.Success(c, result)
}

// GetServiceDetail 获取服务详情
func (h *ContentHandler) GetServiceDetail(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "无效的服务ID")
		return
	}

	service, err := h.contentService.GetServiceByID(uint(id))
	if err != nil {
		response.InternalServerError(c, "获取服务详情失败")
		return
	}
	response.Success(c, service)
}

// CreateService 创建服务
func (h *ContentHandler) CreateService(c *gin.Context) {
	var service model.Service
	if err := c.ShouldBindJSON(&service); err != nil {
		response.BadRequest(c, "请求参数错误: "+err.Error())
		return
	}

	if err := h.contentService.CreateService(&service); err != nil {
		response.InternalServerError(c, "创建服务失败")
		return
	}

	response.SuccessWithMsg(c, service, "创建服务成功")
}

// UpdateService 更新服务
func (h *ContentHandler) UpdateService(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "无效的服务ID")
		return
	}

	var service model.Service
	if err := c.ShouldBindJSON(&service); err != nil {
		response.BadRequest(c, "请求参数错误: "+err.Error())
		return
	}

	if err := h.contentService.UpdateService(uint(id), &service); err != nil {
		response.InternalServerError(c, "更新服务失败")
		return
	}

	response.SuccessWithMsg(c, service, "更新服务成功")
}

// DeleteService 删除服务
func (h *ContentHandler) DeleteService(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "无效的服务ID")
		return
	}

	if err := h.contentService.DeleteService(uint(id)); err != nil {
		response.InternalServerError(c, "删除服务失败")
		return
	}

	response.SuccessWithMsg(c, nil, "删除服务成功")
}

// UpdateServiceOrder 更新服务排序
func (h *ContentHandler) UpdateServiceOrder(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "无效的服务ID")
		return
	}

	var req struct {
		Order int `json:"order" binding:"required"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "请求参数错误: "+err.Error())
		return
	}

	// 获取现有服务
	service, err := h.contentService.GetServiceByID(uint(id))
	if err != nil {
		response.InternalServerError(c, "获取服务失败")
		return
	}

	// 更新排序
	service.Order = req.Order
	if err := h.contentService.UpdateService(uint(id), service); err != nil {
		response.InternalServerError(c, "更新服务排序失败")
		return
	}

	response.SuccessWithMsg(c, service, "更新排序成功")
}

// GetServiceTypes 获取服务类型列表
func (h *ContentHandler) GetServiceTypes(c *gin.Context) {
	types, err := h.contentService.GetServiceTypes()
	if err != nil {
		response.InternalServerError(c, "获取服务类型失败")
		return
	}
	response.Success(c, types)
}

// GetSwipers 获取轮播图列表（支持分页）
func (h *ContentHandler) GetSwipers(c *gin.Context) {
	// 获取分页参数
	pageStr := c.DefaultQuery("page", "1")
	sizeStr := c.DefaultQuery("size", "10")

	page, err := strconv.Atoi(pageStr)
	if err != nil || page < 1 {
		page = 1
	}

	size, err := strconv.Atoi(sizeStr)
	if err != nil || size < 1 || size > 100 {
		size = 10
	}

	// 获取所有轮播图
	swipers, err := h.contentService.GetAllSwipers()
	if err != nil {
		response.InternalServerError(c, "获取轮播图列表失败")
		return
	}

	// 计算总数
	total := len(swipers)

	// 计算分页
	offset := (page - 1) * size
	end := offset + size

	// 处理边界情况
	if offset >= total {
		// 返回空数据
		result := map[string]interface{}{
			"list":      []model.Swiper{},
			"total":     total,
			"page":      page,
			"page_size": size,
		}
		response.Success(c, result)
		return
	}

	if end > total {
		end = total
	}

	// 分页数据
	pagedSwipers := swipers[offset:end]

	// 构造响应数据
	result := map[string]interface{}{
		"list":      pagedSwipers,
		"total":     total,
		"page":      page,
		"page_size": size,
	}

	response.Success(c, result)
}

// GetSwiperByID 获取轮播图详情
func (h *ContentHandler) GetSwiperByID(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "无效的轮播图ID")
		return
	}

	swiper, err := h.contentService.GetSwiperByID(uint(id))
	if err != nil {
		response.InternalServerError(c, "获取轮播图详情失败")
		return
	}
	response.Success(c, swiper)
}

// CreateSwiper 创建轮播图
func (h *ContentHandler) CreateSwiper(c *gin.Context) {
	var swiper model.Swiper
	if err := c.ShouldBindJSON(&swiper); err != nil {
		response.BadRequest(c, "请求参数错误: "+err.Error())
		return
	}

	// 注意：已移除Status字段，轮播图默认为激活状态

	if err := h.contentService.CreateSwiper(&swiper); err != nil {
		response.InternalServerError(c, "创建轮播图失败")
		return
	}

	response.SuccessWithMsg(c, swiper, "创建轮播图成功")
}

// UpdateSwiper 更新轮播图
func (h *ContentHandler) UpdateSwiper(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "无效的轮播图ID")
		return
	}

	var swiper model.Swiper
	if err := c.ShouldBindJSON(&swiper); err != nil {
		response.BadRequest(c, "请求参数错误: "+err.Error())
		return
	}

	if err := h.contentService.UpdateSwiper(uint(id), &swiper); err != nil {
		response.InternalServerError(c, "更新轮播图失败")
		return
	}

	response.SuccessWithMsg(c, swiper, "更新轮播图成功")
}

// DeleteSwiper 删除轮播图
func (h *ContentHandler) DeleteSwiper(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "无效的轮播图ID")
		return
	}

	if err := h.contentService.DeleteSwiper(uint(id)); err != nil {
		response.InternalServerError(c, "删除轮播图失败")
		return
	}

	response.SuccessWithMsg(c, nil, "删除轮播图成功")
}

// UpdateSwiperOrder 更新轮播图排序
func (h *ContentHandler) UpdateSwiperOrder(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "无效的轮播图ID")
		return
	}

	var req struct {
		Order int `json:"order" binding:"required"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "请求参数错误: "+err.Error())
		return
	}

	// 获取现有轮播图
	swiper, err := h.contentService.GetSwiperByID(uint(id))
	if err != nil {
		response.InternalServerError(c, "获取轮播图失败")
		return
	}

	// 更新排序
	swiper.Order = req.Order
	if err := h.contentService.UpdateSwiper(uint(id), swiper); err != nil {
		response.InternalServerError(c, "更新轮播图排序失败")
		return
	}

	response.SuccessWithMsg(c, swiper, "更新排序成功")
}

// GetPartners 获取合作伙伴列表（支持分页）
func (h *ContentHandler) GetPartners(c *gin.Context) {
	// 获取分页参数
	pageStr := c.DefaultQuery("page", "1")
	sizeStr := c.DefaultQuery("size", "10")

	page, err := strconv.Atoi(pageStr)
	if err != nil || page < 1 {
		page = 1
	}

	size, err := strconv.Atoi(sizeStr)
	if err != nil || size < 1 || size > 100 {
		size = 10
	}

	// 获取搜索参数
	keyword := c.Query("keyword")

	// 获取所有合作伙伴
	allPartners, err := h.contentService.GetAllPartners()
	if err != nil {
		response.InternalServerError(c, "获取合作伙伴列表失败")
		return
	}

	// 过滤合作伙伴
	var filteredPartners []model.Partner
	for _, partner := range allPartners {
		// 关键词过滤
		if keyword != "" {
			if !strings.Contains(partner.Name, keyword) {
				continue
			}
		}

		filteredPartners = append(filteredPartners, partner)
	}

	// 计算总数
	total := len(filteredPartners)

	// 计算分页
	offset := (page - 1) * size
	end := offset + size

	// 处理边界情况
	if offset >= total {
		result := map[string]interface{}{
			"list":      []model.Partner{},
			"total":     total,
			"page":      page,
			"page_size": size,
		}
		response.Success(c, result)
		return
	}

	if end > total {
		end = total
	}

	// 分页数据
	pagedPartners := filteredPartners[offset:end]

	// 构造响应数据
	result := map[string]interface{}{
		"list":      pagedPartners,
		"total":     total,
		"page":      page,
		"page_size": size,
	}

	response.Success(c, result)
}

// GetPartnerDetail 获取合作伙伴详情
func (h *ContentHandler) GetPartnerDetail(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "无效的合作伙伴ID")
		return
	}

	partner, err := h.contentService.GetPartnerByID(uint(id))
	if err != nil {
		response.InternalServerError(c, "获取合作伙伴详情失败")
		return
	}
	response.Success(c, partner)
}

// CreatePartner 创建合作伙伴
func (h *ContentHandler) CreatePartner(c *gin.Context) {
	var partner model.Partner
	if err := c.ShouldBindJSON(&partner); err != nil {
		response.BadRequest(c, "请求参数错误: "+err.Error())
		return
	}

	if err := h.contentService.CreatePartner(&partner); err != nil {
		response.InternalServerError(c, "创建合作伙伴失败")
		return
	}

	response.SuccessWithMsg(c, partner, "创建合作伙伴成功")
}

// UpdatePartner 更新合作伙伴
func (h *ContentHandler) UpdatePartner(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "无效的合作伙伴ID")
		return
	}

	var partner model.Partner
	if err := c.ShouldBindJSON(&partner); err != nil {
		response.BadRequest(c, "请求参数错误: "+err.Error())
		return
	}

	if err := h.contentService.UpdatePartner(uint(id), &partner); err != nil {
		response.InternalServerError(c, "更新合作伙伴失败")
		return
	}

	response.SuccessWithMsg(c, partner, "更新合作伙伴成功")
}

// DeletePartner 删除合作伙伴
func (h *ContentHandler) DeletePartner(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "无效的合作伙伴ID")
		return
	}

	if err := h.contentService.DeletePartner(uint(id)); err != nil {
		response.InternalServerError(c, "删除合作伙伴失败")
		return
	}

	response.SuccessWithMsg(c, nil, "删除合作伙伴成功")
}

// GetFriendLinks 获取友情链接列表（支持分页）
func (h *ContentHandler) GetFriendLinks(c *gin.Context) {
	// 获取分页参数
	pageStr := c.DefaultQuery("page", "1")
	sizeStr := c.DefaultQuery("size", "10")

	page, err := strconv.Atoi(pageStr)
	if err != nil || page < 1 {
		page = 1
	}

	size, err := strconv.Atoi(sizeStr)
	if err != nil || size < 1 || size > 100 {
		size = 10
	}

	// 获取搜索参数
	keyword := c.Query("keyword")

	// 获取所有友情链接
	allFriendLinks, err := h.contentService.GetAllFriendLinks()
	if err != nil {
		response.InternalServerError(c, "获取友情链接列表失败")
		return
	}

	// 过滤友情链接
	var filteredFriendLinks []model.FriendLink
	for _, friendLink := range allFriendLinks {
		// 关键词过滤
		if keyword != "" {
			if !strings.Contains(friendLink.Name, keyword) {
				continue
			}
		}

		filteredFriendLinks = append(filteredFriendLinks, friendLink)
	}

	// 计算总数
	total := len(filteredFriendLinks)

	// 计算分页
	offset := (page - 1) * size
	end := offset + size

	// 处理边界情况
	if offset >= total {
		result := map[string]interface{}{
			"list":      []model.FriendLink{},
			"total":     total,
			"page":      page,
			"page_size": size,
		}
		response.Success(c, result)
		return
	}

	if end > total {
		end = total
	}

	// 分页数据
	pagedFriendLinks := filteredFriendLinks[offset:end]

	// 构造响应数据
	result := map[string]interface{}{
		"list":      pagedFriendLinks,
		"total":     total,
		"page":      page,
		"page_size": size,
	}

	response.Success(c, result)
}

// GetFriendLinkDetail 获取友情链接详情
func (h *ContentHandler) GetFriendLinkDetail(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "无效的友情链接ID")
		return
	}

	friendLink, err := h.contentService.GetFriendLinkByID(uint(id))
	if err != nil {
		response.InternalServerError(c, "获取友情链接详情失败")
		return
	}
	response.Success(c, friendLink)
}

// CreateFriendLink 创建友情链接
func (h *ContentHandler) CreateFriendLink(c *gin.Context) {
	var friendLink model.FriendLink
	if err := c.ShouldBindJSON(&friendLink); err != nil {
		log.Printf("CreateFriendLink bind error: %v", err)
		response.BadRequest(c, "请求参数错误: "+err.Error())
		return
	}

	log.Printf("CreateFriendLink request data: %+v", friendLink)

	if err := h.contentService.CreateFriendLink(&friendLink); err != nil {
		log.Printf("CreateFriendLink service error: %v", err)
		response.InternalServerError(c, "创建友情链接失败: "+err.Error())
		return
	}

	response.SuccessWithMsg(c, friendLink, "创建友情链接成功")
}

// UpdateFriendLink 更新友情链接
func (h *ContentHandler) UpdateFriendLink(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "无效的友情链接ID")
		return
	}

	var friendLink model.FriendLink
	if err := c.ShouldBindJSON(&friendLink); err != nil {
		response.BadRequest(c, "请求参数错误: "+err.Error())
		return
	}

	if err := h.contentService.UpdateFriendLink(uint(id), &friendLink); err != nil {
		response.InternalServerError(c, "更新友情链接失败")
		return
	}

	response.SuccessWithMsg(c, friendLink, "更新友情链接成功")
}

// DeleteFriendLink 删除友情链接
func (h *ContentHandler) DeleteFriendLink(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "无效的友情链接ID")
		return
	}

	if err := h.contentService.DeleteFriendLink(uint(id)); err != nil {
		response.InternalServerError(c, "删除友情链接失败")
		return
	}

	response.SuccessWithMsg(c, nil, "删除友情链接成功")
}

// UpdateFriendLinkOrder 更新友情链接排序
func (h *ContentHandler) UpdateFriendLinkOrder(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "无效的友情链接ID")
		return
	}

	var req struct {
		Order int `json:"order" binding:"required"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "请求参数错误: "+err.Error())
		return
	}

	// 获取现有友情链接
	friendLink, err := h.contentService.GetFriendLinkByID(uint(id))
	if err != nil {
		response.InternalServerError(c, "获取友情链接失败")
		return
	}

	// 更新排序
	friendLink.Order = req.Order
	if err := h.contentService.UpdateFriendLink(uint(id), friendLink); err != nil {
		response.InternalServerError(c, "更新友情链接排序失败")
		return
	}

	response.SuccessWithMsg(c, friendLink, "更新排序成功")
}

// GetRecruitments 获取招聘信息列表（支持分页）
func (h *ContentHandler) GetRecruitments(c *gin.Context) {
	// 获取分页参数
	pageStr := c.DefaultQuery("page", "1")
	sizeStr := c.DefaultQuery("size", "10")

	page, err := strconv.Atoi(pageStr)
	if err != nil || page < 1 {
		page = 1
	}

	size, err := strconv.Atoi(sizeStr)
	if err != nil || size < 1 || size > 100 {
		size = 10
	}

	// 获取搜索参数
	keyword := c.Query("keyword")
	location := c.Query("location")

	// 获取所有招聘信息
	allRecruitments, err := h.contentService.GetAllRecruitments()
	if err != nil {
		response.InternalServerError(c, "获取招聘信息列表失败")
		return
	}

	// 过滤招聘信息
	var filteredRecruitments []model.Recruitment
	for _, recruitment := range allRecruitments {
		// 关键词过滤（职位名称）
		if keyword != "" {
			if !strings.Contains(recruitment.Name, keyword) {
				continue
			}
		}

		// 工作地点过滤
		if location != "" {
			if !strings.Contains(recruitment.Location, location) {
				continue
			}
		}

		filteredRecruitments = append(filteredRecruitments, recruitment)
	}

	// 计算总数
	total := len(filteredRecruitments)

	// 计算分页
	offset := (page - 1) * size
	end := offset + size

	// 处理边界情况
	if offset >= total {
		result := map[string]interface{}{
			"list":      []model.Recruitment{},
			"total":     total,
			"page":      page,
			"page_size": size,
		}
		response.Success(c, result)
		return
	}

	if end > total {
		end = total
	}

	// 分页数据
	pagedRecruitments := filteredRecruitments[offset:end]

	// 构造响应数据
	result := map[string]interface{}{
		"list":      pagedRecruitments,
		"total":     total,
		"page":      page,
		"page_size": size,
	}

	response.Success(c, result)
}

// GetRecruitmentDetail 获取招聘信息详情
func (h *ContentHandler) GetRecruitmentDetail(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "无效的招聘信息ID")
		return
	}

	recruitment, err := h.contentService.GetRecruitmentByID(uint(id))
	if err != nil {
		response.InternalServerError(c, "获取招聘信息详情失败")
		return
	}
	response.Success(c, recruitment)
}

// CreateRecruitment 创建招聘信息
func (h *ContentHandler) CreateRecruitment(c *gin.Context) {
	log.Printf("CreateRecruitment called")

	var recruitment model.Recruitment
	if err := c.ShouldBindJSON(&recruitment); err != nil {
		log.Printf("CreateRecruitment bind error: %v", err)
		response.BadRequest(c, "请求参数错误: "+err.Error())
		return
	}

	log.Printf("CreateRecruitment request data: %+v", recruitment)

	if err := h.contentService.CreateRecruitment(&recruitment); err != nil {
		log.Printf("CreateRecruitment service error: %v", err)
		response.InternalServerError(c, "创建招聘信息失败: "+err.Error())
		return
	}

	response.SuccessWithMsg(c, recruitment, "创建招聘信息成功")
}

// UpdateRecruitment 更新招聘信息
func (h *ContentHandler) UpdateRecruitment(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "无效的招聘信息ID")
		return
	}

	var recruitment model.Recruitment
	if err := c.ShouldBindJSON(&recruitment); err != nil {
		response.BadRequest(c, "请求参数错误: "+err.Error())
		return
	}

	if err := h.contentService.UpdateRecruitment(uint(id), &recruitment); err != nil {
		response.InternalServerError(c, "更新招聘信息失败")
		return
	}

	response.SuccessWithMsg(c, recruitment, "更新招聘信息成功")
}

// DeleteRecruitment 删除招聘信息
func (h *ContentHandler) DeleteRecruitment(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "无效的招聘信息ID")
		return
	}

	if err := h.contentService.DeleteRecruitment(uint(id)); err != nil {
		response.InternalServerError(c, "删除招聘信息失败")
		return
	}

	response.SuccessWithMsg(c, nil, "删除招聘信息成功")
}

// GetPartPlatforms 获取平台列表（支持分页和搜索）
func (h *ContentHandler) GetPartPlatforms(c *gin.Context) {
	// 获取分页参数
	pageStr := c.DefaultQuery("page", "1")
	sizeStr := c.DefaultQuery("size", "10")

	page, err := strconv.Atoi(pageStr)
	if err != nil || page < 1 {
		page = 1
	}

	size, err := strconv.Atoi(sizeStr)
	if err != nil || size < 1 || size > 100 {
		size = 10
	}

	// 获取搜索参数
	keyword := c.Query("keyword")

	// 获取所有平台
	allPlatforms, err := h.contentService.GetAllPartPlatforms()
	if err != nil {
		response.InternalServerError(c, "获取平台列表失败")
		return
	}

	// 过滤平台
	var filteredPlatforms []model.PartPlatform
	for _, platform := range allPlatforms {
		// 关键词过滤（搜索平台名称）
		if keyword != "" {
			if !strings.Contains(platform.Name, keyword) {
				continue
			}
		}

		filteredPlatforms = append(filteredPlatforms, platform)
	}

	// 计算总数
	total := len(filteredPlatforms)

	// 计算分页
	offset := (page - 1) * size
	end := offset + size

	// 处理边界情况
	if offset >= total {
		result := map[string]interface{}{
			"list":      []model.PartPlatform{},
			"total":     total,
			"page":      page,
			"page_size": size,
		}
		response.Success(c, result)
		return
	}

	if end > total {
		end = total
	}

	// 分页数据
	pagedPlatforms := filteredPlatforms[offset:end]

	// 构造响应数据
	result := map[string]interface{}{
		"list":      pagedPlatforms,
		"total":     total,
		"page":      page,
		"page_size": size,
	}

	response.Success(c, result)
}

// GetPartPlatformDetail 获取平台详情
func (h *ContentHandler) GetPartPlatformDetail(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "无效的平台ID")
		return
	}

	platform, err := h.contentService.GetPartPlatformByID(uint(id))
	if err != nil {
		response.InternalServerError(c, "获取平台详情失败")
		return
	}
	response.Success(c, platform)
}

// CreatePartPlatform 创建平台
func (h *ContentHandler) CreatePartPlatform(c *gin.Context) {
	log.Printf("收到创建平台请求")

	var platform model.PartPlatform
	if err := c.ShouldBindJSON(&platform); err != nil {
		log.Printf("请求参数错误: %v", err)
		response.BadRequest(c, "请求参数错误: "+err.Error())
		return
	}

	log.Printf("平台数据: %+v", platform)

	if err := h.contentService.CreatePartPlatform(&platform); err != nil {
		log.Printf("创建平台失败: %v", err)
		response.InternalServerError(c, "创建平台失败")
		return
	}

	log.Printf("平台创建成功，ID: %d", platform.ID)
	response.SuccessWithMsg(c, platform, "创建平台成功")
}

// UpdatePartPlatform 更新平台
func (h *ContentHandler) UpdatePartPlatform(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "无效的平台ID")
		return
	}

	var platform model.PartPlatform
	if err := c.ShouldBindJSON(&platform); err != nil {
		response.BadRequest(c, "请求参数错误: "+err.Error())
		return
	}

	if err := h.contentService.UpdatePartPlatform(uint(id), &platform); err != nil {
		response.InternalServerError(c, "更新平台失败")
		return
	}

	response.SuccessWithMsg(c, platform, "更新平台成功")
}

// DeletePartPlatform 删除平台
func (h *ContentHandler) DeletePartPlatform(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "无效的平台ID")
		return
	}

	if err := h.contentService.DeletePartPlatform(uint(id)); err != nil {
		response.InternalServerError(c, "删除平台失败")
		return
	}

	response.SuccessWithMsg(c, nil, "删除平台成功")
}

// GetProjectCases 获取项目案例列表（支持分页）
func (h *ContentHandler) GetProjectCases(c *gin.Context) {
	// 获取分页参数
	pageStr := c.DefaultQuery("page", "1")
	sizeStr := c.DefaultQuery("size", "10")

	page, err := strconv.Atoi(pageStr)
	if err != nil || page < 1 {
		page = 1
	}

	size, err := strconv.Atoi(sizeStr)
	if err != nil || size < 1 || size > 100 {
		size = 10
	}

	// 注意：由于ProjectCase模型已优化，移除了搜索参数处理

	// 获取所有项目案例
	allCases, err := h.contentService.GetAllProjectCases()
	if err != nil {
		response.InternalServerError(c, "获取项目案例列表失败")
		return
	}

	// 过滤项目案例
	var filteredCases []model.ProjectCase
	for _, projectCase := range allCases {
		// 注意：由于ProjectCase模型已优化，移除了Title字段，无法进行关键词过滤

		filteredCases = append(filteredCases, projectCase)
	}

	// 计算总数
	total := len(filteredCases)

	// 计算分页
	offset := (page - 1) * size
	end := offset + size

	// 处理边界情况
	if offset >= total {
		result := map[string]interface{}{
			"list":      []model.ProjectCase{},
			"total":     total,
			"page":      page,
			"page_size": size,
		}
		response.Success(c, result)
		return
	}

	if end > total {
		end = total
	}

	// 分页数据
	pagedCases := filteredCases[offset:end]

	// 构造响应数据
	result := map[string]interface{}{
		"list":      pagedCases,
		"total":     total,
		"page":      page,
		"page_size": size,
	}

	response.Success(c, result)
}

// GetProjectCaseDetail 获取项目案例详情
func (h *ContentHandler) GetProjectCaseDetail(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "无效的项目案例ID")
		return
	}

	projectCase, err := h.contentService.GetProjectCaseByID(uint(id))
	if err != nil {
		response.InternalServerError(c, "获取项目案例详情失败")
		return
	}
	response.Success(c, projectCase)
}

// CreateProjectCase 创建项目案例
func (h *ContentHandler) CreateProjectCase(c *gin.Context) {
	var projectCase model.ProjectCase
	if err := c.ShouldBindJSON(&projectCase); err != nil {
		response.BadRequest(c, "请求参数错误: "+err.Error())
		return
	}

	if err := h.contentService.CreateProjectCase(&projectCase); err != nil {
		response.InternalServerError(c, "创建项目案例失败")
		return
	}

	response.SuccessWithMsg(c, projectCase, "创建项目案例成功")
}

// UpdateProjectCase 更新项目案例
func (h *ContentHandler) UpdateProjectCase(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "无效的项目案例ID")
		return
	}

	var projectCase model.ProjectCase
	if err := c.ShouldBindJSON(&projectCase); err != nil {
		response.BadRequest(c, "请求参数错误: "+err.Error())
		return
	}

	if err := h.contentService.UpdateProjectCase(uint(id), &projectCase); err != nil {
		response.InternalServerError(c, "更新项目案例失败")
		return
	}

	response.SuccessWithMsg(c, projectCase, "更新项目案例成功")
}

// DeleteProjectCase 删除项目案例
func (h *ContentHandler) DeleteProjectCase(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "无效的项目案例ID")
		return
	}

	if err := h.contentService.DeleteProjectCase(uint(id)); err != nil {
		response.InternalServerError(c, "删除项目案例失败")
		return
	}

	response.SuccessWithMsg(c, nil, "删除项目案例成功")
}

// UpdateProjectCaseSort 更新项目案例排序
func (h *ContentHandler) UpdateProjectCaseSort(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "无效的项目案例ID")
		return
	}

	var req struct {
		Sort int `json:"sort" binding:"required"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "请求参数错误: "+err.Error())
		return
	}

	// 获取现有项目案例
	projectCase, err := h.contentService.GetProjectCaseByID(uint(id))
	if err != nil {
		response.InternalServerError(c, "获取项目案例失败")
		return
	}

	// 注意：由于ProjectCase模型已优化，移除了Sort字段，排序功能已不可用
	// 如果需要排序功能，建议重新设计数据结构
	response.BadRequest(c, "排序功能已移除，请联系开发人员")

	response.SuccessWithMsg(c, projectCase, "更新排序成功")
}
