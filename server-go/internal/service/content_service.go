package service

import (
	"fmt"
	"strings"
	"weishi-server/internal/model"
	"weishi-server/internal/repository"
	"weishi-server/pkg/cos"
	"weishi-server/pkg/logger"
)

type ContentService interface {
	// 新闻相关方法
	GetAllNews() ([]model.News, error)
	GetHomePageNews() ([]model.News, error)
	GetNewsByID(id uint) (*model.News, error)
	CreateNews(news *model.News) error
	UpdateNews(id uint, news *model.News) error
	DeleteNews(id uint) error
	SetNewsHomePage(id uint, isHomePage bool) error

	// 服务相关方法
	GetAllServices() ([]model.Service, error)
	GetServiceByID(id uint) (*model.Service, error)
	CreateService(service *model.Service) error
	UpdateService(id uint, service *model.Service) error
	DeleteService(id uint) error
	GetServiceTypes() ([]string, error)

	// 轮播图相关方法
	GetAllSwipers() ([]model.Swiper, error)
	GetSwiperByID(id uint) (*model.Swiper, error)
	CreateSwiper(swiper *model.Swiper) error
	UpdateSwiper(id uint, swiper *model.Swiper) error
	DeleteSwiper(id uint) error

	// 合作伙伴相关方法
	GetAllPartners() ([]model.Partner, error)
	GetPartnerByID(id uint) (*model.Partner, error)
	CreatePartner(partner *model.Partner) error
	UpdatePartner(id uint, partner *model.Partner) error
	DeletePartner(id uint) error
	// 友情链接相关方法
	GetAllFriendLinks() ([]model.FriendLink, error)
	GetFriendLinkByID(id uint) (*model.FriendLink, error)
	CreateFriendLink(friendLink *model.FriendLink) error
	UpdateFriendLink(id uint, friendLink *model.FriendLink) error
	DeleteFriendLink(id uint) error

	// 招聘信息相关方法
	GetAllRecruitments() ([]model.Recruitment, error)
	GetRecruitmentByID(id uint) (*model.Recruitment, error)
	CreateRecruitment(recruitment *model.Recruitment) error
	UpdateRecruitment(id uint, recruitment *model.Recruitment) error
	DeleteRecruitment(id uint) error

	// 平台相关方法
	GetAllPartPlatforms() ([]model.PartPlatform, error)
	GetPartPlatformByID(id uint) (*model.PartPlatform, error)
	CreatePartPlatform(platform *model.PartPlatform) error
	UpdatePartPlatform(id uint, platform *model.PartPlatform) error
	DeletePartPlatform(id uint) error

	// 项目案例相关方法
	GetAllProjectCases() ([]model.ProjectCase, error)
	GetProjectCaseByID(id uint) (*model.ProjectCase, error)
	CreateProjectCase(projectCase *model.ProjectCase) error
	UpdateProjectCase(id uint, projectCase *model.ProjectCase) error
	DeleteProjectCase(id uint) error

	// 文件清理相关方法
	SetCosClient(cosClient *cos.Client)
}

type contentService struct {
	contentRepo repository.ContentRepository
	cosClient   *cos.Client
}

func NewContentService(contentRepo repository.ContentRepository) ContentService {
	return &contentService{
		contentRepo: contentRepo,
		cosClient:   nil, // 可以为nil，在删除时会检查
	}
}

// SetCosClient 设置COS客户端（用于文件清理）
func (s *contentService) SetCosClient(cosClient *cos.Client) {
	s.cosClient = cosClient
}

// extractFileKeyFromURL 从COS URL中提取文件key（仅处理腾讯COS链接）
func (s *contentService) extractFileKeyFromURL(url string) string {
	if url == "" {
		return ""
	}

	// 只处理腾讯COS的标准域名格式
	// 格式：https://bucket.cos.region.myqcloud.com/module/filename
	if strings.Contains(url, ".cos.") && strings.Contains(url, ".myqcloud.com/") {
		// 标准COS域名格式
		parts := strings.Split(url, ".myqcloud.com/")
		if len(parts) > 1 {
			return parts[1]
		}
	}

	// 检查是否是配置的自定义COS域名
	if s.cosClient != nil {
		config := s.cosClient.GetConfig()
		if config.Domain != "" {
			// 检查是否匹配配置的自定义域名
			expectedDomainPrefix := fmt.Sprintf("https://%s/", config.Domain)
			if strings.HasPrefix(url, expectedDomainPrefix) {
				// 提取路径部分
				return strings.TrimPrefix(url, expectedDomainPrefix)
			}

			// 也支持http协议
			expectedDomainPrefixHTTP := fmt.Sprintf("http://%s/", config.Domain)
			if strings.HasPrefix(url, expectedDomainPrefixHTTP) {
				return strings.TrimPrefix(url, expectedDomainPrefixHTTP)
			}
		}
	}

	// 如果不是腾讯COS链接，返回空字符串
	return ""
}

// isCosURL 判断URL是否为腾讯COS链接
func (s *contentService) isCosURL(url string) bool {
	if url == "" {
		return false
	}

	// 检查标准COS域名格式
	if strings.Contains(url, ".cos.") && strings.Contains(url, ".myqcloud.com") {
		return true
	}

	// 检查是否是配置的自定义COS域名
	if s.cosClient != nil {
		config := s.cosClient.GetConfig()
		if config.Domain != "" {
			return strings.HasPrefix(url, fmt.Sprintf("https://%s/", config.Domain)) ||
				strings.HasPrefix(url, fmt.Sprintf("http://%s/", config.Domain))
		}
	}

	return false
}

// cleanupCosFile 清理COS文件（仅删除腾讯COS链接，不管成功与否，不通知前端）
func (s *contentService) cleanupCosFile(url string) {
	s.cleanupCosFileWithModule(url, "内容管理")
}

// cleanupCosFileWithModule 清理COS文件（带模块信息）
func (s *contentService) cleanupCosFileWithModule(url, module string) {
	if s.cosClient == nil {
		logger.Warn("[文件清理] COS客户端未初始化，跳过文件清理")
		return
	}

	if url == "" {
		logger.Warn("[文件清理] URL为空，跳过文件清理")
		return
	}

	// 首先判断是否为COS链接
	if !s.isCosURL(url) {
		logger.Info(fmt.Sprintf("[文件清理] 跳过非COS文件清理 (外部链接): %s", url))
		return
	}

	fileKey := s.extractFileKeyFromURL(url)
	if fileKey == "" {
		logger.Error(fmt.Sprintf("[文件清理] ❌ 无法解析COS文件路径，URL格式可能不正确: %s", url))
		return
	}

	logger.Info(fmt.Sprintf("[文件清理] 🗑️ [%s] 开始清理COS文件: %s (Key: %s)", module, url, fileKey))

	// 删除COS文件，不管成功与否，不影响主流程
	err := s.cosClient.DeleteFile(fileKey)
	if err != nil {
		// 详细记录删除失败的错误信息
		logger.Error(fmt.Sprintf("[文件清理] ❌ [%s] 清理COS文件失败", module))
		logger.Error(fmt.Sprintf("  📄 文件URL: %s", url))
		logger.Error(fmt.Sprintf("  🔑 文件Key: %s", fileKey))
		logger.Error(fmt.Sprintf("  ⚠️  错误类型: %T", err))
		logger.Error(fmt.Sprintf("  📝 错误详情: %v", err))

		// 根据错误类型提供更有用的信息
		if strings.Contains(err.Error(), "NoSuchKey") || strings.Contains(err.Error(), "404") {
			logger.Info("  💡 提示: 文件可能已被删除或不存在")
		} else if strings.Contains(err.Error(), "AccessDenied") || strings.Contains(err.Error(), "403") {
			logger.Warn("  💡 提示: COS访问权限不足，请检查SecretID和SecretKey配置")
		} else if strings.Contains(err.Error(), "timeout") || strings.Contains(err.Error(), "connection") {
			logger.Warn("  💡 提示: 网络连接问题，请检查网络连接和COS服务状态")
		}

		logger.Info("  ℹ️  注意: 此错误不会影响主要业务流程")
	} else {
		logger.Info(fmt.Sprintf("[文件清理] ✅ [%s] 成功清理COS文件: %s (Key: %s)", module, url, fileKey))
	}
}

func (s *contentService) GetAllNews() ([]model.News, error) {
	return s.contentRepo.GetAllNews()
}

func (s *contentService) GetHomePageNews() ([]model.News, error) {
	return s.contentRepo.GetHomePageNews()
}

func (s *contentService) GetNewsByID(id uint) (*model.News, error) {
	return s.contentRepo.GetNewsByID(id)
}

func (s *contentService) CreateNews(news *model.News) error {
	return s.contentRepo.CreateNews(news)
}

func (s *contentService) UpdateNews(id uint, news *model.News) error {
	news.ID = id
	return s.contentRepo.UpdateNews(news)
}

func (s *contentService) DeleteNews(id uint) error {
	// 先获取新闻信息，用于清理关联的文件
	news, err := s.contentRepo.GetNewsByID(id)
	if err != nil {
		return err
	}

	logger.Info(fmt.Sprintf("[新闻管理] 开始删除新闻，ID: %d, 标题: %s", id, news.Title))

	// 删除新闻
	err = s.contentRepo.DeleteNews(id)
	if err != nil {
		logger.Error(fmt.Sprintf("[新闻管理] ❌ 删除新闻失败，ID: %d, 错误: %v", id, err))
		return err
	}

	logger.Info(fmt.Sprintf("[新闻管理] ✅ 成功删除新闻，ID: %d", id))

	// 清理关联的图片文件
	if news.Image != nil && *news.Image != "" {
		logger.Info(fmt.Sprintf("[新闻管理] 开始清理关联图片文件: %s", *news.Image))
		s.cleanupCosFileWithModule(*news.Image, "新闻管理")
	}

	return nil
}

func (s *contentService) SetNewsHomePage(id uint, isHomePage bool) error {
	return s.contentRepo.SetNewsHomePage(id, isHomePage)
}

func (s *contentService) GetAllServices() ([]model.Service, error) {
	return s.contentRepo.GetAllServices()
}

func (s *contentService) GetServiceByID(id uint) (*model.Service, error) {
	return s.contentRepo.GetServiceByID(id)
}

func (s *contentService) CreateService(service *model.Service) error {
	return s.contentRepo.CreateService(service)
}

func (s *contentService) UpdateService(id uint, service *model.Service) error {
	service.ID = id
	return s.contentRepo.UpdateService(service)
}

func (s *contentService) DeleteService(id uint) error {
	// 先获取服务信息，用于清理关联的文件
	service, err := s.contentRepo.GetServiceByID(id)
	if err != nil {
		return err
	}

	logger.Info(fmt.Sprintf("[服务管理] 开始删除服务，ID: %d, 名称: %s", id, service.Name))

	// 删除服务
	err = s.contentRepo.DeleteService(id)
	if err != nil {
		logger.Error(fmt.Sprintf("[服务管理] ❌ 删除服务失败，ID: %d, 错误: %v", id, err))
		return err
	}

	logger.Info(fmt.Sprintf("[服务管理] ✅ 成功删除服务，ID: %d", id))

	// 清理关联的图片文件
	if service.Image != "" {
		logger.Info(fmt.Sprintf("[服务管理] 开始清理关联图片文件: %s", service.Image))
		s.cleanupCosFileWithModule(service.Image, "服务管理")
	}

	return nil
}

func (s *contentService) GetServiceTypes() ([]string, error) {
	return s.contentRepo.GetServiceTypes()
}

func (s *contentService) GetAllSwipers() ([]model.Swiper, error) {
	return s.contentRepo.GetAllSwipers()
}

func (s *contentService) GetSwiperByID(id uint) (*model.Swiper, error) {
	return s.contentRepo.GetSwiperByID(id)
}

func (s *contentService) CreateSwiper(swiper *model.Swiper) error {
	return s.contentRepo.CreateSwiper(swiper)
}

func (s *contentService) UpdateSwiper(id uint, swiper *model.Swiper) error {
	swiper.ID = id
	return s.contentRepo.UpdateSwiper(swiper)
}

func (s *contentService) DeleteSwiper(id uint) error {
	// 先获取轮播图信息，用于清理关联的文件
	swiper, err := s.contentRepo.GetSwiperByID(id)
	if err != nil {
		return err
	}

	logger.Info(fmt.Sprintf("[轮播图管理] 开始删除轮播图，ID: %d, 标题: %s", id, swiper.Title))

	// 删除轮播图
	err = s.contentRepo.DeleteSwiper(id)
	if err != nil {
		logger.Error(fmt.Sprintf("[轮播图管理] ❌ 删除轮播图失败，ID: %d, 错误: %v", id, err))
		return err
	}

	logger.Info(fmt.Sprintf("[轮播图管理] ✅ 成功删除轮播图，ID: %d", id))

	// 清理关联的图片文件
	if swiper.URL != "" {
		logger.Info(fmt.Sprintf("[轮播图管理] 开始清理关联图片文件: %s", swiper.URL))
		s.cleanupCosFileWithModule(swiper.URL, "轮播图管理")
	}

	return nil
}

func (s *contentService) GetAllPartners() ([]model.Partner, error) {
	return s.contentRepo.GetAllPartners()
}

func (s *contentService) GetPartnerByID(id uint) (*model.Partner, error) {
	return s.contentRepo.GetPartnerByID(id)
}

func (s *contentService) CreatePartner(partner *model.Partner) error {
	return s.contentRepo.CreatePartner(partner)
}

func (s *contentService) UpdatePartner(id uint, partner *model.Partner) error {
	partner.ID = id
	return s.contentRepo.UpdatePartner(partner)
}

func (s *contentService) DeletePartner(id uint) error {
	// 先获取合作伙伴信息，用于清理关联的文件
	partner, err := s.contentRepo.GetPartnerByID(id)
	if err != nil {
		return err
	}

	logger.Info(fmt.Sprintf("[合作伙伴管理] 开始删除合作伙伴，ID: %d, 名称: %s", id, partner.Name))

	// 删除合作伙伴
	err = s.contentRepo.DeletePartner(id)
	if err != nil {
		logger.Error(fmt.Sprintf("[合作伙伴管理] ❌ 删除合作伙伴失败，ID: %d, 错误: %v", id, err))
		return err
	}

	logger.Info(fmt.Sprintf("[合作伙伴管理] ✅ 成功删除合作伙伴，ID: %d", id))

	// 清理关联的logo文件
	if partner.Logo != "" {
		logger.Info(fmt.Sprintf("[合作伙伴管理] 开始清理关联Logo文件: %s", partner.Logo))
		s.cleanupCosFileWithModule(partner.Logo, "合作伙伴管理")
	}

	return nil
}

func (s *contentService) GetAllFriendLinks() ([]model.FriendLink, error) {
	return s.contentRepo.GetAllFriendLinks()
}

func (s *contentService) GetFriendLinkByID(id uint) (*model.FriendLink, error) {
	return s.contentRepo.GetFriendLinkByID(id)
}

func (s *contentService) CreateFriendLink(friendLink *model.FriendLink) error {
	return s.contentRepo.CreateFriendLink(friendLink)
}

func (s *contentService) UpdateFriendLink(id uint, friendLink *model.FriendLink) error {
	friendLink.ID = id
	return s.contentRepo.UpdateFriendLink(friendLink)
}

func (s *contentService) DeleteFriendLink(id uint) error {
	return s.contentRepo.DeleteFriendLink(id)
}

func (s *contentService) GetAllRecruitments() ([]model.Recruitment, error) {
	return s.contentRepo.GetAllRecruitments()
}

func (s *contentService) GetRecruitmentByID(id uint) (*model.Recruitment, error) {
	return s.contentRepo.GetRecruitmentByID(id)
}

func (s *contentService) CreateRecruitment(recruitment *model.Recruitment) error {
	return s.contentRepo.CreateRecruitment(recruitment)
}

func (s *contentService) UpdateRecruitment(id uint, recruitment *model.Recruitment) error {
	recruitment.ID = id
	return s.contentRepo.UpdateRecruitment(recruitment)
}

func (s *contentService) DeleteRecruitment(id uint) error {
	return s.contentRepo.DeleteRecruitment(id)
}

func (s *contentService) GetAllPartPlatforms() ([]model.PartPlatform, error) {
	return s.contentRepo.GetAllPartPlatforms()
}

func (s *contentService) GetPartPlatformByID(id uint) (*model.PartPlatform, error) {
	return s.contentRepo.GetPartPlatformByID(id)
}

func (s *contentService) CreatePartPlatform(platform *model.PartPlatform) error {
	return s.contentRepo.CreatePartPlatform(platform)
}

func (s *contentService) UpdatePartPlatform(id uint, platform *model.PartPlatform) error {
	platform.ID = id
	return s.contentRepo.UpdatePartPlatform(platform)
}

func (s *contentService) DeletePartPlatform(id uint) error {
	return s.contentRepo.DeletePartPlatform(id)
}

func (s *contentService) GetAllProjectCases() ([]model.ProjectCase, error) {
	return s.contentRepo.GetAllProjectCases()
}

func (s *contentService) GetProjectCaseByID(id uint) (*model.ProjectCase, error) {
	return s.contentRepo.GetProjectCaseByID(id)
}

func (s *contentService) CreateProjectCase(projectCase *model.ProjectCase) error {
	return s.contentRepo.CreateProjectCase(projectCase)
}

func (s *contentService) UpdateProjectCase(id uint, projectCase *model.ProjectCase) error {
	projectCase.ID = id
	return s.contentRepo.UpdateProjectCase(projectCase)
}

func (s *contentService) DeleteProjectCase(id uint) error {
	// 先获取项目案例信息，用于清理关联的文件
	projectCase, err := s.contentRepo.GetProjectCaseByID(id)
	if err != nil {
		return err
	}

	title := "未知项目"
	if projectCase.Title != nil {
		title = *projectCase.Title
	}
	logger.Info(fmt.Sprintf("[项目案例管理] 开始删除项目案例，ID: %d, 标题: %s", id, title))

	// 删除项目案例
	err = s.contentRepo.DeleteProjectCase(id)
	if err != nil {
		logger.Error(fmt.Sprintf("[项目案例管理] ❌ 删除项目案例失败，ID: %d, 错误: %v", id, err))
		return err
	}

	logger.Info(fmt.Sprintf("[项目案例管理] ✅ 成功删除项目案例，ID: %d", id))

	// 清理关联的文件
	if projectCase.URL != "" {
		logger.Info(fmt.Sprintf("[项目案例管理] 开始清理关联文件: %s", projectCase.URL))
		s.cleanupCosFileWithModule(projectCase.URL, "项目案例管理")
	}

	return nil
}
