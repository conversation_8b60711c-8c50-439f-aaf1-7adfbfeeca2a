package router

import (
	"fmt"
	"log"
	"weishi-server/internal/config"
	"weishi-server/internal/handler"
	"weishi-server/internal/middleware"
	"weishi-server/internal/repository"
	"weishi-server/internal/service"
	"weishi-server/pkg/cos"

	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
	swaggerFiles "github.com/swaggo/files"
	ginSwagger "github.com/swaggo/gin-swagger"
	"gorm.io/gorm"
)

func New(db *gorm.DB, cfg *config.Config) *gin.Engine {
	r := gin.New()

	// 添加中间件
	r.Use(gin.Logger())
	r.Use(gin.Recovery())

	// CORS 配置
	r.Use(cors.New(cors.Config{
		AllowOrigins: []string{
			"http://localhost:3000", // Nuxt3 前端 (生产)
			"http://127.0.0.1:3000",
			"http://localhost:3003", // Nuxt3 前端 (本地部署)
			"http://127.0.0.1:3003",
			"http://localhost:4000", // Nuxt3 前端 (开发)
			"http://127.0.0.1:4000",
			"http://localhost:5173", // Vue3 管理后台 (开发)
			"http://127.0.0.1:5173",
			"http://localhost",      // 本地Caddy代理 (HTTP)
			"https://localhost",     // 本地Caddy代理 (HTTPS)
			"http://localhost:8080", // 本地Caddy代理 (备用端口)
			"https://viclink.cn",    // 生产环境域名
			"https://www.viclink.cn",
		},
		AllowMethods:     []string{"GET", "POST", "PUT", "PATCH", "DELETE", "OPTIONS"},
		AllowHeaders:     []string{"Origin", "Content-Type", "Accept", "Authorization", "X-Requested-With"},
		ExposeHeaders:    []string{"Content-Length"},
		AllowCredentials: true,
		MaxAge:           12 * 3600, // 12小时
	}))

	// 调试中间件
	r.Use(func(c *gin.Context) {
		log.Printf("Request: %s %s, Origin: %s, Content-Type: %s",
			c.Request.Method, c.Request.URL.Path,
			c.Request.Header.Get("Origin"),
			c.Request.Header.Get("Content-Type"))
		c.Next()
		log.Printf("Response Status: %d", c.Writer.Status())
	})

	// 健康检查
	r.GET("/api/health", func(c *gin.Context) {
		c.JSON(200, gin.H{"status": "ok"})
	})

	// Swagger 文档
	r.GET("/api/docs/*any", ginSwagger.WrapHandler(swaggerFiles.Handler))

	// API 路由组
	api := r.Group("/api")

	// 公开API路由（不需要认证）
	setupPublicRoutes(api, db, cfg)

	// 需要认证的API路由
	authAPI := api.Group("")
	authAPI.Use(middleware.JWTAuth(cfg))
	setupAuthRoutes(authAPI, db, cfg)

	return r
}

func setupPublicRoutes(api *gin.RouterGroup, db *gorm.DB, cfg *config.Config) {
	// 内容模块
	contentRepo := repository.NewContentRepository(db)
	contentService := service.NewContentService(contentRepo)
	contentHandler := handler.NewContentHandler(contentService)

	// 新闻相关路由
	api.GET("/news", contentHandler.GetNews)
	api.GET("/news/homepage", contentHandler.GetHomePageNews)
	api.GET("/news/:id", contentHandler.GetNewsDetail)

	// 其他公开内容路由
	api.GET("/services", contentHandler.GetServices)
	api.GET("/services/types", contentHandler.GetServiceTypes)
	api.GET("/services/:id", contentHandler.GetServiceDetail)
	api.GET("/swipers", contentHandler.GetSwipers)
	api.GET("/swipers/:id", contentHandler.GetSwiperByID)
	api.GET("/partners", contentHandler.GetPartners)
	api.GET("/partners/:id", contentHandler.GetPartnerDetail)
	api.GET("/friendlinks", contentHandler.GetFriendLinks)
	api.GET("/friendlinks/:id", contentHandler.GetFriendLinkDetail)
	api.GET("/recruitments", contentHandler.GetRecruitments)
	api.GET("/part-platforms", contentHandler.GetPartPlatforms)
	api.GET("/part-platforms/:id", contentHandler.GetPartPlatformDetail)
	api.GET("/project-cases", contentHandler.GetProjectCases)
	api.GET("/project-cases/:id", contentHandler.GetProjectCaseDetail)

	// 验证码模块
	captchaHandler := handler.NewCaptchaHandler()
	api.GET("/captcha", captchaHandler.GenerateCaptcha)
	api.POST("/captcha/verify", captchaHandler.VerifyCaptcha)

	// 管理员登录（公开接口）
	adminRepo := repository.NewAdminRepository(db)
	roleRepo := repository.NewRoleRepository(db)
	permissionRepo := repository.NewPermissionRepository(db)
	adminLogRepo := repository.NewAdminLogRepository(db)
	adminService := service.NewAdminService(adminRepo, roleRepo, permissionRepo, adminLogRepo)
	adminHandler := handler.NewAdminHandler(adminService, cfg)

	adminAuth := api.Group("/admin/auth")
	{
		adminAuth.POST("/login", adminHandler.Login)
		adminAuth.GET("/captcha", captchaHandler.GenerateCaptcha)       // 添加管理端验证码路由
		adminAuth.POST("/captcha/verify", captchaHandler.VerifyCaptcha) // 添加管理端验证码验证路由
		adminAuth.GET("/test", func(c *gin.Context) {
			c.JSON(200, gin.H{"message": "admin auth route working"})
		})
	}
}

func setupAuthRoutes(api *gin.RouterGroup, db *gorm.DB, cfg *config.Config) {
	// 管理员模块
	adminRepo := repository.NewAdminRepository(db)
	roleRepo := repository.NewRoleRepository(db)
	permissionRepo := repository.NewPermissionRepository(db)
	adminLogRepo := repository.NewAdminLogRepository(db)
	adminService := service.NewAdminService(adminRepo, roleRepo, permissionRepo, adminLogRepo)
	adminHandler := handler.NewAdminHandler(adminService, cfg)

	// 操作日志模块（需要在管理员路由前初始化）
	adminLogService := service.NewAdminLogService(adminLogRepo)
	adminLogHandler := handler.NewAdminLogHandler(adminLogService)

	// 管理员路由组（添加日志中间件）
	adminAPI := api.Group("/admin")
	adminAPI.Use(middleware.AdminLogMiddleware(adminLogService))

	// 管理员认证路由（需要token）
	adminAuth := adminAPI.Group("/auth")
	{
		adminAuth.POST("/logout", adminHandler.Logout)
		adminAuth.GET("/profile", adminHandler.GetProfile)
		adminAuth.PUT("/profile", adminHandler.UpdateProfile)
		adminAuth.POST("/profile/change-password", adminHandler.ChangePassword)
	}

	// 管理员个人资料路由已移至 /admin/auth/profile

	// 管理员管理
	admins := adminAPI.Group("/users")
	{
		admins.POST("", adminHandler.CreateAdmin)
		admins.GET("", adminHandler.GetAdmins)
		admins.GET("/:id", adminHandler.GetAdmin)
		admins.PUT("/:id", adminHandler.UpdateAdmin)
		admins.DELETE("/:id", adminHandler.DeleteAdmin)
		admins.POST("/batch-delete", adminHandler.BatchDeleteAdmins)
		admins.PUT("/:id/toggle-status", adminHandler.ToggleStatus)
		admins.POST("/:id/reset-password", adminHandler.ResetPassword)
		admins.GET("/:id/roles", adminHandler.GetUserRoles)
		admins.POST("/:id/roles", adminHandler.AssignRoles)
	}

	// 角色模块
	roleService := service.NewRoleService(roleRepo, permissionRepo)
	roleHandler := handler.NewRoleHandler(roleService)

	roles := adminAPI.Group("/roles")
	{
		roles.GET("", roleHandler.GetRoles)
		roles.GET("/all", roleHandler.GetAllRoles)
		roles.GET("/:id", roleHandler.GetRole)
		roles.POST("", roleHandler.CreateRole)
		roles.PUT("/:id", roleHandler.UpdateRole)
		roles.DELETE("/:id", roleHandler.DeleteRole)
		roles.POST("/batch-delete", roleHandler.BatchDeleteRoles)
		roles.PUT("/:id/toggle-status", roleHandler.ToggleStatus)
		roles.GET("/:id/permissions", roleHandler.GetRolePermissions)
		roles.POST("/:id/permissions", roleHandler.AssignPermissions)
		// roles.POST("/:id/copy", roleHandler.CopyRole) // 暂时禁用
	}

	// 权限模块
	permissionService := service.NewPermissionService(permissionRepo)
	permissionHandler := handler.NewPermissionHandler(permissionService)

	permissions := adminAPI.Group("/permissions")
	{
		permissions.GET("", permissionHandler.GetPermissions)
		permissions.GET("/tree", permissionHandler.GetPermissionTree)
		permissions.GET("/all", permissionHandler.GetAllPermissions)
		permissions.GET("/menu", permissionHandler.GetMenuPermissions)
		permissions.GET("/children/:parentId", permissionHandler.GetChildPermissions)
		permissions.GET("/:id", permissionHandler.GetPermission)
		permissions.POST("", permissionHandler.CreatePermission)
		permissions.PUT("/:id", permissionHandler.UpdatePermission)
		permissions.DELETE("/:id", permissionHandler.DeletePermission)
		permissions.POST("/batch-delete", permissionHandler.BatchDeletePermissions)
		permissions.PUT("/:id/toggle-status", permissionHandler.ToggleStatus)
		// permissions.POST("/:id/move", permissionHandler.MovePermission) // 暂时禁用
	}

	logs := adminAPI.Group("/logs")
	{
		logs.GET("", adminLogHandler.GetAdminLogs)
		logs.GET("/:id", adminLogHandler.GetAdminLog)
		logs.GET("/statistics", adminLogHandler.GetAdminLogStatistics)
		logs.GET("/today", adminLogHandler.GetTodayLogs)
		logs.GET("/user/:userId", adminLogHandler.GetUserLogs)
		logs.GET("/module/:module", adminLogHandler.GetModuleLogs)
		logs.POST("/cleanup", adminLogHandler.DeleteOldLogs)
	}

	// 仪表板模块
	dashboardService := service.NewDashboardService(adminRepo, roleRepo, permissionRepo, adminLogRepo)
	dashboardHandler := handler.NewDashboardHandler(dashboardService)

	dashboard := adminAPI.Group("/dashboard")
	{
		dashboard.GET("/overview", dashboardHandler.GetOverviewStats)
		dashboard.GET("/system", dashboardHandler.GetSystemInfo)
		dashboard.GET("/activity", dashboardHandler.GetRecentActivity)
		dashboard.GET("/user-growth", dashboardHandler.GetUserGrowthStats)
		dashboard.GET("/module-access", dashboardHandler.GetModuleAccessStats)
		dashboard.GET("/operation-stats", dashboardHandler.GetOperationStats)
	}

	// 内容管理模块
	contentRepo := repository.NewContentRepository(db)
	contentService := service.NewContentService(contentRepo)
	contentHandler := handler.NewContentHandler(contentService)

	// 轮播图管理 - 需要认证的操作
	swipers := api.Group("/swipers")
	swipers.Use(middleware.JWTAuth(cfg))
	{
		swipers.POST("", contentHandler.CreateSwiper)
		swipers.PUT("/:id", contentHandler.UpdateSwiper)
		swipers.DELETE("/:id", contentHandler.DeleteSwiper)
		swipers.PATCH("/:id/order", contentHandler.UpdateSwiperOrder)
	}

	// 新闻管理 - 需要认证的操作
	news := api.Group("/news")
	news.Use(middleware.JWTAuth(cfg))
	{
		news.POST("", contentHandler.CreateNews)
		news.PUT("/:id", contentHandler.UpdateNews)
		news.DELETE("/:id", contentHandler.DeleteNews)
		news.PATCH("/:id/home-page", contentHandler.SetNewsHomePage)
	}

	// 内容管理 - 需要认证的操作
	services := api.Group("/services")
	services.Use(middleware.JWTAuth(cfg))
	{
		services.POST("", contentHandler.CreateService)
		services.PUT("/:id", contentHandler.UpdateService)
		services.DELETE("/:id", contentHandler.DeleteService)
		services.PATCH("/:id/order", contentHandler.UpdateServiceOrder)
	}

	// 项目案例管理 - 需要认证的操作
	projectCases := api.Group("/project-cases")
	projectCases.Use(middleware.JWTAuth(cfg))
	{
		projectCases.POST("", contentHandler.CreateProjectCase)
		projectCases.PUT("/:id", contentHandler.UpdateProjectCase)
		projectCases.DELETE("/:id", contentHandler.DeleteProjectCase)
		projectCases.PATCH("/:id/sort", contentHandler.UpdateProjectCaseSort)
	}

	// 合作伙伴管理 - 需要认证的操作
	partners := api.Group("/partners")
	partners.Use(middleware.JWTAuth(cfg))
	{
		partners.POST("", contentHandler.CreatePartner)
		partners.PUT("/:id", contentHandler.UpdatePartner)
		partners.DELETE("/:id", contentHandler.DeletePartner)
	}

	// 友情链接管理 - 需要认证的操作
	friendlinks := api.Group("/friendlinks")
	friendlinks.Use(middleware.JWTAuth(cfg))
	{
		friendlinks.POST("", contentHandler.CreateFriendLink)
		friendlinks.PUT("/:id", contentHandler.UpdateFriendLink)
		friendlinks.DELETE("/:id", contentHandler.DeleteFriendLink)
		friendlinks.PATCH("/:id/order", contentHandler.UpdateFriendLinkOrder)
	}

	// 招聘信息管理 - 需要认证的操作
	recruitments := api.Group("/recruitments")
	recruitments.Use(middleware.JWTAuth(cfg))
	{
		recruitments.POST("", contentHandler.CreateRecruitment)
		recruitments.PUT("/:id", contentHandler.UpdateRecruitment)
		recruitments.DELETE("/:id", contentHandler.DeleteRecruitment)
	}

	// 平台管理 - 需要认证的操作
	partPlatforms := api.Group("/part-platforms")
	partPlatforms.Use(middleware.JWTAuth(cfg))
	{
		partPlatforms.POST("", contentHandler.CreatePartPlatform)
		partPlatforms.PUT("/:id", contentHandler.UpdatePartPlatform)
		partPlatforms.DELETE("/:id", contentHandler.DeletePartPlatform)
	}

	// 文件上传模块
	if cfg.COS.SecretID != "" && cfg.COS.SecretKey != "" && cfg.COS.Bucket != "" {
		cosConfig := &cos.Config{
			SecretID:  cfg.COS.SecretID,
			SecretKey: cfg.COS.SecretKey,
			Region:    cfg.COS.Region,
			Bucket:    cfg.COS.Bucket,
			Domain:    cfg.COS.Domain,
		}

		cosClient, err := cos.NewClient(cosConfig)
		if err != nil {
			// 如果COS配置错误，记录日志但不影响其他功能
			fmt.Printf("COS客户端初始化失败: %v\n", err)
		} else {
			// 为ContentService设置COS客户端，支持文件清理
			contentService.SetCosClient(cosClient)

			fileService := service.NewFileService(contentRepo, cosClient, cfg)
			fileHandler := handler.NewFileHandler(fileService)

			// 文件上传路由 - 需要认证
			files := api.Group("/files")
			files.Use(middleware.JWTAuth(cfg))
			{
				files.POST("/upload", fileHandler.UploadFile)
				files.DELETE("/:id", fileHandler.DeleteFile)
				files.GET("/:id", fileHandler.GetFile)
				files.GET("", fileHandler.GetMyFiles)
				files.GET("/module/:module", fileHandler.GetFilesByModule)
			}

			// 上传配置路由（公开访问）
			api.GET("/upload/config", fileHandler.GetUploadConfig)
		}

	}
}
