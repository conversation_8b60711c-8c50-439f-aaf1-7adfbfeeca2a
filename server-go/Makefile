# 江苏蔚之领域智能科技有限公司 Go 后端服务器 Makefile

# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASS=Ydb3344%
DB_NAME=weizhi

# MySQL连接选项
MYSQL_CMD=mysql -h $(DB_HOST) -P $(DB_PORT) -u $(DB_USER) -p'$(DB_PASS)' --skip-ssl

# 项目配置
APP_NAME=weishi-server
BINARY_PATH=./main
CONFIG_FILE=config.yaml

.PHONY: help build run clean test deps setup-database setup-database-skip-admin create-database init-tables drop-database init-db seed-data init-admin reset-db logs start stop restart status

# 默认目标
help: ## 显示帮助信息
	@echo "江苏蔚之领域智能科技有限公司 Go 后端服务器"
	@echo ""
	@echo "可用命令:"
	@grep -E '^[a-zA-Z_-]+:.*?## .*$$' $(MAKEFILE_LIST) | sort | awk 'BEGIN {FS = ":.*?## "}; {printf "\033[36m%-20s\033[0m %s\n", $$1, $$2}'

# 项目管理
build: ## 编译项目
	@echo "编译 $(APP_NAME)..."
	@go build -o $(BINARY_PATH) ./cmd
	@echo "编译完成: $(BINARY_PATH)"

run: build ## 编译并运行服务器
	@echo "启动 $(APP_NAME) 服务器..."
	@./$(BINARY_PATH)

start: build ## 后台启动服务器
	@echo "后台启动 $(APP_NAME) 服务器..."
	@nohup ./$(BINARY_PATH) > server.log 2>&1 &
	@echo "服务器已启动，日志文件: server.log"

stop: ## 停止服务器
	@echo "停止 $(APP_NAME) 服务器..."
	@pkill -f "./$(BINARY_PATH)" || echo "服务器未运行"

restart: stop start ## 重启服务器

status: ## 查看服务器状态
	@echo "检查 $(APP_NAME) 服务器状态..."
	@ps aux | grep "./$(BINARY_PATH)" | grep -v grep || echo "服务器未运行"
	@echo ""
	@echo "健康检查:"
	@curl -s http://localhost:3000/api/health || echo "服务器无响应"

logs: ## 查看服务器日志
	@tail -f server.log

clean: ## 清理编译文件
	@echo "清理编译文件..."
	@rm -f $(BINARY_PATH)
	@rm -f server.log
	@echo "清理完成"

test: ## 运行测试
	@echo "运行测试..."
	@go test ./...

deps: ## 安装依赖
	@echo "安装项目依赖..."
	@go mod tidy
	@go mod download

# 数据库管理
check-db: ## 检查数据库连接
	@echo "检查数据库连接..."
	@$(MYSQL_CMD) -e "SELECT 'Database connection successful!' as status;" $(DB_NAME)

# setup-database: ## 完整设置数据库 (创建数据库+表结构+管理员数据+业务数据)
# 	@echo "开始完整数据库设置..."
# 	@echo "1. 创建数据库..."
# 	@$(MYSQL_CMD) < scripts/create_database.sql
# 	@echo "2. 初始化表结构..."
# 	@echo 'package main\n\nimport (\n    "fmt"\n    "log"\n    "weishi-server/internal/config"\n    "weishi-server/internal/database"\n)\n\nfunc main() {\n    cfg := config.New()\n    _, err := database.New(cfg.Database)\n    if err != nil {\n        log.Fatalf("Failed to connect to database: %v", err)\n    }\n    fmt.Println("Database migration completed successfully!")\n}' > temp_migrate.go
# 	@go run temp_migrate.go
# 	@rm -f temp_migrate.go
# 	@echo "3. 导入管理员数据..."
# 	@$(MYSQL_CMD) $(DB_NAME) < scripts/init_admin.sql
# 	@echo "4. 导入业务数据..."
# 	@$(MYSQL_CMD) $(DB_NAME) < scripts/seed_data.sql
# 	@echo ""
# 	@echo "🎉 数据库设置完成！"
# 	@echo ""
# 	@echo "管理员登录信息:"
# 	@echo "  用户名: admin"
# 	@echo "  密码: admin123"
# 	@echo ""

# setup-database-skip-admin: ## 设置数据库但跳过管理员数据
# 	@echo "开始数据库设置 (跳过管理员数据)..."
# 	@echo "1. 创建数据库..."
# 	@$(MYSQL_CMD) < scripts/create_database.sql
# 	@echo "2. 初始化表结构..."
# 	@echo 'package main\n\nimport (\n    "fmt"\n    "log"\n    "weishi-server/internal/config"\n    "weishi-server/internal/database"\n)\n\nfunc main() {\n    cfg := config.New()\n    _, err := database.New(cfg.Database)\n    if err != nil {\n        log.Fatalf("Failed to connect to database: %v", err)\n    }\n    fmt.Println("Database migration completed successfully!")\n}' > temp_migrate.go
# 	@go run temp_migrate.go
# 	@rm -f temp_migrate.go
# 	@echo "3. 导入业务数据..."
# 	@$(MYSQL_CMD) $(DB_NAME) < scripts/seed_data.sql
# 	@echo "数据库设置完成 (已跳过管理员数据)"

create-database: ## 仅创建数据库 (会删除已存在的数据库)
	@echo "创建数据库 (会删除已存在的数据库)..."
	@$(MYSQL_CMD) < scripts/create_database.sql
	@echo "数据库 $(DB_NAME) 创建完成"

# drop-database: ## 删除数据库 (危险操作)
# 	@echo "⚠️  警告: 即将删除数据库 $(DB_NAME)"
# 	@echo "按 Ctrl+C 取消，或按 Enter 继续..."
# 	@read
# 	@$(MYSQL_CMD) -e "DROP DATABASE IF EXISTS $(DB_NAME);"
# 	@echo "数据库 $(DB_NAME) 已删除"

# init-tables: ## 初始化表结构 (使用Go程序迁移)
# 	@echo "初始化表结构..."
# 	@echo 'package main\n\nimport (\n    "fmt"\n    "log"\n    "weishi-server/internal/config"\n    "weishi-server/internal/database"\n)\n\nfunc main() {\n    cfg := config.New()\n    _, err := database.New(cfg.Database)\n    if err != nil {\n        log.Fatalf("Failed to connect to database: %v", err)\n    }\n    fmt.Println("Database migration completed successfully!")\n}' > temp_migrate.go
# 	@go run temp_migrate.go
# 	@rm -f temp_migrate.go
# 	@echo "表结构初始化完成"

init-admin: ## 初始化管理员数据
	@echo "初始化管理员数据..."
	@$(MYSQL_CMD) $(DB_NAME) < scripts/init_admin.sql
	@echo "管理员数据初始化完成"
	@echo "默认账号: admin"
	@echo "默认密码: admin123"

seed-data: ## 导入业务种子数据
	@echo "导入业务种子数据..."
	@$(MYSQL_CMD) $(DB_NAME) < scripts/seed_data.sql
	@echo "业务数据导入完成"

init-db: init-admin seed-data ## 完整初始化数据库 (管理员 + 业务数据)
	@echo "数据库初始化完成！"

reset-admin: ## 重置管理员数据
	@echo "重置管理员数据..."
	@$(MYSQL_CMD) -e "TRUNCATE TABLE admin_logs; TRUNCATE TABLE role_permissions; TRUNCATE TABLE admin_user_roles; TRUNCATE TABLE permissions; TRUNCATE TABLE roles; TRUNCATE TABLE admin_users;" $(DB_NAME)
	@$(MYSQL_CMD) < scripts/init_admin.sql
	@echo "管理员数据重置完成"

reset-business: ## 重置业务数据
	@echo "重置业务数据..."
	@$(MYSQL_CMD) -e "TRUNCATE TABLE friend_links; TRUNCATE TABLE partners; TRUNCATE TABLE part_platform; TRUNCATE TABLE project_cases; TRUNCATE TABLE our_services; TRUNCATE TABLE swipers; TRUNCATE TABLE recruitments;" $(DB_NAME)
	@$(MYSQL_CMD) < scripts/seed_data.sql
	@echo "业务数据重置完成"

reset-db: reset-admin reset-business ## 重置所有数据
	@echo "所有数据重置完成！"

backup-db: ## 备份数据库
	@echo "备份数据库..."
	@mysqldump -h $(DB_HOST) -P $(DB_PORT) -u $(DB_USER) -p'$(DB_PASS)' --skip-ssl $(DB_NAME) > backup_$(shell date +%Y%m%d_%H%M%S).sql
	@echo "数据库备份完成"

# API测试
test-health: ## 测试健康检查接口
	@echo "测试健康检查接口..."
	@curl -X GET http://localhost:3000/api/health

test-captcha: ## 测试验证码接口
	@echo "测试验证码接口..."
	@curl -X GET http://localhost:3000/api/captcha

test-login: ## 测试登录接口
	@echo "测试登录接口..."
	@curl -X POST http://localhost:3000/api/admin/auth/login \
		-H "Content-Type: application/json" \
		-d '{"username":"admin","password":"password","captcha":"3","captcha_id":"test"}'

test-api: test-health test-captcha test-login ## 运行基础API测试

# 开发工具
dev: ## 开发模式 (自动重启)
	@echo "开发模式启动..."
	@which air > /dev/null || (echo "请先安装air: go install github.com/cosmtrek/air@latest" && exit 1)
	@air

format: ## 格式化代码
	@echo "格式化代码..."
	@go fmt ./...
	@echo "代码格式化完成"

lint: ## 代码检查
	@echo "代码检查..."
	@which golangci-lint > /dev/null || (echo "请先安装golangci-lint" && exit 1)
	@golangci-lint run

# 生产部署
docker-build: ## 构建Docker镜像
	@echo "构建Docker镜像..."
	@docker build -t $(APP_NAME):latest .

docker-run: ## 运行Docker容器
	@echo "运行Docker容器..."
	@docker run -d --name $(APP_NAME) -p 3000:3000 $(APP_NAME):latest

# 监控
monitor: ## 实时监控服务器状态
	@echo "实时监控服务器状态 (按Ctrl+C退出)..."
	@while true; do \
		clear; \
		echo "=== $(APP_NAME) 服务器监控 ==="; \
		echo "时间: $$(date)"; \
		echo ""; \
		echo "进程状态:"; \
		ps aux | grep "./$(BINARY_PATH)" | grep -v grep || echo "服务器未运行"; \
		echo ""; \
		echo "内存使用:"; \
		ps aux | grep "./$(BINARY_PATH)" | grep -v grep | awk '{print "CPU: " $$3 "%, MEM: " $$4 "%"}' || echo "N/A"; \
		echo ""; \
		echo "端口监听:"; \
		netstat -tlnp 2>/dev/null | grep :3000 || echo "端口3000未监听"; \
		echo ""; \
		echo "最近日志:"; \
		tail -5 server.log 2>/dev/null || echo "无日志文件"; \
		sleep 5; \
	done

# 快速开始
# quick-start: deps build setup-database start ## 快速开始 (安装依赖+编译+完整数据库设置+启动)
# 	@echo ""
# 	@echo "🎉 $(APP_NAME) 快速启动完成！"
# 	@echo ""
# 	@echo "服务信息:"
# 	@echo "  - 服务地址: http://localhost:3000"
# 	@echo "  - 健康检查: http://localhost:3000/api/health"
# 	@echo "  - 管理员账号: admin"
# 	@echo "  - 管理员密码: admin123"
# 	@echo ""
# 	@echo "常用命令:"
# 	@echo "  - 查看状态: make status"
# 	@echo "  - 查看日志: make logs"
# 	@echo "  - 停止服务: make stop"
# 	@echo "  - 重启服务: make restart" 