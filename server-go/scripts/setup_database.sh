#!/bin/bash

# 数据库设置脚本
# 用于创建数据库和初始化数据

set -e  # 遇到错误时退出

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查MySQL是否安装和运行
check_mysql() {
    print_info "检查MySQL服务状态..."
    
    if ! command -v mysql &> /dev/null; then
        print_error "MySQL客户端未安装，请先安装MySQL"
        exit 1
    fi
    
    print_success "MySQL客户端已安装"
}

# 测试数据库连接
test_database_connection() {
    print_info "测试数据库连接..."
    
    # 尝试连接数据库（禁用SSL）
    if mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USERNAME" -p"$DB_PASSWORD" --ssl-mode=DISABLED -e "SELECT 1;" &> /dev/null; then
        print_success "数据库连接成功（SSL禁用）"
        MYSQL_OPTIONS="--ssl-mode=DISABLED"
    elif mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USERNAME" -p"$DB_PASSWORD" --skip-ssl -e "SELECT 1;" &> /dev/null; then
        print_success "数据库连接成功（跳过SSL）"
        MYSQL_OPTIONS="--skip-ssl"
    elif mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USERNAME" -p"$DB_PASSWORD" -e "SELECT 1;" &> /dev/null; then
        print_success "数据库连接成功（默认SSL）"
        MYSQL_OPTIONS=""
    else
        print_error "数据库连接失败，请检查配置和MySQL服务状态"
        print_info "请确认："
        print_info "  1. MySQL服务已启动"
        print_info "  2. 用户名和密码正确"
        print_info "  3. 数据库主机和端口可访问"
        exit 1
    fi
}

# 从配置文件读取数据库配置
read_config() {
    print_info "读取数据库配置..."
    
    if [ ! -f "config.yaml" ]; then
        print_error "配置文件 config.yaml 不存在"
        exit 1
    fi
    
    # 使用yq或者grep读取配置（这里使用grep的简单方法）
    DB_HOST=$(grep -A 10 "database:" config.yaml | grep "host:" | awk '{print $2}' | tr -d '"')
    DB_PORT=$(grep -A 10 "database:" config.yaml | grep "port:" | awk '{print $2}' | tr -d '"')
    DB_USERNAME=$(grep -A 10 "database:" config.yaml | grep "username:" | awk '{print $2}' | tr -d '"')
    DB_PASSWORD=$(grep -A 10 "database:" config.yaml | grep "password:" | awk '{print $2}' | tr -d '"')
    DB_DATABASE=$(grep -A 10 "database:" config.yaml | grep "database:" | awk '{print $2}' | tr -d '"')
    
    print_info "数据库主机: $DB_HOST"
    print_info "数据库端口: $DB_PORT" 
    print_info "数据库用户: $DB_USERNAME"
    print_info "数据库名称: $DB_DATABASE"
}

# 创建数据库
create_database() {
    print_info "创建数据库 $DB_DATABASE..."
    
    # 使用测试时确定的MySQL选项执行SQL脚本
    mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USERNAME" -p"$DB_PASSWORD" $MYSQL_OPTIONS < scripts/create_database.sql
    
    if [ $? -eq 0 ]; then
        print_success "数据库创建成功"
    else
        print_error "数据库创建失败，请检查MySQL配置"
        exit 1
    fi
}

# 初始化数据表结构（使用Go程序迁移）
init_tables() {
    print_info "初始化数据表结构..."
    
    # 确保在正确的目录
    if [ ! -f "go.mod" ]; then
        print_error "请在Go项目根目录运行此脚本"
        exit 1
    fi
    
    # 构建临时迁移程序
    cat > temp_migrate.go << 'EOF'
package main

import (
    "fmt"
    "log"
    "weishi-server/internal/config"
    "weishi-server/internal/database"
)

func main() {
    // 初始化配置
    cfg := config.New()

    // 初始化数据库连接（会自动执行迁移）
    _, err := database.New(cfg.Database)
    if err != nil {
        log.Fatalf("Failed to connect to database: %v", err)
    }

    fmt.Println("Database migration completed successfully!")
}
EOF
    
    # 运行迁移
    go run temp_migrate.go
    
    if [ $? -eq 0 ]; then
        print_success "数据表结构初始化成功"
    else
        print_error "数据表结构初始化失败"
        rm -f temp_migrate.go
        exit 1
    fi
    
    # 清理临时文件
    rm -f temp_migrate.go
}

# 导入管理员初始化数据
import_admin_data() {
    print_info "导入管理员初始化数据..."
    
    # 检查管理员初始化数据文件是否存在
    if [ ! -f "scripts/init_admin.sql" ]; then
        print_error "管理员初始化数据文件 scripts/init_admin.sql 不存在"
        exit 1
    fi
    
    # 使用测试时确定的MySQL选项导入管理员初始化数据
    mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USERNAME" -p"$DB_PASSWORD" $MYSQL_OPTIONS < scripts/init_admin.sql
    
    if [ $? -eq 0 ]; then
        print_success "管理员初始化数据导入成功"
    else
        print_error "管理员初始化数据导入失败"
        exit 1
    fi
}

# 导入种子数据
import_seed_data() {
    print_info "导入业务种子数据..."
    
    # 检查种子数据文件是否存在
    if [ ! -f "scripts/seed_data.sql" ]; then
        print_error "种子数据文件 scripts/seed_data.sql 不存在"
        exit 1
    fi
    
    # 使用测试时确定的MySQL选项导入种子数据
    mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USERNAME" -p"$DB_PASSWORD" $MYSQL_OPTIONS < scripts/seed_data.sql
    
    if [ $? -eq 0 ]; then
        print_success "业务种子数据导入成功"
    else
        print_error "业务种子数据导入失败"
        exit 1
    fi
}

# 主函数
main() {
    echo -e "${BLUE}================================${NC}"
    echo -e "${BLUE}  蔚之领域数据库设置脚本${NC}"
    echo -e "${BLUE}================================${NC}"
    echo ""
    
    # 检查参数
    SKIP_DB_CREATE=false
    SKIP_TABLE_INIT=false
    SKIP_ADMIN_DATA=false
    SKIP_SEED_DATA=false
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            --skip-db-create)
                SKIP_DB_CREATE=true
                shift
                ;;
            --skip-table-init)
                SKIP_TABLE_INIT=true
                shift
                ;;
            --skip-admin-data)
                SKIP_ADMIN_DATA=true
                shift
                ;;
            --skip-seed-data)
                SKIP_SEED_DATA=true
                shift
                ;;
            --help|-h)
                echo "用法: $0 [选项]"
                echo ""
                echo "选项:"
                echo "  --skip-db-create    跳过数据库创建"
                echo "  --skip-table-init   跳过数据表结构初始化"
                echo "  --skip-admin-data   跳过管理员初始化数据导入"
                echo "  --skip-seed-data    跳过业务种子数据导入"
                echo "  --help, -h          显示此帮助信息"
                exit 0
                ;;
            *)
                print_error "未知参数: $1"
                echo "使用 --help 查看可用选项"
                exit 1
                ;;
        esac
    done
    
    # 执行步骤
    check_mysql
    read_config
    test_database_connection
    
    if [ "$SKIP_DB_CREATE" = false ]; then
        create_database
    else
        print_warning "跳过数据库创建"
    fi
    
    if [ "$SKIP_TABLE_INIT" = false ]; then
        init_tables
    else
        print_warning "跳过数据表结构初始化"
    fi
    
    if [ "$SKIP_ADMIN_DATA" = false ]; then
        import_admin_data
    else
        print_warning "跳过管理员初始化数据导入"
    fi
    
    if [ "$SKIP_SEED_DATA" = false ]; then
        import_seed_data
    else
        print_warning "跳过业务种子数据导入"
    fi
    
    echo ""
    print_success "数据库设置完成！"
    echo ""
    echo -e "${YELLOW}数据库已包含以下数据：${NC}"
    if [ "$SKIP_ADMIN_DATA" = false ]; then
        echo -e "${YELLOW}  管理员数据：${NC}"
        echo -e "${YELLOW}    - 管理员用户（admin/admin123）${NC}"
        echo -e "${YELLOW}    - 角色权限配置${NC}"
        echo -e "${YELLOW}    - 系统权限数据${NC}"
        echo -e "${YELLOW}    - 操作日志示例${NC}"
    fi
    if [ "$SKIP_SEED_DATA" = false ]; then
        echo -e "${YELLOW}  业务数据：${NC}"
        echo -e "${YELLOW}    - 友情链接数据${NC}"
        echo -e "${YELLOW}    - 合作伙伴数据${NC}"
        echo -e "${YELLOW}    - 零件平台数据${NC}"
        echo -e "${YELLOW}    - 项目案例数据${NC}"
        echo -e "${YELLOW}    - 服务配置数据${NC}"
        echo -e "${YELLOW}    - 轮播图数据${NC}"
        echo -e "${YELLOW}    - 招聘信息数据${NC}"
    fi
    echo ""
    if [ "$SKIP_ADMIN_DATA" = false ]; then
        echo -e "${GREEN}管理员登录信息：${NC}"
        echo -e "${GREEN}  用户名: admin${NC}"
        echo -e "${GREEN}  密码: admin123${NC}"
        echo -e "${GREEN}  管理后台: http://localhost:3001/admin${NC}"
        echo ""
    fi
    echo -e "${BLUE}数据库初始化完成，可以开始使用！${NC}"
}

# 执行主函数
main "$@" 