#!/bin/bash

# 容器内数据导入脚本
# 使用环境变量配置，支持在 Docker 容器内运行

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 环境变量配置（使用默认值）
DB_HOST=${DB_HOST:-mysql}
DB_PORT=${DB_PORT:-3306}
DB_USERNAME=${DB_USERNAME:-root}
DB_PASSWORD=${DB_PASSWORD:-${MYSQL_ROOT_PASSWORD}}
DB_DATABASE=${DB_DATABASE:-${MYSQL_DATABASE:-weizhi}}

# 数据导入选项
IMPORT_FULL_DATA=${IMPORT_FULL_DATA:-false}

# 脚本目录
SCRIPT_DIR="/app/scripts"

# 检查环境变量
check_env() {
    log_info "检查环境变量..."
    
    if [ -z "$DB_PASSWORD" ]; then
        log_error "数据库密码未设置 (DB_PASSWORD 或 MYSQL_ROOT_PASSWORD)"
        exit 1
    fi
    
    log_info "数据库配置:"
    log_info "  主机: $DB_HOST:$DB_PORT"
    log_info "  用户: $DB_USERNAME"
    log_info "  数据库: $DB_DATABASE"
    log_info "导入选项:"
    log_info "  完整数据: $IMPORT_FULL_DATA"
}

# 等待数据库连接
wait_for_db() {
    log_info "等待数据库连接..."

    local max_attempts=60  # 增加到60次，总共120秒
    local attempt=1

    while [ $attempt -le $max_attempts ]; do
        if mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USERNAME" -p"$DB_PASSWORD" -e "SELECT 1;" >/dev/null 2>&1; then
            log_success "数据库连接成功"
            return 0
        fi

        log_info "等待数据库连接... ($attempt/$max_attempts)"
        sleep 2
        attempt=$((attempt + 1))
    done

    log_error "数据库连接超时"
    log_error "请检查数据库服务是否正常运行"
    log_error "数据库配置: $DB_HOST:$DB_PORT, 用户: $DB_USERNAME, 数据库: $DB_DATABASE"
    exit 1
}

# 检查数据库是否存在
check_database() {
    log_info "检查数据库 $DB_DATABASE..."
    
    if ! mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USERNAME" -p"$DB_PASSWORD" -e "USE $DB_DATABASE; SELECT 1;" >/dev/null 2>&1; then
        log_error "数据库 $DB_DATABASE 不存在"
        exit 1
    fi
    
    log_success "数据库检查通过"
}

# 检查表结构
check_tables() {
    log_info "检查数据库表结构..."
    
    local tables=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USERNAME" -p"$DB_PASSWORD" -e "USE $DB_DATABASE; SHOW TABLES;" -s -N 2>/dev/null | wc -l)
    
    if [ "$tables" -lt 5 ]; then
        log_warning "数据库表较少（当前: $tables 个表），可能需要等待应用初始化"
    else
        log_success "数据库表结构正常（共 $tables 个表）"
    fi
}

# 检查是否已有数据
has_existing_data() {
    local tables=("admin_users" "our_services" "swipers")
    
    for table in "${tables[@]}"; do
        local count=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USERNAME" -p"$DB_PASSWORD" -e "USE $DB_DATABASE; SELECT COUNT(*) FROM $table;" -s -N 2>/dev/null || echo "0")
        if [ "$count" -gt 0 ]; then
            log_info "表 $table 已有 $count 条数据"
            return 0
        fi
    done
    
    return 1
}

# 导入SQL文件
import_sql_file() {
    local sql_file="$1"
    local description="$2"
    
    if [ ! -f "$sql_file" ]; then
        log_warning "文件不存在: $sql_file"
        return 1
    fi
    
    log_info "导入 $description: $(basename "$sql_file")"
    
    if mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USERNAME" -p"$DB_PASSWORD" "$DB_DATABASE" < "$sql_file"; then
        log_success "$description 导入完成"
        return 0
    else
        log_error "$description 导入失败"
        return 1
    fi
}



# 导入完整数据
import_full_data() {
    log_info "开始导入完整数据..."
    
    local full_files=(
        "$SCRIPT_DIR/database_full.sql"
        "$SCRIPT_DIR/full_data.sql"
    )
    
    for file in "${full_files[@]}"; do
        if [ -f "$file" ]; then
            import_sql_file "$file" "完整数据"
            return $?
        fi
    done
    
    log_error "未找到完整数据文件"
    return 1
}

# 显示数据统计
show_stats() {
    log_info "数据库统计:"
    
    local tables=("admin_users" "our_services" "swipers" "news" "products" "cases")
    
    for table in "${tables[@]}"; do
        local count=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USERNAME" -p"$DB_PASSWORD" -e "USE $DB_DATABASE; SELECT COUNT(*) FROM $table;" -s -N 2>/dev/null || echo "0")
        log_info "  $table: $count 条"
    done
}

# 主函数
main() {
    log_info "=== 数据导入脚本 ==="
    
    # 检查环境
    check_env
    wait_for_db
    check_database
    check_tables
    
    # 检查是否需要导入
    if [ "$IMPORT_SEED_DATA" != "true" ] && [ "$IMPORT_ADMIN_DATA" != "true" ] && [ "$IMPORT_FULL_DATA" != "true" ]; then
        log_info "未启用任何数据导入选项，退出"
        exit 0
    fi
    
    # 检查现有数据
    if has_existing_data; then
        log_warning "检测到现有数据，跳过导入以避免重复"
        show_stats
        exit 0
    fi
    
    # 执行导入
    local import_success=false

    if [ "$IMPORT_FULL_DATA" = "true" ]; then
        if import_full_data; then
            import_success=true
        fi
    else
        log_warning "未启用数据导入选项"
    fi
    
    # 显示结果
    if [ "$import_success" = "true" ]; then
        log_success "数据导入完成！"
        show_stats
    else
        log_warning "数据导入未完全成功，请检查日志"
    fi
}

# 执行主函数
main "$@"
