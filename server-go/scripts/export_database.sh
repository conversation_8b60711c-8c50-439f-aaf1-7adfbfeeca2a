#!/bin/bash

# 数据库导出脚本
# 功能：导出数据库结构和数据，但排除 admin 用户相关数据
# 作者：蔚之领域项目组
# 日期：2025-07-21

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置变量
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
DOCKER_ENV_FILE="$PROJECT_ROOT/../docker.env"

# 默认配置
DEFAULT_HOST="localhost"
DEFAULT_PORT="3307"
DEFAULT_DATABASE="weizhi"
DEFAULT_OUTPUT_DIR="$SCRIPT_DIR"

# 函数：打印带颜色的消息
print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# 函数：显示帮助信息
show_help() {
    echo "数据库导出脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help              显示此帮助信息"
    echo "  -H, --host HOST         数据库主机 (默认: $DEFAULT_HOST)"
    echo "  -P, --port PORT         数据库端口 (默认: $DEFAULT_PORT)"
    echo "  -d, --database DB       数据库名称 (默认: $DEFAULT_DATABASE)"
    echo "  -u, --user USER         数据库用户名"
    echo "  -p, --password PASS     数据库密码"
    echo "  -o, --output DIR        输出目录 (默认: $DEFAULT_OUTPUT_DIR)"
    echo "  --structure-only        仅导出结构，不导出数据"
    echo "  --data-only             仅导出数据，不导出结构"
    echo "  --include-admin         包含 admin 用户数据（默认排除）"
    echo "  --docker                使用 Docker 容器导出"
    echo ""
    echo "示例:"
    echo "  $0 --docker                           # 使用 Docker 容器导出"
    echo "  $0 -H localhost -P 3307 -u root       # 指定连接参数"
    echo "  $0 --structure-only                   # 仅导出结构"
    echo "  $0 --include-admin                    # 包含 admin 用户数据"
}

# 函数：从 docker.env 文件读取配置
load_docker_env() {
    if [[ -f "$DOCKER_ENV_FILE" ]]; then
        print_message $BLUE "📄 从 $DOCKER_ENV_FILE 读取配置..."
        
        # 读取数据库配置
        DB_HOST=$(grep "^MYSQL_HOST=" "$DOCKER_ENV_FILE" 2>/dev/null | cut -d'=' -f2 || echo "$DEFAULT_HOST")
        DB_PORT=$(grep "^MYSQL_PORT=" "$DOCKER_ENV_FILE" 2>/dev/null | cut -d'=' -f2 || echo "$DEFAULT_PORT")
        DB_NAME=$(grep "^MYSQL_DATABASE=" "$DOCKER_ENV_FILE" 2>/dev/null | cut -d'=' -f2 || echo "$DEFAULT_DATABASE")
        DB_USER=$(grep "^MYSQL_USER=" "$DOCKER_ENV_FILE" 2>/dev/null | cut -d'=' -f2 || echo "root")
        DB_PASSWORD=$(grep "^MYSQL_ROOT_PASSWORD=" "$DOCKER_ENV_FILE" 2>/dev/null | cut -d'=' -f2)
        
        # 如果没有找到 root 密码，尝试普通用户密码
        if [[ -z "$DB_PASSWORD" ]]; then
            DB_PASSWORD=$(grep "^MYSQL_PASSWORD=" "$DOCKER_ENV_FILE" 2>/dev/null | cut -d'=' -f2)
        fi
    else
        print_message $YELLOW "⚠️  未找到 docker.env 文件，使用默认配置"
        DB_HOST="$DEFAULT_HOST"
        DB_PORT="$DEFAULT_PORT"
        DB_NAME="$DEFAULT_DATABASE"
        DB_USER="root"
        DB_PASSWORD=""
    fi
}

# 函数：验证数据库连接
test_connection() {
    local test_cmd
    
    if [[ "$USE_DOCKER" == "true" ]]; then
        test_cmd="docker exec weishi-mysql mysql -u$DB_USER"
        if [[ -n "$DB_PASSWORD" ]]; then
            test_cmd="$test_cmd -p'$DB_PASSWORD'"
        fi
        test_cmd="$test_cmd -e 'SELECT 1;' $DB_NAME"
    else
        test_cmd="mysql -h$DB_HOST -P$DB_PORT -u$DB_USER"
        if [[ -n "$DB_PASSWORD" ]]; then
            test_cmd="$test_cmd -p'$DB_PASSWORD'"
        fi
        test_cmd="$test_cmd -e 'SELECT 1;' $DB_NAME"
    fi
    
    print_message $BLUE "🔍 测试数据库连接..."
    if eval "$test_cmd" >/dev/null 2>&1; then
        print_message $GREEN "✅ 数据库连接成功"
        return 0
    else
        print_message $RED "❌ 数据库连接失败"
        return 1
    fi
}

# 函数：导出数据库结构
export_structure() {
    local output_file="$OUTPUT_DIR/database_structure.sql"
    local dump_cmd
    
    print_message $BLUE "📋 导出数据库结构..."
    
    if [[ "$USE_DOCKER" == "true" ]]; then
        dump_cmd="docker exec weishi-mysql mysqldump -u$DB_USER"
        if [[ -n "$DB_PASSWORD" ]]; then
            dump_cmd="$dump_cmd -p'$DB_PASSWORD'"
        fi
        dump_cmd="$dump_cmd --no-data --routines --triggers $DB_NAME"
    else
        dump_cmd="mysqldump -h$DB_HOST -P$DB_PORT -u$DB_USER"
        if [[ -n "$DB_PASSWORD" ]]; then
            dump_cmd="$dump_cmd -p'$DB_PASSWORD'"
        fi
        dump_cmd="$dump_cmd --no-data --routines --triggers $DB_NAME"
    fi
    
    if eval "$dump_cmd" > "$output_file" 2>/dev/null; then
        print_message $GREEN "✅ 数据库结构导出成功: $output_file"
    else
        print_message $RED "❌ 数据库结构导出失败"
        return 1
    fi
}

# 函数：导出数据（排除 admin 用户数据）
export_data() {
    local output_file="$OUTPUT_DIR/database_data.sql"
    local dump_cmd
    local exclude_conditions=""
    
    print_message $BLUE "📊 导出数据库数据..."
    
    # 如果不包含 admin 数据，添加排除条件
    if [[ "$INCLUDE_ADMIN" != "true" ]]; then
        print_message $YELLOW "⚠️  排除 admin 用户相关数据"
        exclude_conditions="--where=\"username != 'admin'\""
    fi
    
    if [[ "$USE_DOCKER" == "true" ]]; then
        dump_cmd="docker exec weishi-mysql mysqldump -u$DB_USER"
        if [[ -n "$DB_PASSWORD" ]]; then
            dump_cmd="$dump_cmd -p'$DB_PASSWORD'"
        fi
        dump_cmd="$dump_cmd --no-create-info --skip-triggers"
    else
        dump_cmd="mysqldump -h$DB_HOST -P$DB_PORT -u$DB_USER"
        if [[ -n "$DB_PASSWORD" ]]; then
            dump_cmd="$dump_cmd -p'$DB_PASSWORD'"
        fi
        dump_cmd="$dump_cmd --no-create-info --skip-triggers"
    fi
    
    # 创建临时文件
    local temp_file=$(mktemp)
    
    # 导出所有表的数据，但对特定表应用过滤条件
    {
        echo "-- 数据库数据导出"
        echo "-- 生成时间: $(date)"
        echo "-- 数据库: $DB_NAME"
        if [[ "$INCLUDE_ADMIN" != "true" ]]; then
            echo "-- 注意: 已排除 admin 用户相关数据"
        fi
        echo ""
        
        # 导出不包含 admin 数据的表
        if [[ "$INCLUDE_ADMIN" != "true" ]]; then
            # admin_users 表 - 排除 admin 用户
            eval "$dump_cmd --where=\"username != 'admin'\" $DB_NAME admin_users" 2>/dev/null || true
            
            # admin_user_roles 表 - 排除 admin 用户的角色关联
            eval "$dump_cmd --where=\"admin_user_id NOT IN (SELECT id FROM admin_users WHERE username = 'admin')\" $DB_NAME admin_user_roles" 2>/dev/null || true
            
            # admin_logs 表 - 排除 admin 用户的日志
            eval "$dump_cmd --where=\"admin_user_id NOT IN (SELECT id FROM admin_users WHERE username = 'admin')\" $DB_NAME admin_logs" 2>/dev/null || true
        else
            # 包含所有数据
            eval "$dump_cmd $DB_NAME admin_users admin_user_roles admin_logs" 2>/dev/null || true
        fi
        
        # 导出其他表的数据（不需要过滤）
        eval "$dump_cmd $DB_NAME admin_roles admin_permissions admin_role_permissions admin_permission_groups swipers news our_services project_cases partners friend_links recruitments part_platform" 2>/dev/null || true
        
    } > "$temp_file"
    
    # 移动临时文件到目标位置
    mv "$temp_file" "$output_file"
    
    if [[ -f "$output_file" ]]; then
        print_message $GREEN "✅ 数据库数据导出成功: $output_file"
    else
        print_message $RED "❌ 数据库数据导出失败"
        return 1
    fi
}

# 函数：导出完整数据库
export_full() {
    local output_file="$OUTPUT_DIR/database_full.sql"
    local dump_cmd
    
    print_message $BLUE "🗄️  导出完整数据库..."
    
    if [[ "$USE_DOCKER" == "true" ]]; then
        dump_cmd="docker exec weishi-mysql mysqldump -u$DB_USER"
        if [[ -n "$DB_PASSWORD" ]]; then
            dump_cmd="$dump_cmd -p'$DB_PASSWORD'"
        fi
        dump_cmd="$dump_cmd --routines --triggers --single-transaction $DB_NAME"
    else
        dump_cmd="mysqldump -h$DB_HOST -P$DB_PORT -u$DB_USER"
        if [[ -n "$DB_PASSWORD" ]]; then
            dump_cmd="$dump_cmd -p'$DB_PASSWORD'"
        fi
        dump_cmd="$dump_cmd --routines --triggers --single-transaction $DB_NAME"
    fi
    
    # 创建临时文件
    local temp_file=$(mktemp)
    
    # 先导出完整结构和数据
    if eval "$dump_cmd" > "$temp_file" 2>/dev/null; then
        # 如果需要排除 admin 数据，进行后处理
        if [[ "$INCLUDE_ADMIN" != "true" ]]; then
            print_message $YELLOW "⚠️  从完整导出中移除 admin 用户数据..."
            
            # 使用 sed 移除 admin 相关的 INSERT 语句
            sed -i.bak '/INSERT INTO `admin_users`.*admin.*VALUES/d' "$temp_file"
            sed -i.bak '/INSERT INTO `admin_user_roles`.*admin_user_id.*3/d' "$temp_file"
            sed -i.bak '/INSERT INTO `admin_logs`.*admin_user_id.*3/d' "$temp_file"
            
            # 删除备份文件
            rm -f "$temp_file.bak"
        fi
        
        # 移动到目标位置
        mv "$temp_file" "$output_file"
        print_message $GREEN "✅ 完整数据库导出成功: $output_file"
    else
        print_message $RED "❌ 完整数据库导出失败"
        rm -f "$temp_file"
        return 1
    fi
}

# 主函数
main() {
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -H|--host)
                DB_HOST="$2"
                shift 2
                ;;
            -P|--port)
                DB_PORT="$2"
                shift 2
                ;;
            -d|--database)
                DB_NAME="$2"
                shift 2
                ;;
            -u|--user)
                DB_USER="$2"
                shift 2
                ;;
            -p|--password)
                DB_PASSWORD="$2"
                shift 2
                ;;
            -o|--output)
                OUTPUT_DIR="$2"
                shift 2
                ;;
            --structure-only)
                STRUCTURE_ONLY="true"
                shift
                ;;
            --data-only)
                DATA_ONLY="true"
                shift
                ;;
            --include-admin)
                INCLUDE_ADMIN="true"
                shift
                ;;
            --docker)
                USE_DOCKER="true"
                shift
                ;;
            *)
                print_message $RED "❌ 未知选项: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 设置默认值
    OUTPUT_DIR="${OUTPUT_DIR:-$DEFAULT_OUTPUT_DIR}"
    STRUCTURE_ONLY="${STRUCTURE_ONLY:-false}"
    DATA_ONLY="${DATA_ONLY:-false}"
    INCLUDE_ADMIN="${INCLUDE_ADMIN:-false}"
    USE_DOCKER="${USE_DOCKER:-false}"
    
    # 如果使用 Docker 或者没有指定连接参数，尝试从 docker.env 读取
    if [[ "$USE_DOCKER" == "true" ]] || [[ -z "$DB_USER" ]]; then
        load_docker_env
    fi
    
    # 设置默认值
    DB_HOST="${DB_HOST:-$DEFAULT_HOST}"
    DB_PORT="${DB_PORT:-$DEFAULT_PORT}"
    DB_NAME="${DB_NAME:-$DEFAULT_DATABASE}"
    DB_USER="${DB_USER:-root}"
    
    print_message $BLUE "🚀 开始数据库导出..."
    print_message $BLUE "📋 配置信息:"
    print_message $BLUE "   数据库主机: $DB_HOST"
    print_message $BLUE "   数据库端口: $DB_PORT"
    print_message $BLUE "   数据库名称: $DB_NAME"
    print_message $BLUE "   数据库用户: $DB_USER"
    print_message $BLUE "   输出目录: $OUTPUT_DIR"
    print_message $BLUE "   使用 Docker: $USE_DOCKER"
    print_message $BLUE "   包含 admin 数据: $INCLUDE_ADMIN"
    echo ""
    
    # 创建输出目录
    mkdir -p "$OUTPUT_DIR"
    
    # 测试数据库连接
    if ! test_connection; then
        print_message $RED "❌ 无法连接到数据库，请检查配置"
        exit 1
    fi
    
    # 根据选项执行导出
    if [[ "$STRUCTURE_ONLY" == "true" ]]; then
        export_structure
    elif [[ "$DATA_ONLY" == "true" ]]; then
        export_data
    else
        # 导出结构和数据
        export_structure
        export_data
        export_full
    fi
    
    print_message $GREEN "🎉 数据库导出完成！"
    print_message $GREEN "📁 输出文件位于: $OUTPUT_DIR"
}

# 执行主函数
main "$@"
