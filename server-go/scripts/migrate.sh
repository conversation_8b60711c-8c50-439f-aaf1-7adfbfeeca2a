#!/bin/bash

# 数据库迁移脚本
# 用法: ./migrate.sh [up|down] [migration_number]

set -e

# 配置
DB_HOST="localhost"
DB_PORT="3306"
DB_NAME="weizhi"
DB_USER="root"
DB_PASS="Ydb3344%"
MIGRATIONS_DIR="./migrations"

# Docker配置
DOCKER_CONTAINER=""  # 如果使用Docker容器，设置容器名

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 获取MySQL命令
get_mysql_cmd() {
    if [ -n "$DOCKER_CONTAINER" ]; then
        echo "docker exec -i $DOCKER_CONTAINER mysql -u$DB_USER -p$DB_PASS $DB_NAME"
    else
        echo "mysql -h$DB_HOST -P$DB_PORT -u$DB_USER -p$DB_PASS $DB_NAME"
    fi
}

# 检查MySQL连接
check_mysql_connection() {
    log_info "检查MySQL连接..."
    local mysql_cmd=$(get_mysql_cmd)

    if [ -n "$DOCKER_CONTAINER" ]; then
        if ! docker exec -i "$DOCKER_CONTAINER" mysql -u"$DB_USER" -p"$DB_PASS" -e "USE $DB_NAME;" 2>/dev/null; then
            log_error "无法连接到Docker MySQL容器: $DOCKER_CONTAINER"
            exit 1
        fi
    else
        if ! mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASS" -e "USE $DB_NAME;" 2>/dev/null; then
            log_error "无法连接到MySQL数据库"
            exit 1
        fi
    fi
    log_info "MySQL连接成功"
}

# 备份数据库
backup_database() {
    local backup_file="backup_$(date +%Y%m%d_%H%M%S).sql"
    log_info "备份数据库到 $backup_file..."
    
    mysqldump -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p \
        --single-transaction \
        --routines \
        --triggers \
        "$DB_NAME" > "$backup_file"
    
    log_info "数据库备份完成: $backup_file"
}

# 执行迁移
run_migration() {
    local direction=$1
    local migration_number=$2
    
    if [ -z "$migration_number" ]; then
        log_error "请指定迁移版本号"
        exit 1
    fi
    
    local migration_file
    if [ "$direction" = "up" ]; then
        migration_file="$MIGRATIONS_DIR/${migration_number}_optimize_table_structure.sql"
    else
        migration_file="$MIGRATIONS_DIR/${migration_number}_optimize_table_structure_rollback.sql"
    fi
    
    if [ ! -f "$migration_file" ]; then
        log_error "迁移文件不存在: $migration_file"
        exit 1
    fi
    
    log_info "执行迁移: $migration_file"
    mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p "$DB_NAME" < "$migration_file"
    log_info "迁移执行完成"
}

# 显示帮助
show_help() {
    echo "数据库迁移工具"
    echo ""
    echo "用法:"
    echo "  $0 up <migration_number>     执行迁移"
    echo "  $0 down <migration_number>   回滚迁移"
    echo "  $0 backup                    仅备份数据库"
    echo ""
    echo "示例:"
    echo "  $0 up 001                    执行001号迁移"
    echo "  $0 down 001                  回滚001号迁移"
    echo "  $0 backup                    备份当前数据库"
}

# 主函数
main() {
    local command=$1
    local migration_number=$2
    
    case "$command" in
        "up")
            check_mysql_connection
            backup_database
            run_migration "up" "$migration_number"
            ;;
        "down")
            check_mysql_connection
            backup_database
            run_migration "down" "$migration_number"
            ;;
        "backup")
            check_mysql_connection
            backup_database
            ;;
        *)
            show_help
            exit 1
            ;;
    esac
}

# 检查参数
if [ $# -eq 0 ]; then
    show_help
    exit 1
fi

# 执行主函数
main "$@"
