#!/bin/bash

# 快速数据库导出脚本
# 功能：快速导出数据库，排除 admin 用户数据
# 使用 Docker 容器进行导出

set -e

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
OUTPUT_DIR="$SCRIPT_DIR/exports"

# 创建输出目录
mkdir -p "$OUTPUT_DIR"

echo -e "${BLUE}🚀 开始快速数据库导出...${NC}"
echo -e "${BLUE}📁 输出目录: $OUTPUT_DIR${NC}"
echo ""

# 从 docker.env 读取数据库配置
DOCKER_ENV_FILE="$(dirname "$SCRIPT_DIR")/../docker.env"
if [[ -f "$DOCKER_ENV_FILE" ]]; then
    DB_NAME=$(grep "^MYSQL_DATABASE=" "$DOCKER_ENV_FILE" | cut -d'=' -f2)
    DB_PASSWORD=$(grep "^MYSQL_ROOT_PASSWORD=" "$DOCKER_ENV_FILE" | cut -d'=' -f2)
else
    DB_NAME="weizhi"
    DB_PASSWORD="Ydb3344%"
fi

echo -e "${BLUE}📋 数据库: $DB_NAME${NC}"

# 1. 导出数据库结构
echo -e "${BLUE}📋 导出数据库结构...${NC}"
docker exec weishi-mysql mysqldump \
    -u root -p"$DB_PASSWORD" \
    --no-data \
    --routines \
    --triggers \
    "$DB_NAME" > "$OUTPUT_DIR/structure_${TIMESTAMP}.sql"

if [[ $? -eq 0 ]]; then
    echo -e "${GREEN}✅ 结构导出成功${NC}"
else
    echo -e "${RED}❌ 结构导出失败${NC}"
    exit 1
fi

# 2. 导出数据（排除 admin 用户）
echo -e "${BLUE}📊 导出数据（排除 admin 用户）...${NC}"

# 创建数据导出文件
DATA_FILE="$OUTPUT_DIR/data_no_admin_${TIMESTAMP}.sql"

{
    echo "-- 数据库数据导出（排除 admin 用户）"
    echo "-- 生成时间: $(date)"
    echo "-- 数据库: $DB_NAME"
    echo "-- 注意: 已排除 admin 用户相关数据"
    echo ""
    
    # 导出 admin_users 表（排除 admin 用户）
    echo "-- admin_users 表数据（排除 admin 用户）"
    docker exec weishi-mysql mysqldump \
        -u root -p"$DB_PASSWORD" \
        --no-create-info \
        --skip-triggers \
        --where="username != 'admin'" \
        "$DB_NAME" admin_users 2>/dev/null || echo "-- admin_users 表为空或导出失败"
    
    echo ""
    
    # 导出 admin_user_roles 表（排除 admin 用户的角色）
    echo "-- admin_user_roles 表数据（排除 admin 用户）"
    docker exec weishi-mysql mysqldump \
        -u root -p"$DB_PASSWORD" \
        --no-create-info \
        --skip-triggers \
        --where="admin_user_id NOT IN (SELECT id FROM admin_users WHERE username = 'admin')" \
        "$DB_NAME" admin_user_roles 2>/dev/null || echo "-- admin_user_roles 表为空或导出失败"
    
    echo ""
    
    # 导出其他管理相关表（不需要过滤）
    echo "-- 角色权限相关表数据"
    docker exec weishi-mysql mysqldump \
        -u root -p"$DB_PASSWORD" \
        --no-create-info \
        --skip-triggers \
        "$DB_NAME" admin_roles admin_permissions admin_role_permissions admin_permission_groups 2>/dev/null || echo "-- 角色权限表导出失败"
    
    echo ""
    
    # 导出内容相关表
    echo "-- 内容相关表数据"
    docker exec weishi-mysql mysqldump \
        -u root -p"$DB_PASSWORD" \
        --no-create-info \
        --skip-triggers \
        "$DB_NAME" swipers news our_services project_cases partners friend_links recruitments part_platform 2>/dev/null || echo "-- 内容表导出失败"
    
    echo ""
    echo "-- 数据导出完成"
    
} > "$DATA_FILE"

if [[ $? -eq 0 ]]; then
    echo -e "${GREEN}✅ 数据导出成功${NC}"
else
    echo -e "${RED}❌ 数据导出失败${NC}"
    exit 1
fi

# 3. 创建完整的导出文件（结构 + 数据）
echo -e "${BLUE}🗄️  创建完整导出文件...${NC}"
FULL_FILE="$OUTPUT_DIR/full_no_admin_${TIMESTAMP}.sql"

{
    echo "-- 完整数据库导出（排除 admin 用户数据）"
    echo "-- 生成时间: $(date)"
    echo "-- 数据库: $DB_NAME"
    echo "-- 包含: 结构 + 数据（排除 admin 用户）"
    echo ""
    
    # 添加结构
    cat "$OUTPUT_DIR/structure_${TIMESTAMP}.sql"
    
    echo ""
    echo "-- ========================================"
    echo "-- 数据部分开始"
    echo "-- ========================================"
    echo ""
    
    # 添加数据
    tail -n +6 "$DATA_FILE"  # 跳过前5行注释
    
} > "$FULL_FILE"

echo -e "${GREEN}✅ 完整导出文件创建成功${NC}"

# 4. 显示导出结果
echo ""
echo -e "${GREEN}🎉 数据库导出完成！${NC}"
echo -e "${BLUE}📁 导出文件:${NC}"
echo -e "   📋 结构文件: structure_${TIMESTAMP}.sql"
echo -e "   📊 数据文件: data_no_admin_${TIMESTAMP}.sql"
echo -e "   🗄️  完整文件: full_no_admin_${TIMESTAMP}.sql"
echo ""
echo -e "${BLUE}📍 文件位置: $OUTPUT_DIR${NC}"

# 5. 显示文件大小
echo ""
echo -e "${BLUE}📏 文件大小:${NC}"
ls -lh "$OUTPUT_DIR"/*_${TIMESTAMP}.sql | awk '{print "   " $9 ": " $5}'

echo ""
echo -e "${YELLOW}💡 提示:${NC}"
echo -e "   - 这些导出文件已排除 admin 用户数据"
echo -e "   - 可以安全地用于生产环境部署"
echo -e "   - admin 用户将通过密码初始化功能自动创建"
