#!/bin/bash

# 数据库导出验证脚本
# 功能：验证导出的数据库文件是否正确排除了 admin 用户数据

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
EXPORTS_DIR="$SCRIPT_DIR/exports"

# 函数：打印带颜色的消息
print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# 函数：显示帮助信息
show_help() {
    echo "数据库导出验证脚本"
    echo ""
    echo "用法: $0 [选项] [文件名]"
    echo ""
    echo "选项:"
    echo "  -h, --help              显示此帮助信息"
    echo "  -d, --directory DIR     指定导出文件目录 (默认: $EXPORTS_DIR)"
    echo "  -a, --all               验证目录中的所有导出文件"
    echo ""
    echo "示例:"
    echo "  $0                                          # 验证最新的导出文件"
    echo "  $0 full_no_admin_20250722_001702.sql       # 验证指定文件"
    echo "  $0 --all                                    # 验证所有导出文件"
    echo "  $0 -d /backup/exports --all                # 验证指定目录的所有文件"
}

# 函数：验证单个文件
verify_file() {
    local file_path="$1"
    local file_name=$(basename "$file_path")
    
    print_message $BLUE "🔍 验证文件: $file_name"
    
    if [[ ! -f "$file_path" ]]; then
        print_message $RED "❌ 文件不存在: $file_path"
        return 1
    fi
    
    local errors=0
    local warnings=0
    
    # 检查文件是否为空
    if [[ ! -s "$file_path" ]]; then
        print_message $RED "❌ 文件为空"
        ((errors++))
    fi
    
    # 检查是否包含 admin 用户的 INSERT 语句
    local admin_inserts=0
    if grep -q "INSERT INTO.*admin_users.*'admin'" "$file_path" 2>/dev/null; then
        admin_inserts=$(grep -c "INSERT INTO.*admin_users.*'admin'" "$file_path" 2>/dev/null)
    fi
    if [[ $admin_inserts -gt 0 ]]; then
        print_message $RED "❌ 发现 admin 用户数据 ($admin_inserts 条记录)"
        ((errors++))
    else
        print_message $GREEN "✅ 已正确排除 admin 用户数据"
    fi

    # 检查是否包含 admin 用户的角色关联
    local admin_roles=0
    if grep -q "admin_user_id.*3" "$file_path" 2>/dev/null; then
        admin_roles=$(grep -c "admin_user_id.*3" "$file_path" 2>/dev/null)
    fi
    if [[ $admin_roles -gt 0 ]]; then
        print_message $RED "❌ 发现 admin 用户角色关联 ($admin_roles 条记录)"
        ((errors++))
    else
        print_message $GREEN "✅ 已正确排除 admin 用户角色关联"
    fi

    # 检查是否包含 admin 用户的操作日志（检查不同的模式）
    local admin_logs=0
    if grep -q "admin_user_id.*['\"]3['\"]" "$file_path" 2>/dev/null; then
        admin_logs=$(grep -c "admin_user_id.*['\"]3['\"]" "$file_path" 2>/dev/null)
    fi
    if [[ $admin_logs -gt 0 ]]; then
        print_message $YELLOW "⚠️  发现可能的 admin 用户日志 ($admin_logs 条记录)"
        ((warnings++))
    else
        print_message $GREEN "✅ 已正确排除 admin 用户日志"
    fi
    
    # 检查是否包含角色权限数据
    local roles_count=0
    if grep -q "INSERT INTO.*admin_roles" "$file_path" 2>/dev/null; then
        roles_count=$(grep -c "INSERT INTO.*admin_roles" "$file_path" 2>/dev/null)
    fi
    if [[ $roles_count -gt 0 ]]; then
        print_message $GREEN "✅ 包含角色数据 ($roles_count 条记录)"
    else
        print_message $YELLOW "⚠️  未找到角色数据"
        ((warnings++))
    fi

    # 检查是否包含权限数据
    local permissions_count=0
    if grep -q "INSERT INTO.*admin_permissions" "$file_path" 2>/dev/null; then
        permissions_count=$(grep -c "INSERT INTO.*admin_permissions" "$file_path" 2>/dev/null)
    fi
    if [[ $permissions_count -gt 0 ]]; then
        print_message $GREEN "✅ 包含权限数据 ($permissions_count 条记录)"
    else
        print_message $YELLOW "⚠️  未找到权限数据"
        ((warnings++))
    fi

    # 检查是否包含业务数据
    local business_tables=("swipers" "news" "our_services" "project_cases" "partners" "friend_links")
    local business_data_found=0

    for table in "${business_tables[@]}"; do
        local count=0
        if grep -q "INSERT INTO.*$table" "$file_path" 2>/dev/null; then
            count=$(grep -c "INSERT INTO.*$table" "$file_path" 2>/dev/null)
        fi
        if [[ $count -gt 0 ]]; then
            ((business_data_found++))
        fi
    done
    
    if [[ $business_data_found -gt 0 ]]; then
        print_message $GREEN "✅ 包含业务数据 ($business_data_found 个表有数据)"
    else
        print_message $YELLOW "⚠️  未找到业务数据"
        ((warnings++))
    fi
    
    # 检查文件编码
    if command -v file >/dev/null 2>&1; then
        local encoding=$(file -b --mime-encoding "$file_path")
        if [[ "$encoding" == "utf-8" ]]; then
            print_message $GREEN "✅ 文件编码正确 (UTF-8)"
        else
            print_message $YELLOW "⚠️  文件编码: $encoding"
            ((warnings++))
        fi
    fi
    
    # 检查 SQL 语法（基本检查）
    local syntax_errors=0
    
    # 检查是否有未闭合的引号
    local quote_count=$(grep -o "'" "$file_path" | wc -l)
    if [[ $((quote_count % 2)) -ne 0 ]]; then
        print_message $RED "❌ 可能存在未闭合的引号"
        ((syntax_errors++))
    fi
    
    # 检查是否有基本的 SQL 结构
    if grep -q "CREATE TABLE\|INSERT INTO\|DROP TABLE" "$file_path"; then
        print_message $GREEN "✅ 包含有效的 SQL 语句"
    else
        print_message $RED "❌ 未找到有效的 SQL 语句"
        ((syntax_errors++))
    fi
    
    ((errors += syntax_errors))
    
    # 显示文件统计信息
    local file_size=$(du -h "$file_path" | cut -f1)
    local line_count=$(wc -l < "$file_path")
    local insert_count=0
    if grep -q "INSERT INTO" "$file_path" 2>/dev/null; then
        insert_count=$(grep -c "INSERT INTO" "$file_path" 2>/dev/null)
    fi
    
    print_message $BLUE "📊 文件统计:"
    print_message $BLUE "   文件大小: $file_size"
    print_message $BLUE "   行数: $line_count"
    print_message $BLUE "   INSERT 语句数: $insert_count"
    
    # 总结
    echo ""
    if [[ $errors -eq 0 ]]; then
        if [[ $warnings -eq 0 ]]; then
            print_message $GREEN "🎉 验证通过！文件完全符合要求"
        else
            print_message $YELLOW "⚠️  验证通过，但有 $warnings 个警告"
        fi
        return 0
    else
        print_message $RED "❌ 验证失败！发现 $errors 个错误，$warnings 个警告"
        return 1
    fi
}

# 函数：获取最新的导出文件
get_latest_export() {
    local latest_file=$(ls -t "$EXPORTS_DIR"/*.sql 2>/dev/null | head -1)
    if [[ -n "$latest_file" ]]; then
        echo "$latest_file"
    else
        return 1
    fi
}

# 主函数
main() {
    local verify_all=false
    local target_file=""
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -d|--directory)
                EXPORTS_DIR="$2"
                shift 2
                ;;
            -a|--all)
                verify_all=true
                shift
                ;;
            -*)
                print_message $RED "❌ 未知选项: $1"
                show_help
                exit 1
                ;;
            *)
                target_file="$1"
                shift
                ;;
        esac
    done
    
    print_message $BLUE "🔍 数据库导出验证工具"
    print_message $BLUE "📁 导出目录: $EXPORTS_DIR"
    echo ""
    
    # 检查导出目录是否存在
    if [[ ! -d "$EXPORTS_DIR" ]]; then
        print_message $RED "❌ 导出目录不存在: $EXPORTS_DIR"
        exit 1
    fi
    
    local total_files=0
    local passed_files=0
    local failed_files=0
    
    if [[ "$verify_all" == "true" ]]; then
        # 验证所有文件
        print_message $BLUE "🔍 验证目录中的所有 SQL 文件..."
        echo ""
        
        for file in "$EXPORTS_DIR"/*.sql; do
            if [[ -f "$file" ]]; then
                ((total_files++))
                if verify_file "$file"; then
                    ((passed_files++))
                else
                    ((failed_files++))
                fi
                echo ""
            fi
        done
        
        if [[ $total_files -eq 0 ]]; then
            print_message $YELLOW "⚠️  未找到任何 SQL 文件"
            exit 1
        fi
        
    elif [[ -n "$target_file" ]]; then
        # 验证指定文件
        if [[ "$target_file" == *"/"* ]]; then
            # 包含路径的文件名
            file_path="$target_file"
        else
            # 仅文件名，在导出目录中查找
            file_path="$EXPORTS_DIR/$target_file"
        fi
        
        total_files=1
        if verify_file "$file_path"; then
            passed_files=1
        else
            failed_files=1
        fi
        
    else
        # 验证最新文件
        print_message $BLUE "🔍 查找最新的导出文件..."
        
        if latest_file=$(get_latest_export); then
            print_message $BLUE "📄 最新文件: $(basename "$latest_file")"
            echo ""
            
            total_files=1
            if verify_file "$latest_file"; then
                passed_files=1
            else
                failed_files=1
            fi
        else
            print_message $RED "❌ 未找到任何导出文件"
            print_message $BLUE "💡 请先运行导出脚本: ./quick_export.sh"
            exit 1
        fi
    fi
    
    # 显示总结
    echo ""
    print_message $BLUE "📊 验证总结:"
    print_message $BLUE "   总文件数: $total_files"
    print_message $GREEN "   通过验证: $passed_files"
    print_message $RED "   验证失败: $failed_files"
    
    if [[ $failed_files -eq 0 ]]; then
        print_message $GREEN "🎉 所有文件验证通过！"
        exit 0
    else
        print_message $RED "❌ 有文件验证失败，请检查导出脚本"
        exit 1
    fi
}

# 执行主函数
main "$@"
