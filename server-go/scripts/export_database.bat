@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

REM 数据库导出脚本 (Windows 版本)
REM 功能：导出数据库结构和数据，但排除 admin 用户相关数据

echo.
echo 🚀 数据库导出脚本 (Windows 版本)
echo =====================================
echo.

REM 设置变量
set SCRIPT_DIR=%~dp0
set PROJECT_ROOT=%SCRIPT_DIR%..
set DOCKER_ENV_FILE=%PROJECT_ROOT%\..\docker.env
set TIMESTAMP=%date:~0,4%%date:~5,2%%date:~8,2%_%time:~0,2%%time:~3,2%%time:~6,2%
set TIMESTAMP=%TIMESTAMP: =0%
set OUTPUT_DIR=%SCRIPT_DIR%exports

REM 创建输出目录
if not exist "%OUTPUT_DIR%" mkdir "%OUTPUT_DIR%"

echo 📁 输出目录: %OUTPUT_DIR%
echo 🕐 时间戳: %TIMESTAMP%
echo.

REM 检查 Docker 是否运行
echo 🔍 检查 Docker 服务...
docker ps >nul 2>&1
if errorlevel 1 (
    echo ❌ Docker 服务未运行，请启动 Docker Desktop
    pause
    exit /b 1
)
echo ✅ Docker 服务正常

REM 检查容器是否运行
echo 🔍 检查数据库容器...
docker ps | findstr weishi-mysql >nul
if errorlevel 1 (
    echo ❌ weishi-mysql 容器未运行
    echo 💡 请先启动数据库容器: docker-compose up -d mysql
    pause
    exit /b 1
)
echo ✅ 数据库容器正常运行

REM 读取数据库配置
set DB_NAME=weizhi
set DB_PASSWORD=Ydb3344%%

if exist "%DOCKER_ENV_FILE%" (
    echo 📄 从 docker.env 读取配置...
    for /f "tokens=1,2 delims==" %%a in ('type "%DOCKER_ENV_FILE%" ^| findstr "MYSQL_DATABASE="') do set DB_NAME=%%b
    for /f "tokens=1,2 delims==" %%a in ('type "%DOCKER_ENV_FILE%" ^| findstr "MYSQL_ROOT_PASSWORD="') do set DB_PASSWORD=%%b
) else (
    echo ⚠️  未找到 docker.env 文件，使用默认配置
)

echo 📋 数据库名称: %DB_NAME%
echo.

REM 1. 导出数据库结构
echo 📋 导出数据库结构...
set STRUCTURE_FILE=%OUTPUT_DIR%\structure_%TIMESTAMP%.sql
docker exec weishi-mysql mysqldump -u root -p"%DB_PASSWORD%" --no-data --routines --triggers "%DB_NAME%" > "%STRUCTURE_FILE%"

if errorlevel 1 (
    echo ❌ 结构导出失败
    pause
    exit /b 1
)
echo ✅ 结构导出成功

REM 2. 导出数据（排除 admin 用户）
echo 📊 导出数据（排除 admin 用户）...
set DATA_FILE=%OUTPUT_DIR%\data_no_admin_%TIMESTAMP%.sql

REM 创建数据导出文件头部
echo -- 数据库数据导出（排除 admin 用户） > "%DATA_FILE%"
echo -- 生成时间: %date% %time% >> "%DATA_FILE%"
echo -- 数据库: %DB_NAME% >> "%DATA_FILE%"
echo -- 注意: 已排除 admin 用户相关数据 >> "%DATA_FILE%"
echo. >> "%DATA_FILE%"

REM 导出 admin_users 表（排除 admin 用户）
echo -- admin_users 表数据（排除 admin 用户） >> "%DATA_FILE%"
docker exec weishi-mysql mysqldump -u root -p"%DB_PASSWORD%" --no-create-info --skip-triggers --where="username != 'admin'" "%DB_NAME%" admin_users >> "%DATA_FILE%" 2>nul
echo. >> "%DATA_FILE%"

REM 导出角色权限相关表
echo -- 角色权限相关表数据 >> "%DATA_FILE%"
docker exec weishi-mysql mysqldump -u root -p"%DB_PASSWORD%" --no-create-info --skip-triggers "%DB_NAME%" admin_roles admin_permissions admin_role_permissions admin_permission_groups >> "%DATA_FILE%" 2>nul
echo. >> "%DATA_FILE%"

REM 导出内容相关表
echo -- 内容相关表数据 >> "%DATA_FILE%"
docker exec weishi-mysql mysqldump -u root -p"%DB_PASSWORD%" --no-create-info --skip-triggers "%DB_NAME%" swipers news our_services project_cases partners friend_links recruitments part_platform >> "%DATA_FILE%" 2>nul

echo ✅ 数据导出成功

REM 3. 创建完整导出文件
echo 🗄️  创建完整导出文件...
set FULL_FILE=%OUTPUT_DIR%\full_no_admin_%TIMESTAMP%.sql

REM 创建完整文件头部
echo -- 完整数据库导出（排除 admin 用户数据） > "%FULL_FILE%"
echo -- 生成时间: %date% %time% >> "%FULL_FILE%"
echo -- 数据库: %DB_NAME% >> "%FULL_FILE%"
echo -- 包含: 结构 + 数据（排除 admin 用户） >> "%FULL_FILE%"
echo. >> "%FULL_FILE%"

REM 合并结构和数据文件
type "%STRUCTURE_FILE%" >> "%FULL_FILE%"
echo. >> "%FULL_FILE%"
echo -- ======================================== >> "%FULL_FILE%"
echo -- 数据部分开始 >> "%FULL_FILE%"
echo -- ======================================== >> "%FULL_FILE%"
echo. >> "%FULL_FILE%"

REM 跳过数据文件的前几行注释，添加到完整文件
more +5 "%DATA_FILE%" >> "%FULL_FILE%"

echo ✅ 完整导出文件创建成功

REM 4. 显示结果
echo.
echo 🎉 数据库导出完成！
echo =====================================
echo 📁 导出文件:
echo    📋 结构文件: structure_%TIMESTAMP%.sql
echo    📊 数据文件: data_no_admin_%TIMESTAMP%.sql
echo    🗄️  完整文件: full_no_admin_%TIMESTAMP%.sql
echo.
echo 📍 文件位置: %OUTPUT_DIR%
echo.

REM 显示文件大小
echo 📏 文件大小:
for %%f in ("%OUTPUT_DIR%\*_%TIMESTAMP%.sql") do (
    set "file=%%~nxf"
    set "size=%%~zf"
    if !size! lss 1024 (
        echo    !file!: !size! bytes
    ) else if !size! lss 1048576 (
        set /a "size_kb=!size!/1024"
        echo    !file!: !size_kb! KB
    ) else (
        set /a "size_mb=!size!/1048576"
        echo    !file!: !size_mb! MB
    )
)

echo.
echo 💡 提示:
echo    - 这些导出文件已排除 admin 用户数据
echo    - 可以安全地用于生产环境部署
echo    - admin 用户将通过密码初始化功能自动创建
echo.

pause
