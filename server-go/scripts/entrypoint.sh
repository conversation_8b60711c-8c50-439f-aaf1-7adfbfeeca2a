#!/bin/bash

# Go 应用启动脚本
# 支持在启动前执行数据导入

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[ENTRYPOINT]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[ENTRYPOINT]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[ENTRYPOINT]${NC} $1"
}

log_error() {
    echo -e "${RED}[ENTRYPOINT]${NC} $1"
}

# 环境变量配置
IMPORT_SEED_DATA=${IMPORT_SEED_DATA:-false}
IMPORT_ADMIN_DATA=${IMPORT_ADMIN_DATA:-false}
IMPORT_FULL_DATA=${IMPORT_FULL_DATA:-false}
RUN_DATA_IMPORT_SCRIPT=${RUN_DATA_IMPORT_SCRIPT:-false}

# 应用配置
APP_BINARY=${APP_BINARY:-./main}
SCRIPT_DIR=${SCRIPT_DIR:-./scripts}

# 等待数据库连接
wait_for_database() {
    local db_host=${DB_HOST:-mysql}
    local db_port=${DB_PORT:-3306}
    local db_user=${DB_USERNAME:-root}
    local db_pass=${DB_PASSWORD:-${MYSQL_ROOT_PASSWORD}}
    
    if [ -z "$db_pass" ]; then
        log_warning "数据库密码未设置，跳过数据库连接检查"
        return 0
    fi
    
    log_info "等待数据库连接 $db_host:$db_port..."
    
    local max_attempts=30
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if mysql -h"$db_host" -P"$db_port" -u"$db_user" -p"$db_pass" -e "SELECT 1;" >/dev/null 2>&1; then
            log_success "数据库连接成功"
            return 0
        fi
        
        log_info "等待数据库连接... ($attempt/$max_attempts)"
        sleep 2
        attempt=$((attempt + 1))
    done
    
    log_warning "数据库连接超时，继续启动应用"
    return 0
}

# 执行数据导入脚本
run_data_import_script() {
    local import_script="$SCRIPT_DIR/import-data.sh"
    
    if [ ! -f "$import_script" ]; then
        log_warning "数据导入脚本不存在: $import_script"
        return 0
    fi
    
    log_info "执行数据导入脚本..."
    
    # 设置环境变量
    export RUN_MODE=container
    export SCRIPT_DIR="$SCRIPT_DIR"
    
    if bash "$import_script"; then
        log_success "数据导入脚本执行完成"
    else
        log_warning "数据导入脚本执行失败，继续启动应用"
    fi
}

# 检查是否需要数据导入
should_import_data() {
    if [ "$RUN_DATA_IMPORT_SCRIPT" = "true" ]; then
        return 0
    fi
    
    if [ "$IMPORT_SEED_DATA" = "true" ] || [ "$IMPORT_ADMIN_DATA" = "true" ] || [ "$IMPORT_FULL_DATA" = "true" ]; then
        return 0
    fi
    
    return 1
}

# 启动应用
start_application() {
    log_info "启动 Go 应用..."
    
    if [ ! -f "$APP_BINARY" ]; then
        log_error "应用二进制文件不存在: $APP_BINARY"
        exit 1
    fi
    
    log_success "启动应用: $APP_BINARY"
    exec "$APP_BINARY" "$@"
}

# 主函数
main() {
    log_info "=== Go 应用启动脚本 ==="
    
    # 显示配置信息
    log_info "配置信息:"
    log_info "  应用二进制: $APP_BINARY"
    log_info "  脚本目录: $SCRIPT_DIR"
    log_info "  数据导入选项:"
    log_info "    种子数据: $IMPORT_SEED_DATA"
    log_info "    管理员数据: $IMPORT_ADMIN_DATA"
    log_info "    完整数据: $IMPORT_FULL_DATA"
    log_info "    运行导入脚本: $RUN_DATA_IMPORT_SCRIPT"
    
    # 等待数据库（如果配置了数据库连接）
    wait_for_database
    
    # 执行数据导入（如果需要）
    if should_import_data; then
        log_info "检测到数据导入需求，执行数据导入..."
        run_data_import_script
    else
        log_info "未启用数据导入，直接启动应用"
    fi
    
    # 启动应用
    start_application "$@"
}

# 处理信号
trap 'log_info "收到终止信号，正在关闭..."; exit 0' SIGTERM SIGINT

# 执行主函数
main "$@"
