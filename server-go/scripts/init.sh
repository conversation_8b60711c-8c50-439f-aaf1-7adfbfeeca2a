#!/bin/bash

echo "🚀 初始化蔚之领域智能科技 Go 服务端项目..."

# 检查 Go 环境
if ! command -v go &> /dev/null; then
    echo "❌ Go 未安装，请先安装 Go 1.24+"
    exit 1
fi

# 检查 Go 版本
GO_VERSION=$(go version | cut -d' ' -f3 | cut -d'.' -f1-2)
if [[ "$GO_VERSION" < "go1.24" ]]; then
    echo "❌ Go 版本过低，需要 1.24+，当前版本: $GO_VERSION"
    exit 1
fi

echo "✅ Go 版本检查通过: $GO_VERSION"

# 下载依赖
echo "📦 下载依赖包..."
go mod download
if [ $? -ne 0 ]; then
    echo "❌ 依赖下载失败"
    exit 1
fi

echo "✅ 依赖下载完成"

# 创建必要目录
echo "📁 创建必要目录..."
mkdir -p logs
mkdir -p docs

# 检查数据库连接
echo "🔍 检查数据库连接..."
echo "请确保 MySQL 数据库已启动并且配置正确"
echo "数据库配置在 config.yaml 文件中"

# 生成 Swagger 文档
echo "📚 生成 API 文档..."
if command -v swag &> /dev/null; then
    swag init -g cmd/main.go
    echo "✅ Swagger 文档生成完成"
else
    echo "⚠️  swag 未安装，跳过文档生成"
    echo "可以运行以下命令安装并生成文档："
    echo "go install github.com/swaggo/swag/cmd/swag@latest"
    echo "swag init -g cmd/main.go"
fi

echo ""
echo "🎉 初始化完成！"
echo ""
echo "📋 接下来的步骤："
echo "1. 确保 MySQL 数据库运行并创建数据库: weishi_db"
echo "2. 修改 config.yaml 中的数据库连接信息"
echo "3. 运行项目: go run cmd/main.go"
echo "4. 访问 API 文档: http://localhost:3000/api/docs/index.html"
echo ""
echo "🔧 常用命令："
echo "开发模式运行: go run cmd/main.go"
echo "构建项目: go build -o main cmd/main.go"
echo "运行测试: go test ./..."
echo "" 