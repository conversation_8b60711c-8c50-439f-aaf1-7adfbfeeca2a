-- =====================================================
-- 蔚之领域智能科技项目数据库初始化文件
-- 包含表结构和初始数据
-- 生成时间: 2025-07-20 23:18:55
-- 管理员账号: admin
-- =====================================================

-- 设置字符集
SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;


-- ------------------------------------------------------
-- Server version	8.0.41

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*M!100616 SET @OLD_NOTE_VERBOSITY=@@NOTE_VERBOSITY, NOTE_VERBOSITY=0 */;

--
-- Current Database: `weizhi`
--

/*!40000 DROP DATABASE IF EXISTS `weizhi`*/;

CREATE DATABASE /*!32312 IF NOT EXISTS*/ `weizhi` /*!40100 DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci */ /*!80016 DEFAULT ENCRYPTION='N' */;

USE `weizhi`;

--
-- Table structure for table `admin_logs`
--

DROP TABLE IF EXISTS `admin_logs`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8mb4 */;
CREATE TABLE `admin_logs` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `created_at` datetime(3) DEFAULT NULL,
  `updated_at` datetime(3) DEFAULT NULL,
  `admin_user_id` bigint unsigned NOT NULL,
  `username` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `action` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `module` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
  `method` varchar(10) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `url` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `ip` varchar(45) COLLATE utf8mb4_unicode_ci NOT NULL,
  `user_agent` text COLLATE utf8mb4_unicode_ci,
  `params` json DEFAULT NULL,
  `result` text COLLATE utf8mb4_unicode_ci,
  `status` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT 'success',
  `error_msg` text COLLATE utf8mb4_unicode_ci,
  `duration` bigint DEFAULT NULL,
  `deleted_at` datetime(3) DEFAULT NULL,
  `content` text COLLATE utf8mb4_unicode_ci,
  PRIMARY KEY (`id`),
  KEY `idx_admin_logs_deleted_at` (`deleted_at`)
) ENGINE=InnoDB AUTO_INCREMENT=842 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `admin_permission_groups`
--

DROP TABLE IF EXISTS `admin_permission_groups`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8mb4 */;
CREATE TABLE `admin_permission_groups` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `code` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` text COLLATE utf8mb4_unicode_ci,
  `icon` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `sort` bigint DEFAULT '0',
  `status` enum('active','inactive') COLLATE utf8mb4_unicode_ci DEFAULT 'active',
  `created_at` datetime(3) DEFAULT NULL,
  `updated_at` datetime(3) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `code` (`code`),
  UNIQUE KEY `code_2` (`code`),
  UNIQUE KEY `idx_admin_permission_groups_code` (`code`),
  KEY `idx_code` (`code`),
  KEY `idx_sort` (`sort`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='管理员权限分组表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `admin_permissions`
--

DROP TABLE IF EXISTS `admin_permissions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8mb4 */;
CREATE TABLE `admin_permissions` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `created_at` datetime(3) DEFAULT NULL,
  `updated_at` datetime(3) DEFAULT NULL,
  `name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `code` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `type` enum('menu','button','api') COLLATE utf8mb4_unicode_ci NOT NULL,
  `parent_id` bigint unsigned DEFAULT NULL,
  `group_id` bigint unsigned DEFAULT NULL,
  `path` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `component` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `icon` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `method` varchar(10) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `description` text COLLATE utf8mb4_unicode_ci,
  `status` enum('active','inactive') COLLATE utf8mb4_unicode_ci DEFAULT 'active',
  `sort` bigint DEFAULT '0',
  `remark` text COLLATE utf8mb4_unicode_ci,
  `created_by` bigint DEFAULT NULL,
  `updated_by` bigint DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_admin_permissions_code` (`code`),
  KEY `idx_group_id` (`group_id`),
  KEY `fk_admin_permissions_children` (`parent_id`),
  CONSTRAINT `fk_admin_permissions_children` FOREIGN KEY (`parent_id`) REFERENCES `admin_permissions` (`id`),
  CONSTRAINT `fk_admin_permissions_group` FOREIGN KEY (`group_id`) REFERENCES `admin_permission_groups` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=140 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `admin_role_permissions`
--

DROP TABLE IF EXISTS `admin_role_permissions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8mb4 */;
CREATE TABLE `admin_role_permissions` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `role_id` bigint unsigned NOT NULL,
  `permission_id` bigint unsigned NOT NULL,
  `created_by` bigint DEFAULT NULL,
  `created_at` datetime(3) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=280 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `admin_roles`
--

DROP TABLE IF EXISTS `admin_roles`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8mb4 */;
CREATE TABLE `admin_roles` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `created_at` datetime(3) DEFAULT NULL,
  `updated_at` datetime(3) DEFAULT NULL,
  `name` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
  `code` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` text COLLATE utf8mb4_unicode_ci,
  `status` enum('active','inactive') COLLATE utf8mb4_unicode_ci DEFAULT 'active',
  `sort` bigint DEFAULT '0',
  `remark` text COLLATE utf8mb4_unicode_ci,
  `created_by` bigint DEFAULT NULL,
  `updated_by` bigint DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_admin_roles_name` (`name`),
  UNIQUE KEY `idx_admin_roles_code` (`code`)
) ENGINE=InnoDB AUTO_INCREMENT=16 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `admin_user_roles`
--

DROP TABLE IF EXISTS `admin_user_roles`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8mb4 */;
CREATE TABLE `admin_user_roles` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `admin_user_id` bigint unsigned NOT NULL,
  `role_id` bigint unsigned NOT NULL,
  `created_by` bigint DEFAULT NULL,
  `created_at` datetime(3) DEFAULT NULL,
  `updated_at` datetime(3) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=11 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `admin_users`
--

DROP TABLE IF EXISTS `admin_users`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8mb4 */;
CREATE TABLE `admin_users` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `created_at` datetime(3) DEFAULT NULL,
  `updated_at` datetime(3) DEFAULT NULL,
  `username` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
  `email` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `password` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `real_name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `avatar` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `phone` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `status` enum('active','inactive','banned') COLLATE utf8mb4_unicode_ci DEFAULT 'active',
  `last_login_at` datetime(3) DEFAULT NULL,
  `last_login_ip` varchar(45) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `login_count` bigint DEFAULT '0',
  `remark` text COLLATE utf8mb4_unicode_ci,
  `created_by` bigint DEFAULT NULL,
  `updated_by` bigint DEFAULT NULL,
  `deleted_at` datetime(3) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_admin_users_username` (`username`),
  UNIQUE KEY `idx_admin_users_email` (`email`),
  KEY `idx_admin_users_deleted_at` (`deleted_at`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `cache_versions`
--

DROP TABLE IF EXISTS `cache_versions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8mb4 */;
CREATE TABLE `cache_versions` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `version` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '版本描述',
  `created_by` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
  `created_at` datetime(3) DEFAULT NULL,
  `updated_at` datetime(3) DEFAULT NULL,
  `cache_key` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_cache_versions_version` (`version`),
  UNIQUE KEY `cache_key` (`cache_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `file_uploads`
--

DROP TABLE IF EXISTS `file_uploads`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8mb4 */;
CREATE TABLE `file_uploads` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `original_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '原始文件名',
  `file_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '存储文件名',
  `file_path` varchar(500) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '文件路径',
  `file_size` bigint NOT NULL COMMENT '文件大小（字节）',
  `mime_type` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '文件类型',
  `bucket_name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '存储桶名称',
  `cos_url` varchar(500) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'COS访问URL',
  `uploaded_by` bigint unsigned NOT NULL COMMENT '上传用户ID',
  `module` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '所属模块',
  `status` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'active' COMMENT '文件状态',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_uploaded_by` (`uploaded_by`),
  KEY `idx_module` (`module`),
  KEY `idx_status` (`status`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB AUTO_INCREMENT=21 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='文件上传记录表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `friend_links`
--

DROP TABLE IF EXISTS `friend_links`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8mb4 */;
CREATE TABLE `friend_links` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `created_at` datetime(3) DEFAULT NULL,
  `updated_at` datetime(3) DEFAULT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `url` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `order` bigint DEFAULT '0',
  `deleted_at` datetime(3) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_friend_links_deleted_at` (`deleted_at`)
) ENGINE=InnoDB AUTO_INCREMENT=13 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `news`
--

DROP TABLE IF EXISTS `news`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8mb4 */;
CREATE TABLE `news` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `created_at` datetime(3) DEFAULT NULL,
  `updated_at` datetime(3) DEFAULT NULL,
  `title` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `content` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `image` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `is_home_page` tinyint(1) DEFAULT '0',
  `deleted_at` datetime(3) DEFAULT NULL,
  `summary` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `cover_image` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `author` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `publish_time` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `is_published` tinyint(1) DEFAULT '0',
  `view_count` bigint DEFAULT '0',
  PRIMARY KEY (`id`),
  KEY `idx_news_deleted_at` (`deleted_at`)
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `our_services`
--

DROP TABLE IF EXISTS `our_services`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8mb4 */;
CREATE TABLE `our_services` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `created_at` datetime(3) DEFAULT NULL,
  `updated_at` datetime(3) DEFAULT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `type` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `fullDescription` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `image` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `features` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `equipments` text COLLATE utf8mb4_unicode_ci,
  `testItems` text COLLATE utf8mb4_unicode_ci,
  `order` bigint DEFAULT '0',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `part_platform`
--

DROP TABLE IF EXISTS `part_platform`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8mb4 */;
CREATE TABLE `part_platform` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `created_at` datetime(3) DEFAULT NULL,
  `updated_at` datetime(3) DEFAULT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `url` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `parameters` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `applications` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `order` bigint DEFAULT '0',
  `deleted_at` datetime(3) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_part_platform_deleted_at` (`deleted_at`)
) ENGINE=InnoDB AUTO_INCREMENT=20 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `partners`
--

DROP TABLE IF EXISTS `partners`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8mb4 */;
CREATE TABLE `partners` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `created_at` datetime(3) DEFAULT NULL,
  `updated_at` datetime(3) DEFAULT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `logo` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `deleted_at` datetime(3) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_partners_deleted_at` (`deleted_at`)
) ENGINE=InnoDB AUTO_INCREMENT=22 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `project_cases`
--

DROP TABLE IF EXISTS `project_cases`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8mb4 */;
CREATE TABLE `project_cases` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `created_at` datetime(3) DEFAULT NULL,
  `updated_at` datetime(3) DEFAULT NULL,
  `url` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `title` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `description` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `sort` bigint DEFAULT '0',
  `deleted_at` datetime(3) DEFAULT NULL,
  `cover_image` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `tags` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `order` bigint DEFAULT '0',
  PRIMARY KEY (`id`),
  KEY `idx_project_cases_deleted_at` (`deleted_at`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `recruitments`
--

DROP TABLE IF EXISTS `recruitments`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8mb4 */;
CREATE TABLE `recruitments` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `created_at` datetime(3) DEFAULT NULL,
  `updated_at` datetime(3) DEFAULT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `location` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `content` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `order` bigint DEFAULT '0',
  `deleted_at` datetime(3) DEFAULT NULL,
  `position` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `department` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `requirement` text COLLATE utf8mb4_unicode_ci,
  `is_active` tinyint(1) DEFAULT '1',
  PRIMARY KEY (`id`),
  KEY `idx_recruitments_deleted_at` (`deleted_at`)
) ENGINE=InnoDB AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `services`
--

DROP TABLE IF EXISTS `services`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8mb4 */;
CREATE TABLE `services` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `created_at` datetime(3) DEFAULT NULL,
  `updated_at` datetime(3) DEFAULT NULL,
  `deleted_at` datetime(3) DEFAULT NULL,
  `title` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` text COLLATE utf8mb4_unicode_ci NOT NULL,
  `icon` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `order` bigint DEFAULT '0',
  PRIMARY KEY (`id`),
  KEY `idx_services_deleted_at` (`deleted_at`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `swipers`
--

DROP TABLE IF EXISTS `swipers`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8mb4 */;
CREATE TABLE `swipers` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `created_at` datetime(3) DEFAULT NULL,
  `updated_at` datetime(3) DEFAULT NULL,
  `url` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `title` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `order` bigint DEFAULT '0',
  `deleted_at` datetime(3) DEFAULT NULL,
  `status` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT 'active',
  PRIMARY KEY (`id`),
  KEY `idx_swipers_deleted_at` (`deleted_at`)
) ENGINE=InnoDB AUTO_INCREMENT=11 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping routines for database 'weizhi'
--
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*M!100616 SET NOTE_VERBOSITY=@OLD_NOTE_VERBOSITY */;

-- Dump completed on 2025-07-20  3:23:03

-- =====================================================
-- 数据插入部分
-- =====================================================

/*M!999999\- enable the sandbox mode */ 
-- MariaDB dump 10.19-11.7.2-MariaDB, for osx10.20 (arm64)
--
-- Host: localhost    Database: weizhi
-- ------------------------------------------------------
-- Server version	8.0.41

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*M!100616 SET @OLD_NOTE_VERBOSITY=@@NOTE_VERBOSITY, NOTE_VERBOSITY=0 */;

--
-- Dumping data for table `admin_logs`
--

LOCK TABLES `admin_logs` WRITE;
/*!40000 ALTER TABLE `admin_logs` DISABLE KEYS */;
-- admin 相关日志数据已移除，将在管理员用户创建后重新生成
/*!40000 ALTER TABLE `admin_logs` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Dumping data for table `admin_permission_groups`
--

LOCK TABLES `admin_permission_groups` WRITE;
/*!40000 ALTER TABLE `admin_permission_groups` DISABLE KEYS */;
INSERT INTO `admin_permission_groups` (`id`, `name`, `code`, `description`, `icon`, `sort`, `status`, `created_at`, `updated_at`) VALUES (1,'系统管理','system','系统核心功能管理','Setting',1,'active','2025-07-18 19:07:14.000','2025-07-18 19:07:14.000');
INSERT INTO `admin_permission_groups` (`id`, `name`, `code`, `description`, `icon`, `sort`, `status`, `created_at`, `updated_at`) VALUES (2,'管理员管理','admin','管理员用户、角色、权限管理','UserFilled',2,'active','2025-07-18 19:07:14.000','2025-07-18 19:07:14.000');
INSERT INTO `admin_permission_groups` (`id`, `name`, `code`, `description`, `icon`, `sort`, `status`, `created_at`, `updated_at`) VALUES (3,'内容管理','content','网站内容数据管理','Document',3,'active','2025-07-18 19:07:14.000','2025-07-18 19:07:14.000');
INSERT INTO `admin_permission_groups` (`id`, `name`, `code`, `description`, `icon`, `sort`, `status`, `created_at`, `updated_at`) VALUES (4,'日志管理','log','系统操作日志管理','Document',4,'active','2025-07-18 19:07:14.000','2025-07-18 19:07:14.000');
/*!40000 ALTER TABLE `admin_permission_groups` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Dumping data for table `admin_permissions`
--

LOCK TABLES `admin_permissions` WRITE;
/*!40000 ALTER TABLE `admin_permissions` DISABLE KEYS */;
INSERT INTO `admin_permissions` (`id`, `created_at`, `updated_at`, `name`, `code`, `type`, `parent_id`, `group_id`, `path`, `component`, `icon`, `method`, `description`, `status`, `sort`, `remark`, `created_by`, `updated_by`) VALUES (1,'2025-07-18 18:53:55.000','2025-07-18 18:53:55.000','仪表盘','dashboard','menu',NULL,1,'/dashboard','views/dashboard/index.vue','Dashboard',NULL,'系统仪表盘','active',1,NULL,NULL,NULL);
INSERT INTO `admin_permissions` (`id`, `created_at`, `updated_at`, `name`, `code`, `type`, `parent_id`, `group_id`, `path`, `component`, `icon`, `method`, `description`, `status`, `sort`, `remark`, `created_by`, `updated_by`) VALUES (2,'2025-07-18 18:53:55.000','2025-07-18 18:53:55.000','管理员管理','admin','menu',NULL,2,'/admin',NULL,'UserFilled',NULL,'管理员管理模块','active',2,NULL,NULL,NULL);
INSERT INTO `admin_permissions` (`id`, `created_at`, `updated_at`, `name`, `code`, `type`, `parent_id`, `group_id`, `path`, `component`, `icon`, `method`, `description`, `status`, `sort`, `remark`, `created_by`, `updated_by`) VALUES (3,'2025-07-18 18:53:55.000','2025-07-18 18:53:55.000','内容管理','content','menu',NULL,3,'/content',NULL,'Document',NULL,'内容管理模块','active',3,NULL,NULL,NULL);
INSERT INTO `admin_permissions` (`id`, `created_at`, `updated_at`, `name`, `code`, `type`, `parent_id`, `group_id`, `path`, `component`, `icon`, `method`, `description`, `status`, `sort`, `remark`, `created_by`, `updated_by`) VALUES (4,'2025-07-18 18:53:55.000','2025-07-18 18:53:55.000','系统设置','system','menu',NULL,1,'/settings',NULL,'Setting',NULL,'系统设置模块','active',4,NULL,NULL,NULL);
INSERT INTO `admin_permissions` (`id`, `created_at`, `updated_at`, `name`, `code`, `type`, `parent_id`, `group_id`, `path`, `component`, `icon`, `method`, `description`, `status`, `sort`, `remark`, `created_by`, `updated_by`) VALUES (5,'2025-07-18 18:54:04.000','2025-07-18 18:54:04.000','管理员用户','admin:user','menu',2,2,'/admin/users','views/admin/users/index.vue','User',NULL,'管理员用户管理','active',1,NULL,NULL,NULL);
INSERT INTO `admin_permissions` (`id`, `created_at`, `updated_at`, `name`, `code`, `type`, `parent_id`, `group_id`, `path`, `component`, `icon`, `method`, `description`, `status`, `sort`, `remark`, `created_by`, `updated_by`) VALUES (6,'2025-07-18 18:54:04.000','2025-07-18 18:54:04.000','角色管理','admin:role','menu',2,2,'/admin/roles','views/admin/roles/index.vue','Avatar',NULL,'角色管理','active',2,NULL,NULL,NULL);
INSERT INTO `admin_permissions` (`id`, `created_at`, `updated_at`, `name`, `code`, `type`, `parent_id`, `group_id`, `path`, `component`, `icon`, `method`, `description`, `status`, `sort`, `remark`, `created_by`, `updated_by`) VALUES (7,'2025-07-18 18:54:04.000','2025-07-18 18:54:04.000','权限管理','admin:permission','menu',2,2,'/admin/permissions','views/admin/permissions/index.vue','Key',NULL,'权限管理','active',3,NULL,NULL,NULL);
INSERT INTO `admin_permissions` (`id`, `created_at`, `updated_at`, `name`, `code`, `type`, `parent_id`, `group_id`, `path`, `component`, `icon`, `method`, `description`, `status`, `sort`, `remark`, `created_by`, `updated_by`) VALUES (8,'2025-07-18 18:54:04.000','2025-07-18 18:54:04.000','操作日志','admin:log','menu',2,2,'/admin/logs','views/admin/logs/index.vue','Document',NULL,'系统操作日志','active',4,NULL,NULL,NULL);
INSERT INTO `admin_permissions` (`id`, `created_at`, `updated_at`, `name`, `code`, `type`, `parent_id`, `group_id`, `path`, `component`, `icon`, `method`, `description`, `status`, `sort`, `remark`, `created_by`, `updated_by`) VALUES (9,'2025-07-18 18:54:18.000','2025-07-18 18:54:18.000','轮播图管理','content:swiper','menu',3,3,'/content/swipers','views/content/swipers/index.vue','Picture',NULL,'轮播图管理','active',1,NULL,NULL,NULL);
INSERT INTO `admin_permissions` (`id`, `created_at`, `updated_at`, `name`, `code`, `type`, `parent_id`, `group_id`, `path`, `component`, `icon`, `method`, `description`, `status`, `sort`, `remark`, `created_by`, `updated_by`) VALUES (10,'2025-07-18 18:54:18.000','2025-07-18 18:54:18.000','新闻管理','content:news','menu',3,3,'/content/news','views/content/news/index.vue','ChatLineRound',NULL,'新闻管理','active',2,NULL,NULL,NULL);
INSERT INTO `admin_permissions` (`id`, `created_at`, `updated_at`, `name`, `code`, `type`, `parent_id`, `group_id`, `path`, `component`, `icon`, `method`, `description`, `status`, `sort`, `remark`, `created_by`, `updated_by`) VALUES (11,'2025-07-18 18:54:18.000','2025-07-18 18:54:18.000','服务管理','content:service','menu',3,3,'/content/services','views/content/services/index.vue','Tools',NULL,'服务管理','active',3,NULL,NULL,NULL);
INSERT INTO `admin_permissions` (`id`, `created_at`, `updated_at`, `name`, `code`, `type`, `parent_id`, `group_id`, `path`, `component`, `icon`, `method`, `description`, `status`, `sort`, `remark`, `created_by`, `updated_by`) VALUES (12,'2025-07-18 18:54:18.000','2025-07-18 18:54:18.000','项目案例','content:project','menu',3,3,'/content/project-cases','views/content/project-cases/index.vue','Folder',NULL,'项目案例管理','active',4,NULL,NULL,NULL);
INSERT INTO `admin_permissions` (`id`, `created_at`, `updated_at`, `name`, `code`, `type`, `parent_id`, `group_id`, `path`, `component`, `icon`, `method`, `description`, `status`, `sort`, `remark`, `created_by`, `updated_by`) VALUES (13,'2025-07-18 18:54:18.000','2025-07-18 18:54:18.000','合作伙伴','content:partner','menu',3,3,'/content/partners','views/content/partners/index.vue','UserFilled',NULL,'合作伙伴管理','active',5,NULL,NULL,NULL);
INSERT INTO `admin_permissions` (`id`, `created_at`, `updated_at`, `name`, `code`, `type`, `parent_id`, `group_id`, `path`, `component`, `icon`, `method`, `description`, `status`, `sort`, `remark`, `created_by`, `updated_by`) VALUES (14,'2025-07-18 18:54:18.000','2025-07-18 18:54:18.000','友情链接','content:link','menu',3,3,'/content/friend-links','views/content/friend-links/index.vue','Link',NULL,'友情链接管理','active',6,NULL,NULL,NULL);
INSERT INTO `admin_permissions` (`id`, `created_at`, `updated_at`, `name`, `code`, `type`, `parent_id`, `group_id`, `path`, `component`, `icon`, `method`, `description`, `status`, `sort`, `remark`, `created_by`, `updated_by`) VALUES (15,'2025-07-18 18:54:18.000','2025-07-18 18:54:18.000','招聘信息','content:recruitment','menu',3,3,'/content/recruitments','views/content/recruitments/index.vue','Briefcase',NULL,'招聘信息管理','active',7,NULL,NULL,NULL);
INSERT INTO `admin_permissions` (`id`, `created_at`, `updated_at`, `name`, `code`, `type`, `parent_id`, `group_id`, `path`, `component`, `icon`, `method`, `description`, `status`, `sort`, `remark`, `created_by`, `updated_by`) VALUES (16,'2025-07-18 18:54:18.000','2025-07-18 18:54:18.000','平台管理','content:platform','menu',3,3,'/content/part-platforms','views/content/part-platforms/index.vue','Platform',NULL,'零件平台管理','active',8,NULL,NULL,NULL);
INSERT INTO `admin_permissions` (`id`, `created_at`, `updated_at`, `name`, `code`, `type`, `parent_id`, `group_id`, `path`, `component`, `icon`, `method`, `description`, `status`, `sort`, `remark`, `created_by`, `updated_by`) VALUES (17,'2025-07-18 18:54:27.000','2025-07-18 18:54:27.000','基本设置','system:setting','menu',4,1,'/settings','views/settings/index.vue','Tools',NULL,'系统基本设置','active',1,NULL,NULL,NULL);
INSERT INTO `admin_permissions` (`id`, `created_at`, `updated_at`, `name`, `code`, `type`, `parent_id`, `group_id`, `path`, `component`, `icon`, `method`, `description`, `status`, `sort`, `remark`, `created_by`, `updated_by`) VALUES (68,'2025-07-18 18:56:48.000','2025-07-18 18:56:48.000','查看用户列表','admin:user:list','button',5,2,NULL,NULL,NULL,NULL,'查看管理员用户列表','active',1,NULL,NULL,NULL);
INSERT INTO `admin_permissions` (`id`, `created_at`, `updated_at`, `name`, `code`, `type`, `parent_id`, `group_id`, `path`, `component`, `icon`, `method`, `description`, `status`, `sort`, `remark`, `created_by`, `updated_by`) VALUES (69,'2025-07-18 18:56:48.000','2025-07-18 18:56:48.000','新增用户','admin:user:create','button',5,2,NULL,NULL,NULL,NULL,'新增管理员用户','active',2,NULL,NULL,NULL);
INSERT INTO `admin_permissions` (`id`, `created_at`, `updated_at`, `name`, `code`, `type`, `parent_id`, `group_id`, `path`, `component`, `icon`, `method`, `description`, `status`, `sort`, `remark`, `created_by`, `updated_by`) VALUES (70,'2025-07-18 18:56:48.000','2025-07-18 18:56:48.000','编辑用户','admin:user:update','button',5,2,NULL,NULL,NULL,NULL,'编辑管理员用户','active',3,NULL,NULL,NULL);
INSERT INTO `admin_permissions` (`id`, `created_at`, `updated_at`, `name`, `code`, `type`, `parent_id`, `group_id`, `path`, `component`, `icon`, `method`, `description`, `status`, `sort`, `remark`, `created_by`, `updated_by`) VALUES (71,'2025-07-18 18:56:48.000','2025-07-18 18:56:48.000','删除用户','admin:user:delete','button',5,2,NULL,NULL,NULL,NULL,'删除管理员用户','active',4,NULL,NULL,NULL);
INSERT INTO `admin_permissions` (`id`, `created_at`, `updated_at`, `name`, `code`, `type`, `parent_id`, `group_id`, `path`, `component`, `icon`, `method`, `description`, `status`, `sort`, `remark`, `created_by`, `updated_by`) VALUES (72,'2025-07-18 18:56:48.000','2025-07-18 18:56:48.000','重置密码','admin:user:reset','button',5,2,NULL,NULL,NULL,NULL,'重置用户密码','active',5,NULL,NULL,NULL);
INSERT INTO `admin_permissions` (`id`, `created_at`, `updated_at`, `name`, `code`, `type`, `parent_id`, `group_id`, `path`, `component`, `icon`, `method`, `description`, `status`, `sort`, `remark`, `created_by`, `updated_by`) VALUES (73,'2025-07-18 18:56:48.000','2025-07-18 18:56:48.000','查看角色列表','admin:role:list','button',6,2,NULL,NULL,NULL,NULL,'查看角色列表','active',1,NULL,NULL,NULL);
INSERT INTO `admin_permissions` (`id`, `created_at`, `updated_at`, `name`, `code`, `type`, `parent_id`, `group_id`, `path`, `component`, `icon`, `method`, `description`, `status`, `sort`, `remark`, `created_by`, `updated_by`) VALUES (74,'2025-07-18 18:56:48.000','2025-07-18 18:56:48.000','新增角色','admin:role:create','button',6,2,NULL,NULL,NULL,NULL,'新增角色','active',2,NULL,NULL,NULL);
INSERT INTO `admin_permissions` (`id`, `created_at`, `updated_at`, `name`, `code`, `type`, `parent_id`, `group_id`, `path`, `component`, `icon`, `method`, `description`, `status`, `sort`, `remark`, `created_by`, `updated_by`) VALUES (75,'2025-07-18 18:56:48.000','2025-07-18 18:56:48.000','编辑角色','admin:role:update','button',6,2,NULL,NULL,NULL,NULL,'编辑角色','active',3,NULL,NULL,NULL);
INSERT INTO `admin_permissions` (`id`, `created_at`, `updated_at`, `name`, `code`, `type`, `parent_id`, `group_id`, `path`, `component`, `icon`, `method`, `description`, `status`, `sort`, `remark`, `created_by`, `updated_by`) VALUES (76,'2025-07-18 18:56:48.000','2025-07-18 18:56:48.000','删除角色','admin:role:delete','button',6,2,NULL,NULL,NULL,NULL,'删除角色','active',4,NULL,NULL,NULL);
INSERT INTO `admin_permissions` (`id`, `created_at`, `updated_at`, `name`, `code`, `type`, `parent_id`, `group_id`, `path`, `component`, `icon`, `method`, `description`, `status`, `sort`, `remark`, `created_by`, `updated_by`) VALUES (77,'2025-07-18 18:56:48.000','2025-07-18 18:56:48.000','分配权限','admin:role:permissions','button',6,2,NULL,NULL,NULL,NULL,'为角色分配权限','active',5,NULL,NULL,NULL);
INSERT INTO `admin_permissions` (`id`, `created_at`, `updated_at`, `name`, `code`, `type`, `parent_id`, `group_id`, `path`, `component`, `icon`, `method`, `description`, `status`, `sort`, `remark`, `created_by`, `updated_by`) VALUES (78,'2025-07-18 18:56:58.000','2025-07-18 18:56:58.000','查看权限列表','admin:permission:list','button',7,2,NULL,NULL,NULL,NULL,'查看权限列表','active',1,NULL,NULL,NULL);
INSERT INTO `admin_permissions` (`id`, `created_at`, `updated_at`, `name`, `code`, `type`, `parent_id`, `group_id`, `path`, `component`, `icon`, `method`, `description`, `status`, `sort`, `remark`, `created_by`, `updated_by`) VALUES (79,'2025-07-18 18:56:58.000','2025-07-18 18:56:58.000','新增权限','admin:permission:create','button',7,2,NULL,NULL,NULL,NULL,'新增权限','active',2,NULL,NULL,NULL);
INSERT INTO `admin_permissions` (`id`, `created_at`, `updated_at`, `name`, `code`, `type`, `parent_id`, `group_id`, `path`, `component`, `icon`, `method`, `description`, `status`, `sort`, `remark`, `created_by`, `updated_by`) VALUES (80,'2025-07-18 18:56:58.000','2025-07-18 18:56:58.000','编辑权限','admin:permission:update','button',7,2,NULL,NULL,NULL,NULL,'编辑权限','active',3,NULL,NULL,NULL);
INSERT INTO `admin_permissions` (`id`, `created_at`, `updated_at`, `name`, `code`, `type`, `parent_id`, `group_id`, `path`, `component`, `icon`, `method`, `description`, `status`, `sort`, `remark`, `created_by`, `updated_by`) VALUES (81,'2025-07-18 18:56:58.000','2025-07-18 18:56:58.000','删除权限','admin:permission:delete','button',7,2,NULL,NULL,NULL,NULL,'删除权限','active',4,NULL,NULL,NULL);
INSERT INTO `admin_permissions` (`id`, `created_at`, `updated_at`, `name`, `code`, `type`, `parent_id`, `group_id`, `path`, `component`, `icon`, `method`, `description`, `status`, `sort`, `remark`, `created_by`, `updated_by`) VALUES (82,'2025-07-18 18:56:58.000','2025-07-18 18:56:58.000','查看日志列表','admin:log:list','button',8,2,NULL,NULL,NULL,NULL,'查看操作日志列表','active',1,NULL,NULL,NULL);
INSERT INTO `admin_permissions` (`id`, `created_at`, `updated_at`, `name`, `code`, `type`, `parent_id`, `group_id`, `path`, `component`, `icon`, `method`, `description`, `status`, `sort`, `remark`, `created_by`, `updated_by`) VALUES (83,'2025-07-18 18:56:58.000','2025-07-18 18:56:58.000','导出日志','admin:log:export','button',8,2,NULL,NULL,NULL,NULL,'导出操作日志','active',2,NULL,NULL,NULL);
INSERT INTO `admin_permissions` (`id`, `created_at`, `updated_at`, `name`, `code`, `type`, `parent_id`, `group_id`, `path`, `component`, `icon`, `method`, `description`, `status`, `sort`, `remark`, `created_by`, `updated_by`) VALUES (84,'2025-07-18 18:57:12.000','2025-07-18 18:57:12.000','查看轮播图列表','content:swiper:list','button',9,3,NULL,NULL,NULL,NULL,'查看轮播图列表','active',1,NULL,NULL,NULL);
INSERT INTO `admin_permissions` (`id`, `created_at`, `updated_at`, `name`, `code`, `type`, `parent_id`, `group_id`, `path`, `component`, `icon`, `method`, `description`, `status`, `sort`, `remark`, `created_by`, `updated_by`) VALUES (85,'2025-07-18 18:57:12.000','2025-07-18 18:57:12.000','新增轮播图','content:swiper:create','button',9,3,NULL,NULL,NULL,NULL,'新增轮播图','active',2,NULL,NULL,NULL);
INSERT INTO `admin_permissions` (`id`, `created_at`, `updated_at`, `name`, `code`, `type`, `parent_id`, `group_id`, `path`, `component`, `icon`, `method`, `description`, `status`, `sort`, `remark`, `created_by`, `updated_by`) VALUES (86,'2025-07-18 18:57:12.000','2025-07-18 18:57:12.000','编辑轮播图','content:swiper:update','button',9,3,NULL,NULL,NULL,NULL,'编辑轮播图','active',3,NULL,NULL,NULL);
INSERT INTO `admin_permissions` (`id`, `created_at`, `updated_at`, `name`, `code`, `type`, `parent_id`, `group_id`, `path`, `component`, `icon`, `method`, `description`, `status`, `sort`, `remark`, `created_by`, `updated_by`) VALUES (87,'2025-07-18 18:57:12.000','2025-07-18 18:57:12.000','删除轮播图','content:swiper:delete','button',9,3,NULL,NULL,NULL,NULL,'删除轮播图','active',4,NULL,NULL,NULL);
INSERT INTO `admin_permissions` (`id`, `created_at`, `updated_at`, `name`, `code`, `type`, `parent_id`, `group_id`, `path`, `component`, `icon`, `method`, `description`, `status`, `sort`, `remark`, `created_by`, `updated_by`) VALUES (88,'2025-07-18 18:57:12.000','2025-07-18 18:57:12.000','查看新闻列表','content:news:list','button',10,3,NULL,NULL,NULL,NULL,'查看新闻列表','active',1,NULL,NULL,NULL);
INSERT INTO `admin_permissions` (`id`, `created_at`, `updated_at`, `name`, `code`, `type`, `parent_id`, `group_id`, `path`, `component`, `icon`, `method`, `description`, `status`, `sort`, `remark`, `created_by`, `updated_by`) VALUES (89,'2025-07-18 18:57:12.000','2025-07-18 18:57:12.000','新增新闻','content:news:create','button',10,3,NULL,NULL,NULL,NULL,'新增新闻','active',2,NULL,NULL,NULL);
INSERT INTO `admin_permissions` (`id`, `created_at`, `updated_at`, `name`, `code`, `type`, `parent_id`, `group_id`, `path`, `component`, `icon`, `method`, `description`, `status`, `sort`, `remark`, `created_by`, `updated_by`) VALUES (90,'2025-07-18 18:57:12.000','2025-07-18 18:57:12.000','编辑新闻','content:news:update','button',10,3,NULL,NULL,NULL,NULL,'编辑新闻','active',3,NULL,NULL,NULL);
INSERT INTO `admin_permissions` (`id`, `created_at`, `updated_at`, `name`, `code`, `type`, `parent_id`, `group_id`, `path`, `component`, `icon`, `method`, `description`, `status`, `sort`, `remark`, `created_by`, `updated_by`) VALUES (91,'2025-07-18 18:57:12.000','2025-07-18 18:57:12.000','删除新闻','content:news:delete','button',10,3,NULL,NULL,NULL,NULL,'删除新闻','active',4,NULL,NULL,NULL);
INSERT INTO `admin_permissions` (`id`, `created_at`, `updated_at`, `name`, `code`, `type`, `parent_id`, `group_id`, `path`, `component`, `icon`, `method`, `description`, `status`, `sort`, `remark`, `created_by`, `updated_by`) VALUES (92,'2025-07-18 18:57:12.000','2025-07-18 18:57:12.000','查看服务列表','content:service:list','button',11,3,NULL,NULL,NULL,NULL,'查看服务列表','active',1,NULL,NULL,NULL);
INSERT INTO `admin_permissions` (`id`, `created_at`, `updated_at`, `name`, `code`, `type`, `parent_id`, `group_id`, `path`, `component`, `icon`, `method`, `description`, `status`, `sort`, `remark`, `created_by`, `updated_by`) VALUES (93,'2025-07-18 18:57:12.000','2025-07-18 18:57:12.000','新增服务','content:service:create','button',11,3,NULL,NULL,NULL,NULL,'新增服务','active',2,NULL,NULL,NULL);
INSERT INTO `admin_permissions` (`id`, `created_at`, `updated_at`, `name`, `code`, `type`, `parent_id`, `group_id`, `path`, `component`, `icon`, `method`, `description`, `status`, `sort`, `remark`, `created_by`, `updated_by`) VALUES (94,'2025-07-18 18:57:12.000','2025-07-18 18:57:12.000','编辑服务','content:service:update','button',11,3,NULL,NULL,NULL,NULL,'编辑服务','active',3,NULL,NULL,NULL);
INSERT INTO `admin_permissions` (`id`, `created_at`, `updated_at`, `name`, `code`, `type`, `parent_id`, `group_id`, `path`, `component`, `icon`, `method`, `description`, `status`, `sort`, `remark`, `created_by`, `updated_by`) VALUES (95,'2025-07-18 18:57:12.000','2025-07-18 18:57:12.000','删除服务','content:service:delete','button',11,3,NULL,NULL,NULL,NULL,'删除服务','active',4,NULL,NULL,NULL);
INSERT INTO `admin_permissions` (`id`, `created_at`, `updated_at`, `name`, `code`, `type`, `parent_id`, `group_id`, `path`, `component`, `icon`, `method`, `description`, `status`, `sort`, `remark`, `created_by`, `updated_by`) VALUES (96,'2025-07-18 18:57:26.000','2025-07-18 18:57:26.000','查看项目列表','content:project:list','button',12,3,NULL,NULL,NULL,NULL,'查看项目案例列表','active',1,NULL,NULL,NULL);
INSERT INTO `admin_permissions` (`id`, `created_at`, `updated_at`, `name`, `code`, `type`, `parent_id`, `group_id`, `path`, `component`, `icon`, `method`, `description`, `status`, `sort`, `remark`, `created_by`, `updated_by`) VALUES (97,'2025-07-18 18:57:26.000','2025-07-18 18:57:26.000','新增项目','content:project:create','button',12,3,NULL,NULL,NULL,NULL,'新增项目案例','active',2,NULL,NULL,NULL);
INSERT INTO `admin_permissions` (`id`, `created_at`, `updated_at`, `name`, `code`, `type`, `parent_id`, `group_id`, `path`, `component`, `icon`, `method`, `description`, `status`, `sort`, `remark`, `created_by`, `updated_by`) VALUES (98,'2025-07-18 18:57:26.000','2025-07-18 18:57:26.000','编辑项目','content:project:update','button',12,3,NULL,NULL,NULL,NULL,'编辑项目案例','active',3,NULL,NULL,NULL);
INSERT INTO `admin_permissions` (`id`, `created_at`, `updated_at`, `name`, `code`, `type`, `parent_id`, `group_id`, `path`, `component`, `icon`, `method`, `description`, `status`, `sort`, `remark`, `created_by`, `updated_by`) VALUES (99,'2025-07-18 18:57:26.000','2025-07-18 18:57:26.000','删除项目','content:project:delete','button',12,3,NULL,NULL,NULL,NULL,'删除项目案例','active',4,NULL,NULL,NULL);
INSERT INTO `admin_permissions` (`id`, `created_at`, `updated_at`, `name`, `code`, `type`, `parent_id`, `group_id`, `path`, `component`, `icon`, `method`, `description`, `status`, `sort`, `remark`, `created_by`, `updated_by`) VALUES (100,'2025-07-18 18:57:26.000','2025-07-18 18:57:26.000','查看合作伙伴列表','content:partner:list','button',13,3,NULL,NULL,NULL,NULL,'查看合作伙伴列表','active',1,NULL,NULL,NULL);
INSERT INTO `admin_permissions` (`id`, `created_at`, `updated_at`, `name`, `code`, `type`, `parent_id`, `group_id`, `path`, `component`, `icon`, `method`, `description`, `status`, `sort`, `remark`, `created_by`, `updated_by`) VALUES (101,'2025-07-18 18:57:26.000','2025-07-18 18:57:26.000','新增合作伙伴','content:partner:create','button',13,3,NULL,NULL,NULL,NULL,'新增合作伙伴','active',2,NULL,NULL,NULL);
INSERT INTO `admin_permissions` (`id`, `created_at`, `updated_at`, `name`, `code`, `type`, `parent_id`, `group_id`, `path`, `component`, `icon`, `method`, `description`, `status`, `sort`, `remark`, `created_by`, `updated_by`) VALUES (102,'2025-07-18 18:57:26.000','2025-07-18 18:57:26.000','编辑合作伙伴','content:partner:update','button',13,3,NULL,NULL,NULL,NULL,'编辑合作伙伴','active',3,NULL,NULL,NULL);
INSERT INTO `admin_permissions` (`id`, `created_at`, `updated_at`, `name`, `code`, `type`, `parent_id`, `group_id`, `path`, `component`, `icon`, `method`, `description`, `status`, `sort`, `remark`, `created_by`, `updated_by`) VALUES (103,'2025-07-18 18:57:26.000','2025-07-18 18:57:26.000','删除合作伙伴','content:partner:delete','button',13,3,NULL,NULL,NULL,NULL,'删除合作伙伴','active',4,NULL,NULL,NULL);
INSERT INTO `admin_permissions` (`id`, `created_at`, `updated_at`, `name`, `code`, `type`, `parent_id`, `group_id`, `path`, `component`, `icon`, `method`, `description`, `status`, `sort`, `remark`, `created_by`, `updated_by`) VALUES (104,'2025-07-18 18:57:26.000','2025-07-18 18:57:26.000','查看友情链接列表','content:link:list','button',14,3,NULL,NULL,NULL,NULL,'查看友情链接列表','active',1,NULL,NULL,NULL);
INSERT INTO `admin_permissions` (`id`, `created_at`, `updated_at`, `name`, `code`, `type`, `parent_id`, `group_id`, `path`, `component`, `icon`, `method`, `description`, `status`, `sort`, `remark`, `created_by`, `updated_by`) VALUES (105,'2025-07-18 18:57:26.000','2025-07-18 18:57:26.000','新增友情链接','content:link:create','button',14,3,NULL,NULL,NULL,NULL,'新增友情链接','active',2,NULL,NULL,NULL);
INSERT INTO `admin_permissions` (`id`, `created_at`, `updated_at`, `name`, `code`, `type`, `parent_id`, `group_id`, `path`, `component`, `icon`, `method`, `description`, `status`, `sort`, `remark`, `created_by`, `updated_by`) VALUES (106,'2025-07-18 18:57:26.000','2025-07-18 18:57:26.000','编辑友情链接','content:link:update','button',14,3,NULL,NULL,NULL,NULL,'编辑友情链接','active',3,NULL,NULL,NULL);
INSERT INTO `admin_permissions` (`id`, `created_at`, `updated_at`, `name`, `code`, `type`, `parent_id`, `group_id`, `path`, `component`, `icon`, `method`, `description`, `status`, `sort`, `remark`, `created_by`, `updated_by`) VALUES (107,'2025-07-18 18:57:26.000','2025-07-18 18:57:26.000','删除友情链接','content:link:delete','button',14,3,NULL,NULL,NULL,NULL,'删除友情链接','active',4,NULL,NULL,NULL);
INSERT INTO `admin_permissions` (`id`, `created_at`, `updated_at`, `name`, `code`, `type`, `parent_id`, `group_id`, `path`, `component`, `icon`, `method`, `description`, `status`, `sort`, `remark`, `created_by`, `updated_by`) VALUES (108,'2025-07-18 18:57:38.000','2025-07-18 18:57:38.000','查看招聘列表','content:recruitment:list','button',15,3,NULL,NULL,NULL,NULL,'查看招聘信息列表','active',1,NULL,NULL,NULL);
INSERT INTO `admin_permissions` (`id`, `created_at`, `updated_at`, `name`, `code`, `type`, `parent_id`, `group_id`, `path`, `component`, `icon`, `method`, `description`, `status`, `sort`, `remark`, `created_by`, `updated_by`) VALUES (109,'2025-07-18 18:57:38.000','2025-07-18 18:57:38.000','新增招聘','content:recruitment:create','button',15,3,NULL,NULL,NULL,NULL,'新增招聘信息','active',2,NULL,NULL,NULL);
INSERT INTO `admin_permissions` (`id`, `created_at`, `updated_at`, `name`, `code`, `type`, `parent_id`, `group_id`, `path`, `component`, `icon`, `method`, `description`, `status`, `sort`, `remark`, `created_by`, `updated_by`) VALUES (110,'2025-07-18 18:57:38.000','2025-07-18 18:57:38.000','编辑招聘','content:recruitment:update','button',15,3,NULL,NULL,NULL,NULL,'编辑招聘信息','active',3,NULL,NULL,NULL);
INSERT INTO `admin_permissions` (`id`, `created_at`, `updated_at`, `name`, `code`, `type`, `parent_id`, `group_id`, `path`, `component`, `icon`, `method`, `description`, `status`, `sort`, `remark`, `created_by`, `updated_by`) VALUES (111,'2025-07-18 18:57:38.000','2025-07-18 18:57:38.000','删除招聘','content:recruitment:delete','button',15,3,NULL,NULL,NULL,NULL,'删除招聘信息','active',4,NULL,NULL,NULL);
INSERT INTO `admin_permissions` (`id`, `created_at`, `updated_at`, `name`, `code`, `type`, `parent_id`, `group_id`, `path`, `component`, `icon`, `method`, `description`, `status`, `sort`, `remark`, `created_by`, `updated_by`) VALUES (112,'2025-07-18 18:57:38.000','2025-07-18 18:57:38.000','查看平台列表','content:platform:list','button',16,3,NULL,NULL,NULL,NULL,'查看平台列表','active',1,NULL,NULL,NULL);
INSERT INTO `admin_permissions` (`id`, `created_at`, `updated_at`, `name`, `code`, `type`, `parent_id`, `group_id`, `path`, `component`, `icon`, `method`, `description`, `status`, `sort`, `remark`, `created_by`, `updated_by`) VALUES (113,'2025-07-18 18:57:38.000','2025-07-18 18:57:38.000','新增平台','content:platform:create','button',16,3,NULL,NULL,NULL,NULL,'新增平台','active',2,NULL,NULL,NULL);
INSERT INTO `admin_permissions` (`id`, `created_at`, `updated_at`, `name`, `code`, `type`, `parent_id`, `group_id`, `path`, `component`, `icon`, `method`, `description`, `status`, `sort`, `remark`, `created_by`, `updated_by`) VALUES (114,'2025-07-18 18:57:38.000','2025-07-18 18:57:38.000','编辑平台','content:platform:update','button',16,3,NULL,NULL,NULL,NULL,'编辑平台','active',3,NULL,NULL,NULL);
INSERT INTO `admin_permissions` (`id`, `created_at`, `updated_at`, `name`, `code`, `type`, `parent_id`, `group_id`, `path`, `component`, `icon`, `method`, `description`, `status`, `sort`, `remark`, `created_by`, `updated_by`) VALUES (115,'2025-07-18 18:57:38.000','2025-07-18 18:57:38.000','删除平台','content:platform:delete','button',16,3,NULL,NULL,NULL,NULL,'删除平台','active',4,NULL,NULL,NULL);
INSERT INTO `admin_permissions` (`id`, `created_at`, `updated_at`, `name`, `code`, `type`, `parent_id`, `group_id`, `path`, `component`, `icon`, `method`, `description`, `status`, `sort`, `remark`, `created_by`, `updated_by`) VALUES (116,'2025-07-18 18:57:38.000','2025-07-18 18:57:38.000','查看系统设置','system:setting:view','button',17,1,NULL,NULL,NULL,NULL,'查看系统设置','active',1,NULL,NULL,NULL);
INSERT INTO `admin_permissions` (`id`, `created_at`, `updated_at`, `name`, `code`, `type`, `parent_id`, `group_id`, `path`, `component`, `icon`, `method`, `description`, `status`, `sort`, `remark`, `created_by`, `updated_by`) VALUES (117,'2025-07-18 18:57:38.000','2025-07-18 18:57:38.000','修改系统设置','system:setting:update','button',17,1,NULL,NULL,NULL,NULL,'修改系统设置','active',2,NULL,NULL,NULL);
INSERT INTO `admin_permissions` (`id`, `created_at`, `updated_at`, `name`, `code`, `type`, `parent_id`, `group_id`, `path`, `component`, `icon`, `method`, `description`, `status`, `sort`, `remark`, `created_by`, `updated_by`) VALUES (119,'2025-07-19 14:25:04.000','2025-07-19 14:25:04.000','查看管理员','system.admin.view','api',2,NULL,'/api/admin/users',NULL,NULL,'GET','查看管理员列表','active',1,NULL,NULL,NULL);
INSERT INTO `admin_permissions` (`id`, `created_at`, `updated_at`, `name`, `code`, `type`, `parent_id`, `group_id`, `path`, `component`, `icon`, `method`, `description`, `status`, `sort`, `remark`, `created_by`, `updated_by`) VALUES (120,'2025-07-19 14:25:04.000','2025-07-19 14:25:04.000','创建管理员','system.admin.create','api',2,NULL,'/api/admin/users',NULL,NULL,'POST','创建管理员','active',2,NULL,NULL,NULL);
INSERT INTO `admin_permissions` (`id`, `created_at`, `updated_at`, `name`, `code`, `type`, `parent_id`, `group_id`, `path`, `component`, `icon`, `method`, `description`, `status`, `sort`, `remark`, `created_by`, `updated_by`) VALUES (121,'2025-07-19 14:25:04.000','2025-07-19 14:25:04.000','编辑管理员','system.admin.edit','api',2,NULL,'/api/admin/users',NULL,NULL,'PUT','编辑管理员','active',3,NULL,NULL,NULL);
INSERT INTO `admin_permissions` (`id`, `created_at`, `updated_at`, `name`, `code`, `type`, `parent_id`, `group_id`, `path`, `component`, `icon`, `method`, `description`, `status`, `sort`, `remark`, `created_by`, `updated_by`) VALUES (122,'2025-07-19 14:25:04.000','2025-07-19 14:25:04.000','删除管理员','system.admin.delete','api',2,NULL,'/api/admin/users',NULL,NULL,'DELETE','删除管理员','active',4,NULL,NULL,NULL);
INSERT INTO `admin_permissions` (`id`, `created_at`, `updated_at`, `name`, `code`, `type`, `parent_id`, `group_id`, `path`, `component`, `icon`, `method`, `description`, `status`, `sort`, `remark`, `created_by`, `updated_by`) VALUES (123,'2025-07-19 14:25:15.000','2025-07-19 14:25:15.000','查看角色','system.role.view','api',6,NULL,'/api/admin/roles',NULL,NULL,'GET','查看角色列表','active',1,NULL,NULL,NULL);
INSERT INTO `admin_permissions` (`id`, `created_at`, `updated_at`, `name`, `code`, `type`, `parent_id`, `group_id`, `path`, `component`, `icon`, `method`, `description`, `status`, `sort`, `remark`, `created_by`, `updated_by`) VALUES (124,'2025-07-19 14:25:15.000','2025-07-19 14:25:15.000','创建角色','system.role.create','api',6,NULL,'/api/admin/roles',NULL,NULL,'POST','创建角色','active',2,NULL,NULL,NULL);
INSERT INTO `admin_permissions` (`id`, `created_at`, `updated_at`, `name`, `code`, `type`, `parent_id`, `group_id`, `path`, `component`, `icon`, `method`, `description`, `status`, `sort`, `remark`, `created_by`, `updated_by`) VALUES (125,'2025-07-19 14:25:15.000','2025-07-19 14:25:15.000','编辑角色','system.role.edit','api',6,NULL,'/api/admin/roles',NULL,NULL,'PUT','编辑角色','active',3,NULL,NULL,NULL);
INSERT INTO `admin_permissions` (`id`, `created_at`, `updated_at`, `name`, `code`, `type`, `parent_id`, `group_id`, `path`, `component`, `icon`, `method`, `description`, `status`, `sort`, `remark`, `created_by`, `updated_by`) VALUES (126,'2025-07-19 14:25:15.000','2025-07-19 14:25:15.000','删除角色','system.role.delete','api',6,NULL,'/api/admin/roles',NULL,NULL,'DELETE','删除角色','active',4,NULL,NULL,NULL);
INSERT INTO `admin_permissions` (`id`, `created_at`, `updated_at`, `name`, `code`, `type`, `parent_id`, `group_id`, `path`, `component`, `icon`, `method`, `description`, `status`, `sort`, `remark`, `created_by`, `updated_by`) VALUES (127,'2025-07-19 14:25:22.000','2025-07-19 14:25:22.000','查看权限','system.permission.view','api',7,NULL,'/api/admin/permissions',NULL,NULL,'GET','查看权限列表','active',1,NULL,NULL,NULL);
INSERT INTO `admin_permissions` (`id`, `created_at`, `updated_at`, `name`, `code`, `type`, `parent_id`, `group_id`, `path`, `component`, `icon`, `method`, `description`, `status`, `sort`, `remark`, `created_by`, `updated_by`) VALUES (128,'2025-07-19 14:25:22.000','2025-07-19 14:25:22.000','创建权限','system.permission.create','api',7,NULL,'/api/admin/permissions',NULL,NULL,'POST','创建权限','active',2,NULL,NULL,NULL);
INSERT INTO `admin_permissions` (`id`, `created_at`, `updated_at`, `name`, `code`, `type`, `parent_id`, `group_id`, `path`, `component`, `icon`, `method`, `description`, `status`, `sort`, `remark`, `created_by`, `updated_by`) VALUES (129,'2025-07-19 14:25:22.000','2025-07-19 14:25:22.000','编辑权限','system.permission.edit','api',7,NULL,'/api/admin/permissions',NULL,NULL,'PUT','编辑权限','active',3,NULL,NULL,NULL);
INSERT INTO `admin_permissions` (`id`, `created_at`, `updated_at`, `name`, `code`, `type`, `parent_id`, `group_id`, `path`, `component`, `icon`, `method`, `description`, `status`, `sort`, `remark`, `created_by`, `updated_by`) VALUES (130,'2025-07-19 14:25:22.000','2025-07-19 14:25:22.000','删除权限','system.permission.delete','api',7,NULL,'/api/admin/permissions',NULL,NULL,'DELETE','删除权限','active',4,NULL,NULL,NULL);
INSERT INTO `admin_permissions` (`id`, `created_at`, `updated_at`, `name`, `code`, `type`, `parent_id`, `group_id`, `path`, `component`, `icon`, `method`, `description`, `status`, `sort`, `remark`, `created_by`, `updated_by`) VALUES (131,'2025-07-19 14:25:29.000','2025-07-19 14:25:29.000','查看日志','system.log.view','api',8,NULL,'/api/admin/logs',NULL,NULL,'GET','查看操作日志','active',1,NULL,NULL,NULL);
INSERT INTO `admin_permissions` (`id`, `created_at`, `updated_at`, `name`, `code`, `type`, `parent_id`, `group_id`, `path`, `component`, `icon`, `method`, `description`, `status`, `sort`, `remark`, `created_by`, `updated_by`) VALUES (132,'2025-07-19 14:25:29.000','2025-07-19 14:25:29.000','删除日志','system.log.delete','api',8,NULL,'/api/admin/logs',NULL,NULL,'DELETE','删除操作日志','active',2,NULL,NULL,NULL);
INSERT INTO `admin_permissions` (`id`, `created_at`, `updated_at`, `name`, `code`, `type`, `parent_id`, `group_id`, `path`, `component`, `icon`, `method`, `description`, `status`, `sort`, `remark`, `created_by`, `updated_by`) VALUES (133,'2025-07-19 14:25:29.000','2025-07-19 14:25:29.000','查看仪表板','system.dashboard.view','api',1,NULL,'/api/admin/dashboard',NULL,NULL,'GET','查看仪表板统计','active',5,NULL,NULL,NULL);
INSERT INTO `admin_permissions` (`id`, `created_at`, `updated_at`, `name`, `code`, `type`, `parent_id`, `group_id`, `path`, `component`, `icon`, `method`, `description`, `status`, `sort`, `remark`, `created_by`, `updated_by`) VALUES (134,'2025-07-19 14:25:39.000','2025-07-19 14:25:39.000','轮播图管理','content.swiper','api',3,NULL,'/api/admin/swipers',NULL,NULL,'GET','轮播图管理','active',1,NULL,NULL,NULL);
INSERT INTO `admin_permissions` (`id`, `created_at`, `updated_at`, `name`, `code`, `type`, `parent_id`, `group_id`, `path`, `component`, `icon`, `method`, `description`, `status`, `sort`, `remark`, `created_by`, `updated_by`) VALUES (135,'2025-07-19 14:25:39.000','2025-07-19 14:25:39.000','服务管理','content.service','api',3,NULL,'/api/admin/services',NULL,NULL,'GET','服务内容管理','active',2,NULL,NULL,NULL);
INSERT INTO `admin_permissions` (`id`, `created_at`, `updated_at`, `name`, `code`, `type`, `parent_id`, `group_id`, `path`, `component`, `icon`, `method`, `description`, `status`, `sort`, `remark`, `created_by`, `updated_by`) VALUES (136,'2025-07-19 14:25:39.000','2025-07-19 14:25:39.000','案例管理','content.case','api',3,NULL,'/api/admin/cases',NULL,NULL,'GET','项目案例管理','active',3,NULL,NULL,NULL);
INSERT INTO `admin_permissions` (`id`, `created_at`, `updated_at`, `name`, `code`, `type`, `parent_id`, `group_id`, `path`, `component`, `icon`, `method`, `description`, `status`, `sort`, `remark`, `created_by`, `updated_by`) VALUES (137,'2025-07-19 14:25:39.000','2025-07-19 14:25:39.000','招聘管理','content.recruitment','api',3,NULL,'/api/admin/recruitments',NULL,NULL,'GET','招聘信息管理','active',4,NULL,NULL,NULL);
INSERT INTO `admin_permissions` (`id`, `created_at`, `updated_at`, `name`, `code`, `type`, `parent_id`, `group_id`, `path`, `component`, `icon`, `method`, `description`, `status`, `sort`, `remark`, `created_by`, `updated_by`) VALUES (138,'2025-07-19 14:25:39.000','2025-07-19 14:25:39.000','合作伙伴管理','content.partner','api',3,NULL,'/api/admin/partners',NULL,NULL,'GET','合作伙伴管理','active',5,NULL,NULL,NULL);
INSERT INTO `admin_permissions` (`id`, `created_at`, `updated_at`, `name`, `code`, `type`, `parent_id`, `group_id`, `path`, `component`, `icon`, `method`, `description`, `status`, `sort`, `remark`, `created_by`, `updated_by`) VALUES (139,'2025-07-19 14:25:39.000','2025-07-19 14:25:39.000','友情链接管理','content.link','api',3,NULL,'/api/admin/links',NULL,NULL,'GET','友情链接管理','active',6,NULL,NULL,NULL);
/*!40000 ALTER TABLE `admin_permissions` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Dumping data for table `admin_role_permissions`
--

LOCK TABLES `admin_role_permissions` WRITE;
/*!40000 ALTER TABLE `admin_role_permissions` DISABLE KEYS */;
INSERT INTO `admin_role_permissions` (`id`, `role_id`, `permission_id`, `created_by`, `created_at`) VALUES (61,1,59,NULL,'2025-06-28 23:41:52.295');
INSERT INTO `admin_role_permissions` (`id`, `role_id`, `permission_id`, `created_by`, `created_at`) VALUES (63,1,61,NULL,'2025-06-28 23:41:52.298');
INSERT INTO `admin_role_permissions` (`id`, `role_id`, `permission_id`, `created_by`, `created_at`) VALUES (64,1,62,NULL,'2025-06-28 23:41:52.300');
INSERT INTO `admin_role_permissions` (`id`, `role_id`, `permission_id`, `created_by`, `created_at`) VALUES (65,1,63,NULL,'2025-06-28 23:41:52.302');
INSERT INTO `admin_role_permissions` (`id`, `role_id`, `permission_id`, `created_by`, `created_at`) VALUES (66,1,64,NULL,'2025-06-28 23:41:52.304');
INSERT INTO `admin_role_permissions` (`id`, `role_id`, `permission_id`, `created_by`, `created_at`) VALUES (67,1,65,NULL,'2025-06-28 23:41:52.305');
INSERT INTO `admin_role_permissions` (`id`, `role_id`, `permission_id`, `created_by`, `created_at`) VALUES (68,1,66,NULL,'2025-06-28 23:41:52.307');
INSERT INTO `admin_role_permissions` (`id`, `role_id`, `permission_id`, `created_by`, `created_at`) VALUES (69,1,67,NULL,'2025-06-28 23:41:52.308');
INSERT INTO `admin_role_permissions` (`id`, `role_id`, `permission_id`, `created_by`, `created_at`) VALUES (70,1,68,NULL,'2025-06-28 23:41:52.309');
INSERT INTO `admin_role_permissions` (`id`, `role_id`, `permission_id`, `created_by`, `created_at`) VALUES (71,1,69,NULL,'2025-06-28 23:41:52.311');
INSERT INTO `admin_role_permissions` (`id`, `role_id`, `permission_id`, `created_by`, `created_at`) VALUES (72,1,70,NULL,'2025-06-28 23:41:52.312');
INSERT INTO `admin_role_permissions` (`id`, `role_id`, `permission_id`, `created_by`, `created_at`) VALUES (73,1,71,NULL,'2025-06-28 23:41:52.313');
INSERT INTO `admin_role_permissions` (`id`, `role_id`, `permission_id`, `created_by`, `created_at`) VALUES (74,1,72,NULL,'2025-06-28 23:41:52.315');
INSERT INTO `admin_role_permissions` (`id`, `role_id`, `permission_id`, `created_by`, `created_at`) VALUES (75,1,73,NULL,'2025-06-28 23:41:52.316');
INSERT INTO `admin_role_permissions` (`id`, `role_id`, `permission_id`, `created_by`, `created_at`) VALUES (76,1,74,NULL,'2025-06-28 23:41:52.318');
INSERT INTO `admin_role_permissions` (`id`, `role_id`, `permission_id`, `created_by`, `created_at`) VALUES (77,1,75,NULL,'2025-06-28 23:41:52.319');
INSERT INTO `admin_role_permissions` (`id`, `role_id`, `permission_id`, `created_by`, `created_at`) VALUES (78,1,76,NULL,'2025-06-28 23:41:52.321');
INSERT INTO `admin_role_permissions` (`id`, `role_id`, `permission_id`, `created_by`, `created_at`) VALUES (79,1,77,NULL,'2025-06-28 23:41:52.322');
INSERT INTO `admin_role_permissions` (`id`, `role_id`, `permission_id`, `created_by`, `created_at`) VALUES (80,1,78,NULL,'2025-06-28 23:41:52.324');
INSERT INTO `admin_role_permissions` (`id`, `role_id`, `permission_id`, `created_by`, `created_at`) VALUES (81,1,79,NULL,'2025-06-28 23:41:52.325');
INSERT INTO `admin_role_permissions` (`id`, `role_id`, `permission_id`, `created_by`, `created_at`) VALUES (82,1,80,NULL,'2025-06-28 23:41:52.326');
INSERT INTO `admin_role_permissions` (`id`, `role_id`, `permission_id`, `created_by`, `created_at`) VALUES (83,1,81,NULL,'2025-06-28 23:41:52.328');
INSERT INTO `admin_role_permissions` (`id`, `role_id`, `permission_id`, `created_by`, `created_at`) VALUES (84,1,82,NULL,'2025-06-28 23:41:52.329');
INSERT INTO `admin_role_permissions` (`id`, `role_id`, `permission_id`, `created_by`, `created_at`) VALUES (85,1,83,NULL,'2025-06-28 23:41:52.330');
INSERT INTO `admin_role_permissions` (`id`, `role_id`, `permission_id`, `created_by`, `created_at`) VALUES (86,1,84,NULL,'2025-06-28 23:41:52.331');
INSERT INTO `admin_role_permissions` (`id`, `role_id`, `permission_id`, `created_by`, `created_at`) VALUES (87,1,85,NULL,'2025-06-28 23:41:52.333');
INSERT INTO `admin_role_permissions` (`id`, `role_id`, `permission_id`, `created_by`, `created_at`) VALUES (88,1,86,NULL,'2025-06-28 23:41:52.334');
INSERT INTO `admin_role_permissions` (`id`, `role_id`, `permission_id`, `created_by`, `created_at`) VALUES (89,1,87,NULL,'2025-06-28 23:41:52.336');
INSERT INTO `admin_role_permissions` (`id`, `role_id`, `permission_id`, `created_by`, `created_at`) VALUES (90,8,1,NULL,'2025-07-18 19:08:38.000');
INSERT INTO `admin_role_permissions` (`id`, `role_id`, `permission_id`, `created_by`, `created_at`) VALUES (91,8,2,NULL,'2025-07-18 19:08:38.000');
INSERT INTO `admin_role_permissions` (`id`, `role_id`, `permission_id`, `created_by`, `created_at`) VALUES (92,8,3,NULL,'2025-07-18 19:08:38.000');
INSERT INTO `admin_role_permissions` (`id`, `role_id`, `permission_id`, `created_by`, `created_at`) VALUES (93,8,4,NULL,'2025-07-18 19:08:38.000');
INSERT INTO `admin_role_permissions` (`id`, `role_id`, `permission_id`, `created_by`, `created_at`) VALUES (94,8,5,NULL,'2025-07-18 19:08:38.000');
INSERT INTO `admin_role_permissions` (`id`, `role_id`, `permission_id`, `created_by`, `created_at`) VALUES (95,8,6,NULL,'2025-07-18 19:08:38.000');
INSERT INTO `admin_role_permissions` (`id`, `role_id`, `permission_id`, `created_by`, `created_at`) VALUES (96,8,7,NULL,'2025-07-18 19:08:38.000');
INSERT INTO `admin_role_permissions` (`id`, `role_id`, `permission_id`, `created_by`, `created_at`) VALUES (97,8,8,NULL,'2025-07-18 19:08:38.000');
INSERT INTO `admin_role_permissions` (`id`, `role_id`, `permission_id`, `created_by`, `created_at`) VALUES (98,8,9,NULL,'2025-07-18 19:08:38.000');
INSERT INTO `admin_role_permissions` (`id`, `role_id`, `permission_id`, `created_by`, `created_at`) VALUES (99,8,10,NULL,'2025-07-18 19:08:38.000');
INSERT INTO `admin_role_permissions` (`id`, `role_id`, `permission_id`, `created_by`, `created_at`) VALUES (100,8,11,NULL,'2025-07-18 19:08:38.000');
INSERT INTO `admin_role_permissions` (`id`, `role_id`, `permission_id`, `created_by`, `created_at`) VALUES (101,8,12,NULL,'2025-07-18 19:08:38.000');
INSERT INTO `admin_role_permissions` (`id`, `role_id`, `permission_id`, `created_by`, `created_at`) VALUES (102,8,13,NULL,'2025-07-18 19:08:38.000');
INSERT INTO `admin_role_permissions` (`id`, `role_id`, `permission_id`, `created_by`, `created_at`) VALUES (103,8,14,NULL,'2025-07-18 19:08:38.000');
INSERT INTO `admin_role_permissions` (`id`, `role_id`, `permission_id`, `created_by`, `created_at`) VALUES (104,8,15,NULL,'2025-07-18 19:08:38.000');
INSERT INTO `admin_role_permissions` (`id`, `role_id`, `permission_id`, `created_by`, `created_at`) VALUES (105,8,16,NULL,'2025-07-18 19:08:38.000');
INSERT INTO `admin_role_permissions` (`id`, `role_id`, `permission_id`, `created_by`, `created_at`) VALUES (106,8,17,NULL,'2025-07-18 19:08:38.000');
INSERT INTO `admin_role_permissions` (`id`, `role_id`, `permission_id`, `created_by`, `created_at`) VALUES (107,8,68,NULL,'2025-07-18 19:08:38.000');
INSERT INTO `admin_role_permissions` (`id`, `role_id`, `permission_id`, `created_by`, `created_at`) VALUES (108,8,69,NULL,'2025-07-18 19:08:38.000');
INSERT INTO `admin_role_permissions` (`id`, `role_id`, `permission_id`, `created_by`, `created_at`) VALUES (109,8,70,NULL,'2025-07-18 19:08:38.000');
INSERT INTO `admin_role_permissions` (`id`, `role_id`, `permission_id`, `created_by`, `created_at`) VALUES (110,8,71,NULL,'2025-07-18 19:08:38.000');
INSERT INTO `admin_role_permissions` (`id`, `role_id`, `permission_id`, `created_by`, `created_at`) VALUES (111,8,72,NULL,'2025-07-18 19:08:38.000');
INSERT INTO `admin_role_permissions` (`id`, `role_id`, `permission_id`, `created_by`, `created_at`) VALUES (112,8,73,NULL,'2025-07-18 19:08:38.000');
INSERT INTO `admin_role_permissions` (`id`, `role_id`, `permission_id`, `created_by`, `created_at`) VALUES (113,8,74,NULL,'2025-07-18 19:08:38.000');
INSERT INTO `admin_role_permissions` (`id`, `role_id`, `permission_id`, `created_by`, `created_at`) VALUES (114,8,75,NULL,'2025-07-18 19:08:38.000');
INSERT INTO `admin_role_permissions` (`id`, `role_id`, `permission_id`, `created_by`, `created_at`) VALUES (115,8,76,NULL,'2025-07-18 19:08:38.000');
INSERT INTO `admin_role_permissions` (`id`, `role_id`, `permission_id`, `created_by`, `created_at`) VALUES (116,8,77,NULL,'2025-07-18 19:08:38.000');
INSERT INTO `admin_role_permissions` (`id`, `role_id`, `permission_id`, `created_by`, `created_at`) VALUES (117,8,78,NULL,'2025-07-18 19:08:38.000');
INSERT INTO `admin_role_permissions` (`id`, `role_id`, `permission_id`, `created_by`, `created_at`) VALUES (118,8,79,NULL,'2025-07-18 19:08:38.000');
INSERT INTO `admin_role_permissions` (`id`, `role_id`, `permission_id`, `created_by`, `created_at`) VALUES (119,8,80,NULL,'2025-07-18 19:08:38.000');
INSERT INTO `admin_role_permissions` (`id`, `role_id`, `permission_id`, `created_by`, `created_at`) VALUES (120,8,81,NULL,'2025-07-18 19:08:38.000');
INSERT INTO `admin_role_permissions` (`id`, `role_id`, `permission_id`, `created_by`, `created_at`) VALUES (121,8,82,NULL,'2025-07-18 19:08:38.000');
INSERT INTO `admin_role_permissions` (`id`, `role_id`, `permission_id`, `created_by`, `created_at`) VALUES (122,8,83,NULL,'2025-07-18 19:08:38.000');
INSERT INTO `admin_role_permissions` (`id`, `role_id`, `permission_id`, `created_by`, `created_at`) VALUES (123,8,84,NULL,'2025-07-18 19:08:38.000');
INSERT INTO `admin_role_permissions` (`id`, `role_id`, `permission_id`, `created_by`, `created_at`) VALUES (124,8,85,NULL,'2025-07-18 19:08:38.000');
INSERT INTO `admin_role_permissions` (`id`, `role_id`, `permission_id`, `created_by`, `created_at`) VALUES (125,8,86,NULL,'2025-07-18 19:08:38.000');
INSERT INTO `admin_role_permissions` (`id`, `role_id`, `permission_id`, `created_by`, `created_at`) VALUES (126,8,87,NULL,'2025-07-18 19:08:38.000');
INSERT INTO `admin_role_permissions` (`id`, `role_id`, `permission_id`, `created_by`, `created_at`) VALUES (127,8,88,NULL,'2025-07-18 19:08:38.000');
INSERT INTO `admin_role_permissions` (`id`, `role_id`, `permission_id`, `created_by`, `created_at`) VALUES (128,8,89,NULL,'2025-07-18 19:08:38.000');
INSERT INTO `admin_role_permissions` (`id`, `role_id`, `permission_id`, `created_by`, `created_at`) VALUES (129,8,90,NULL,'2025-07-18 19:08:38.000');
INSERT INTO `admin_role_permissions` (`id`, `role_id`, `permission_id`, `created_by`, `created_at`) VALUES (130,8,91,NULL,'2025-07-18 19:08:38.000');
INSERT INTO `admin_role_permissions` (`id`, `role_id`, `permission_id`, `created_by`, `created_at`) VALUES (131,8,92,NULL,'2025-07-18 19:08:38.000');
INSERT INTO `admin_role_permissions` (`id`, `role_id`, `permission_id`, `created_by`, `created_at`) VALUES (132,8,93,NULL,'2025-07-18 19:08:38.000');
INSERT INTO `admin_role_permissions` (`id`, `role_id`, `permission_id`, `created_by`, `created_at`) VALUES (133,8,94,NULL,'2025-07-18 19:08:38.000');
INSERT INTO `admin_role_permissions` (`id`, `role_id`, `permission_id`, `created_by`, `created_at`) VALUES (134,8,95,NULL,'2025-07-18 19:08:38.000');
INSERT INTO `admin_role_permissions` (`id`, `role_id`, `permission_id`, `created_by`, `created_at`) VALUES (135,8,96,NULL,'2025-07-18 19:08:38.000');
INSERT INTO `admin_role_permissions` (`id`, `role_id`, `permission_id`, `created_by`, `created_at`) VALUES (136,8,97,NULL,'2025-07-18 19:08:38.000');
INSERT INTO `admin_role_permissions` (`id`, `role_id`, `permission_id`, `created_by`, `created_at`) VALUES (137,8,98,NULL,'2025-07-18 19:08:38.000');
INSERT INTO `admin_role_permissions` (`id`, `role_id`, `permission_id`, `created_by`, `created_at`) VALUES (138,8,99,NULL,'2025-07-18 19:08:38.000');
INSERT INTO `admin_role_permissions` (`id`, `role_id`, `permission_id`, `created_by`, `created_at`) VALUES (139,8,100,NULL,'2025-07-18 19:08:38.000');
INSERT INTO `admin_role_permissions` (`id`, `role_id`, `permission_id`, `created_by`, `created_at`) VALUES (140,8,101,NULL,'2025-07-18 19:08:38.000');
INSERT INTO `admin_role_permissions` (`id`, `role_id`, `permission_id`, `created_by`, `created_at`) VALUES (141,8,102,NULL,'2025-07-18 19:08:38.000');
INSERT INTO `admin_role_permissions` (`id`, `role_id`, `permission_id`, `created_by`, `created_at`) VALUES (142,8,103,NULL,'2025-07-18 19:08:38.000');
INSERT INTO `admin_role_permissions` (`id`, `role_id`, `permission_id`, `created_by`, `created_at`) VALUES (143,8,104,NULL,'2025-07-18 19:08:38.000');
INSERT INTO `admin_role_permissions` (`id`, `role_id`, `permission_id`, `created_by`, `created_at`) VALUES (144,8,105,NULL,'2025-07-18 19:08:38.000');
INSERT INTO `admin_role_permissions` (`id`, `role_id`, `permission_id`, `created_by`, `created_at`) VALUES (145,8,106,NULL,'2025-07-18 19:08:38.000');
INSERT INTO `admin_role_permissions` (`id`, `role_id`, `permission_id`, `created_by`, `created_at`) VALUES (146,8,107,NULL,'2025-07-18 19:08:38.000');
INSERT INTO `admin_role_permissions` (`id`, `role_id`, `permission_id`, `created_by`, `created_at`) VALUES (147,8,108,NULL,'2025-07-18 19:08:38.000');
INSERT INTO `admin_role_permissions` (`id`, `role_id`, `permission_id`, `created_by`, `created_at`) VALUES (148,8,109,NULL,'2025-07-18 19:08:38.000');
INSERT INTO `admin_role_permissions` (`id`, `role_id`, `permission_id`, `created_by`, `created_at`) VALUES (149,8,110,NULL,'2025-07-18 19:08:38.000');
INSERT INTO `admin_role_permissions` (`id`, `role_id`, `permission_id`, `created_by`, `created_at`) VALUES (150,8,111,NULL,'2025-07-18 19:08:38.000');
INSERT INTO `admin_role_permissions` (`id`, `role_id`, `permission_id`, `created_by`, `created_at`) VALUES (151,8,112,NULL,'2025-07-18 19:08:38.000');
INSERT INTO `admin_role_permissions` (`id`, `role_id`, `permission_id`, `created_by`, `created_at`) VALUES (152,8,113,NULL,'2025-07-18 19:08:38.000');
INSERT INTO `admin_role_permissions` (`id`, `role_id`, `permission_id`, `created_by`, `created_at`) VALUES (153,8,114,NULL,'2025-07-18 19:08:38.000');
INSERT INTO `admin_role_permissions` (`id`, `role_id`, `permission_id`, `created_by`, `created_at`) VALUES (154,8,115,NULL,'2025-07-18 19:08:38.000');
INSERT INTO `admin_role_permissions` (`id`, `role_id`, `permission_id`, `created_by`, `created_at`) VALUES (155,8,116,NULL,'2025-07-18 19:08:38.000');
INSERT INTO `admin_role_permissions` (`id`, `role_id`, `permission_id`, `created_by`, `created_at`) VALUES (156,8,117,NULL,'2025-07-18 19:08:38.000');
INSERT INTO `admin_role_permissions` (`id`, `role_id`, `permission_id`, `created_by`, `created_at`) VALUES (217,10,1,NULL,'2025-07-18 19:08:46.000');
INSERT INTO `admin_role_permissions` (`id`, `role_id`, `permission_id`, `created_by`, `created_at`) VALUES (218,10,3,NULL,'2025-07-18 19:08:46.000');
INSERT INTO `admin_role_permissions` (`id`, `role_id`, `permission_id`, `created_by`, `created_at`) VALUES (219,10,9,NULL,'2025-07-18 19:08:46.000');
INSERT INTO `admin_role_permissions` (`id`, `role_id`, `permission_id`, `created_by`, `created_at`) VALUES (220,10,10,NULL,'2025-07-18 19:08:46.000');
INSERT INTO `admin_role_permissions` (`id`, `role_id`, `permission_id`, `created_by`, `created_at`) VALUES (221,10,11,NULL,'2025-07-18 19:08:46.000');
INSERT INTO `admin_role_permissions` (`id`, `role_id`, `permission_id`, `created_by`, `created_at`) VALUES (222,10,12,NULL,'2025-07-18 19:08:46.000');
INSERT INTO `admin_role_permissions` (`id`, `role_id`, `permission_id`, `created_by`, `created_at`) VALUES (223,10,13,NULL,'2025-07-18 19:08:46.000');
INSERT INTO `admin_role_permissions` (`id`, `role_id`, `permission_id`, `created_by`, `created_at`) VALUES (224,10,14,NULL,'2025-07-18 19:08:46.000');
INSERT INTO `admin_role_permissions` (`id`, `role_id`, `permission_id`, `created_by`, `created_at`) VALUES (225,10,15,NULL,'2025-07-18 19:08:46.000');
INSERT INTO `admin_role_permissions` (`id`, `role_id`, `permission_id`, `created_by`, `created_at`) VALUES (226,10,16,NULL,'2025-07-18 19:08:46.000');
INSERT INTO `admin_role_permissions` (`id`, `role_id`, `permission_id`, `created_by`, `created_at`) VALUES (227,10,84,NULL,'2025-07-18 19:08:46.000');
INSERT INTO `admin_role_permissions` (`id`, `role_id`, `permission_id`, `created_by`, `created_at`) VALUES (228,10,85,NULL,'2025-07-18 19:08:46.000');
INSERT INTO `admin_role_permissions` (`id`, `role_id`, `permission_id`, `created_by`, `created_at`) VALUES (229,10,86,NULL,'2025-07-18 19:08:46.000');
INSERT INTO `admin_role_permissions` (`id`, `role_id`, `permission_id`, `created_by`, `created_at`) VALUES (230,10,87,NULL,'2025-07-18 19:08:46.000');
INSERT INTO `admin_role_permissions` (`id`, `role_id`, `permission_id`, `created_by`, `created_at`) VALUES (231,10,88,NULL,'2025-07-18 19:08:46.000');
INSERT INTO `admin_role_permissions` (`id`, `role_id`, `permission_id`, `created_by`, `created_at`) VALUES (232,10,89,NULL,'2025-07-18 19:08:46.000');
INSERT INTO `admin_role_permissions` (`id`, `role_id`, `permission_id`, `created_by`, `created_at`) VALUES (233,10,90,NULL,'2025-07-18 19:08:46.000');
INSERT INTO `admin_role_permissions` (`id`, `role_id`, `permission_id`, `created_by`, `created_at`) VALUES (234,10,91,NULL,'2025-07-18 19:08:46.000');
INSERT INTO `admin_role_permissions` (`id`, `role_id`, `permission_id`, `created_by`, `created_at`) VALUES (235,10,92,NULL,'2025-07-18 19:08:46.000');
INSERT INTO `admin_role_permissions` (`id`, `role_id`, `permission_id`, `created_by`, `created_at`) VALUES (236,10,93,NULL,'2025-07-18 19:08:46.000');
INSERT INTO `admin_role_permissions` (`id`, `role_id`, `permission_id`, `created_by`, `created_at`) VALUES (237,10,94,NULL,'2025-07-18 19:08:46.000');
INSERT INTO `admin_role_permissions` (`id`, `role_id`, `permission_id`, `created_by`, `created_at`) VALUES (238,10,95,NULL,'2025-07-18 19:08:46.000');
INSERT INTO `admin_role_permissions` (`id`, `role_id`, `permission_id`, `created_by`, `created_at`) VALUES (239,10,96,NULL,'2025-07-18 19:08:46.000');
INSERT INTO `admin_role_permissions` (`id`, `role_id`, `permission_id`, `created_by`, `created_at`) VALUES (240,10,97,NULL,'2025-07-18 19:08:46.000');
INSERT INTO `admin_role_permissions` (`id`, `role_id`, `permission_id`, `created_by`, `created_at`) VALUES (241,10,98,NULL,'2025-07-18 19:08:46.000');
INSERT INTO `admin_role_permissions` (`id`, `role_id`, `permission_id`, `created_by`, `created_at`) VALUES (242,10,99,NULL,'2025-07-18 19:08:46.000');
INSERT INTO `admin_role_permissions` (`id`, `role_id`, `permission_id`, `created_by`, `created_at`) VALUES (243,10,100,NULL,'2025-07-18 19:08:46.000');
INSERT INTO `admin_role_permissions` (`id`, `role_id`, `permission_id`, `created_by`, `created_at`) VALUES (244,10,101,NULL,'2025-07-18 19:08:46.000');
INSERT INTO `admin_role_permissions` (`id`, `role_id`, `permission_id`, `created_by`, `created_at`) VALUES (245,10,102,NULL,'2025-07-18 19:08:46.000');
INSERT INTO `admin_role_permissions` (`id`, `role_id`, `permission_id`, `created_by`, `created_at`) VALUES (246,10,103,NULL,'2025-07-18 19:08:46.000');
INSERT INTO `admin_role_permissions` (`id`, `role_id`, `permission_id`, `created_by`, `created_at`) VALUES (247,10,104,NULL,'2025-07-18 19:08:46.000');
INSERT INTO `admin_role_permissions` (`id`, `role_id`, `permission_id`, `created_by`, `created_at`) VALUES (248,10,105,NULL,'2025-07-18 19:08:46.000');
INSERT INTO `admin_role_permissions` (`id`, `role_id`, `permission_id`, `created_by`, `created_at`) VALUES (249,10,106,NULL,'2025-07-18 19:08:46.000');
INSERT INTO `admin_role_permissions` (`id`, `role_id`, `permission_id`, `created_by`, `created_at`) VALUES (250,10,107,NULL,'2025-07-18 19:08:46.000');
INSERT INTO `admin_role_permissions` (`id`, `role_id`, `permission_id`, `created_by`, `created_at`) VALUES (251,10,108,NULL,'2025-07-18 19:08:46.000');
INSERT INTO `admin_role_permissions` (`id`, `role_id`, `permission_id`, `created_by`, `created_at`) VALUES (252,10,109,NULL,'2025-07-18 19:08:46.000');
INSERT INTO `admin_role_permissions` (`id`, `role_id`, `permission_id`, `created_by`, `created_at`) VALUES (253,10,110,NULL,'2025-07-18 19:08:46.000');
INSERT INTO `admin_role_permissions` (`id`, `role_id`, `permission_id`, `created_by`, `created_at`) VALUES (254,10,111,NULL,'2025-07-18 19:08:46.000');
INSERT INTO `admin_role_permissions` (`id`, `role_id`, `permission_id`, `created_by`, `created_at`) VALUES (255,10,112,NULL,'2025-07-18 19:08:46.000');
INSERT INTO `admin_role_permissions` (`id`, `role_id`, `permission_id`, `created_by`, `created_at`) VALUES (256,10,113,NULL,'2025-07-18 19:08:46.000');
INSERT INTO `admin_role_permissions` (`id`, `role_id`, `permission_id`, `created_by`, `created_at`) VALUES (257,10,114,NULL,'2025-07-18 19:08:46.000');
INSERT INTO `admin_role_permissions` (`id`, `role_id`, `permission_id`, `created_by`, `created_at`) VALUES (258,10,115,NULL,'2025-07-18 19:08:46.000');
/*!40000 ALTER TABLE `admin_role_permissions` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Dumping data for table `admin_roles`
--

LOCK TABLES `admin_roles` WRITE;
/*!40000 ALTER TABLE `admin_roles` DISABLE KEYS */;
INSERT INTO `admin_roles` (`id`, `created_at`, `updated_at`, `name`, `code`, `description`, `status`, `sort`, `remark`, `created_by`, `updated_by`) VALUES (8,'2025-06-28 23:41:52.246','2025-06-28 23:41:52.246','超级管理员','super_admin','系统超级管理员，拥有所有权限','active',1,NULL,NULL,NULL);
INSERT INTO `admin_roles` (`id`, `created_at`, `updated_at`, `name`, `code`, `description`, `status`, `sort`, `remark`, `created_by`, `updated_by`) VALUES (9,'2025-06-28 23:41:52.249','2025-06-28 23:41:52.249','管理员','admin','普通管理员','active',2,NULL,NULL,NULL);
INSERT INTO `admin_roles` (`id`, `created_at`, `updated_at`, `name`, `code`, `description`, `status`, `sort`, `remark`, `created_by`, `updated_by`) VALUES (10,'2025-06-28 23:41:52.251','2025-06-28 23:41:52.251','编辑员','editor','内容编辑员','active',3,NULL,NULL,NULL);
INSERT INTO `admin_roles` (`id`, `created_at`, `updated_at`, `name`, `code`, `description`, `status`, `sort`, `remark`, `created_by`, `updated_by`) VALUES (15,'2025-07-19 03:54:40.399','2025-07-19 03:55:10.391','测试角色(已修改)','test_role','这是一个测试角色','active',0,NULL,NULL,NULL);
/*!40000 ALTER TABLE `admin_roles` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Dumping data for table `admin_user_roles`
--

LOCK TABLES `admin_user_roles` WRITE;
/*!40000 ALTER TABLE `admin_user_roles` DISABLE KEYS */;
-- admin 用户角色关联数据已移除，将在管理员用户创建时自动分配
/*!40000 ALTER TABLE `admin_user_roles` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Dumping data for table `admin_users`
--

LOCK TABLES `admin_users` WRITE;
/*!40000 ALTER TABLE `admin_users` DISABLE KEYS */;
-- admin 用户数据已移除，将通过服务启动时的密码初始化功能创建
/*!40000 ALTER TABLE `admin_users` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Dumping data for table `cache_versions`
--

LOCK TABLES `cache_versions` WRITE;
/*!40000 ALTER TABLE `cache_versions` DISABLE KEYS */;
/*!40000 ALTER TABLE `cache_versions` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Dumping data for table `file_uploads`
--

LOCK TABLES `file_uploads` WRITE;
/*!40000 ALTER TABLE `file_uploads` DISABLE KEYS */;
INSERT INTO `file_uploads` (`id`, `original_name`, `file_name`, `file_path`, `file_size`, `mime_type`, `bucket_name`, `cos_url`, `uploaded_by`, `module`, `status`, `created_at`, `updated_at`, `deleted_at`) VALUES (1,'图片1.png','swiper/1751729236_图片1_22328172.png','swiper/1751729236_图片1_22328172.png',268796,'image/png','upload-1305444037','https://upload-1305444037.cos.ap-nanjing.myqcloud.com/swiper/1751729236_图片1_22328172.png',3,'swiper','active','2025-07-05 23:27:17','2025-07-05 23:27:17',NULL);
INSERT INTO `file_uploads` (`id`, `original_name`, `file_name`, `file_path`, `file_size`, `mime_type`, `bucket_name`, `cos_url`, `uploaded_by`, `module`, `status`, `created_at`, `updated_at`, `deleted_at`) VALUES (2,'图片1.png','swiper/1751729252_图片1_52541977.png','swiper/1751729252_图片1_52541977.png',268796,'image/png','upload-1305444037','https://upload-1305444037.cos.ap-nanjing.myqcloud.com/swiper/1751729252_图片1_52541977.png',3,'swiper','active','2025-07-05 23:27:33','2025-07-05 23:27:33',NULL);
INSERT INTO `file_uploads` (`id`, `original_name`, `file_name`, `file_path`, `file_size`, `mime_type`, `bucket_name`, `cos_url`, `uploaded_by`, `module`, `status`, `created_at`, `updated_at`, `deleted_at`) VALUES (3,'图片1.png','swiper/1751729332_图片1_43599330.png','swiper/1751729332_图片1_43599330.png',268796,'image/png','upload-1305444037','https://upload-1305444037.cos.ap-nanjing.myqcloud.com/swiper/1751729332_图片1_43599330.png',3,'swiper','active','2025-07-05 23:28:53','2025-07-05 23:28:53',NULL);
INSERT INTO `file_uploads` (`id`, `original_name`, `file_name`, `file_path`, `file_size`, `mime_type`, `bucket_name`, `cos_url`, `uploaded_by`, `module`, `status`, `created_at`, `updated_at`, `deleted_at`) VALUES (4,'图片1.png','swiper/1751729514_图片1_90081643.png','swiper/1751729514_图片1_90081643.png',268796,'image/png','upload-1305444037','https://upload-1305444037.cos.ap-nanjing.myqcloud.com/swiper/1751729514_图片1_90081643.png',3,'swiper','active','2025-07-05 23:31:55','2025-07-05 23:31:55',NULL);
INSERT INTO `file_uploads` (`id`, `original_name`, `file_name`, `file_path`, `file_size`, `mime_type`, `bucket_name`, `cos_url`, `uploaded_by`, `module`, `status`, `created_at`, `updated_at`, `deleted_at`) VALUES (5,'图片1.png','swiper/1751729842_图片1_13897950.png','swiper/1751729842_图片1_13897950.png',268796,'image/png','upload-1305444037','https://upload-1305444037.cos.ap-nanjing.myqcloud.com/swiper/1751729842_图片1_13897950.png',3,'swiper','active','2025-07-05 23:37:23','2025-07-05 23:37:23',NULL);
INSERT INTO `file_uploads` (`id`, `original_name`, `file_name`, `file_path`, `file_size`, `mime_type`, `bucket_name`, `cos_url`, `uploaded_by`, `module`, `status`, `created_at`, `updated_at`, `deleted_at`) VALUES (6,'图片1.png','swiper/1751730091_图片1_20037977.png','swiper/1751730091_图片1_20037977.png',268796,'image/png','upload-1305444037','https://upload-1305444037.cos.ap-nanjing.myqcloud.com/swiper/1751730091_图片1_20037977.png',3,'swiper','active','2025-07-05 23:41:32','2025-07-05 23:41:32',NULL);
INSERT INTO `file_uploads` (`id`, `original_name`, `file_name`, `file_path`, `file_size`, `mime_type`, `bucket_name`, `cos_url`, `uploaded_by`, `module`, `status`, `created_at`, `updated_at`, `deleted_at`) VALUES (7,'图片1.png','swiper/1751730098_图片1_71923604.png','swiper/1751730098_图片1_71923604.png',268796,'image/png','upload-1305444037','https://upload-1305444037.cos.ap-nanjing.myqcloud.com/swiper/1751730098_图片1_71923604.png',3,'swiper','active','2025-07-05 23:41:38','2025-07-05 23:41:38',NULL);
INSERT INTO `file_uploads` (`id`, `original_name`, `file_name`, `file_path`, `file_size`, `mime_type`, `bucket_name`, `cos_url`, `uploaded_by`, `module`, `status`, `created_at`, `updated_at`, `deleted_at`) VALUES (8,'图片1.png','swiper/1751730150_图片1_52474454.png','swiper/1751730150_图片1_52474454.png',268796,'image/png','upload-1305444037','https://upload-1305444037.cos.ap-nanjing.myqcloud.com/swiper/1751730150_图片1_52474454.png',3,'swiper','active','2025-07-05 23:42:30','2025-07-05 23:42:30',NULL);
INSERT INTO `file_uploads` (`id`, `original_name`, `file_name`, `file_path`, `file_size`, `mime_type`, `bucket_name`, `cos_url`, `uploaded_by`, `module`, `status`, `created_at`, `updated_at`, `deleted_at`) VALUES (9,'WechatIMG503.jpg','news/1751730840_WechatIMG503_12071609.jpg','news/1751730840_WechatIMG503_12071609.jpg',1328914,'image/jpeg','upload-1305444037','https://upload-1305444037.cos.ap-nanjing.myqcloud.com/news/1751730840_WechatIMG503_12071609.jpg',3,'news','active','2025-07-05 23:54:01','2025-07-05 23:54:01',NULL);
INSERT INTO `file_uploads` (`id`, `original_name`, `file_name`, `file_path`, `file_size`, `mime_type`, `bucket_name`, `cos_url`, `uploaded_by`, `module`, `status`, `created_at`, `updated_at`, `deleted_at`) VALUES (11,'尼威动力.png','partner/1752916573_尼威动力_23417857.png','partner/1752916573_尼威动力_23417857.png',10717,'image/png','upload-1305444037','https://upload-1305444037.cos.ap-nanjing.myqcloud.com/partner/1752916573_尼威动力_23417857.png',3,'partner','active','2025-07-19 17:16:14','2025-07-19 17:16:14',NULL);
INSERT INTO `file_uploads` (`id`, `original_name`, `file_name`, `file_path`, `file_size`, `mime_type`, `bucket_name`, `cos_url`, `uploaded_by`, `module`, `status`, `created_at`, `updated_at`, `deleted_at`) VALUES (12,'HydroTech.png','partner/1752917509_HydroTech_99087545.png','partner/1752917509_HydroTech_99087545.png',40272,'image/png','upload-1305444037','https://upload-1305444037.cos.ap-nanjing.myqcloud.com/partner/1752917509_HydroTech_99087545.png',3,'partner','active','2025-07-19 17:31:50','2025-07-19 17:31:50',NULL);
INSERT INTO `file_uploads` (`id`, `original_name`, `file_name`, `file_path`, `file_size`, `mime_type`, `bucket_name`, `cos_url`, `uploaded_by`, `module`, `status`, `created_at`, `updated_at`, `deleted_at`) VALUES (13,'ARGOHYTOS.png','partner/1752917519_ARGOHYTOS_13864275.png','partner/1752917519_ARGOHYTOS_13864275.png',35122,'image/png','upload-1305444037','https://upload-1305444037.cos.ap-nanjing.myqcloud.com/partner/1752917519_ARGOHYTOS_13864275.png',3,'partner','active','2025-07-19 17:32:00','2025-07-19 17:32:00',NULL);
INSERT INTO `file_uploads` (`id`, `original_name`, `file_name`, `file_path`, `file_size`, `mime_type`, `bucket_name`, `cos_url`, `uploaded_by`, `module`, `status`, `created_at`, `updated_at`, `deleted_at`) VALUES (14,'TECHGOAL.png','partner/1752917527_TECHGOAL_19945045.png','partner/1752917527_TECHGOAL_19945045.png',9420,'image/png','upload-1305444037','https://upload-1305444037.cos.ap-nanjing.myqcloud.com/partner/1752917527_TECHGOAL_19945045.png',3,'partner','active','2025-07-19 17:32:08','2025-07-19 17:32:08',NULL);
INSERT INTO `file_uploads` (`id`, `original_name`, `file_name`, `file_path`, `file_size`, `mime_type`, `bucket_name`, `cos_url`, `uploaded_by`, `module`, `status`, `created_at`, `updated_at`, `deleted_at`) VALUES (15,'扬州长运.png','partner/1752917539_扬州长运_94669250.png','partner/1752917539_扬州长运_94669250.png',12154,'image/png','upload-1305444037','https://upload-1305444037.cos.ap-nanjing.myqcloud.com/partner/1752917539_扬州长运_94669250.png',3,'partner','active','2025-07-19 17:32:20','2025-07-19 17:32:20',NULL);
INSERT INTO `file_uploads` (`id`, `original_name`, `file_name`, `file_path`, `file_size`, `mime_type`, `bucket_name`, `cos_url`, `uploaded_by`, `module`, `status`, `created_at`, `updated_at`, `deleted_at`) VALUES (16,'ParkOhio.png','partner/1752917548_ParkOhio_99426720.png','partner/1752917548_ParkOhio_99426720.png',277182,'image/png','upload-1305444037','https://upload-1305444037.cos.ap-nanjing.myqcloud.com/partner/1752917548_ParkOhio_99426720.png',3,'partner','active','2025-07-19 17:32:29','2025-07-19 17:32:29',NULL);
INSERT INTO `file_uploads` (`id`, `original_name`, `file_name`, `file_path`, `file_size`, `mime_type`, `bucket_name`, `cos_url`, `uploaded_by`, `module`, `status`, `created_at`, `updated_at`, `deleted_at`) VALUES (17,'FD.png','partner/1752917573_FD_56142273.png','partner/1752917573_FD_56142273.png',20601,'image/png','upload-1305444037','https://upload-1305444037.cos.ap-nanjing.myqcloud.com/partner/1752917573_FD_56142273.png',3,'partner','active','2025-07-19 17:32:53','2025-07-19 17:32:53',NULL);
INSERT INTO `file_uploads` (`id`, `original_name`, `file_name`, `file_path`, `file_size`, `mime_type`, `bucket_name`, `cos_url`, `uploaded_by`, `module`, `status`, `created_at`, `updated_at`, `deleted_at`) VALUES (18,'SHYECHAN.png','partner/1752917584_SHYECHAN_92704341.png','partner/1752917584_SHYECHAN_92704341.png',44195,'image/png','upload-1305444037','https://upload-1305444037.cos.ap-nanjing.myqcloud.com/partner/1752917584_SHYECHAN_92704341.png',3,'partner','active','2025-07-19 17:33:05','2025-07-19 17:33:05',NULL);
INSERT INTO `file_uploads` (`id`, `original_name`, `file_name`, `file_path`, `file_size`, `mime_type`, `bucket_name`, `cos_url`, `uploaded_by`, `module`, `status`, `created_at`, `updated_at`, `deleted_at`) VALUES (19,'工业阀组过滤器.png','platform/1752919887_工业阀组过滤器_81101260.png','platform/1752919887_工业阀组过滤器_81101260.png',784643,'image/png','upload-1305444037','https://upload-1305444037.cos.ap-nanjing.myqcloud.com/platform/1752919887_工业阀组过滤器_81101260.png',3,'platform','active','2025-07-19 18:11:28','2025-07-19 18:11:28',NULL);
INSERT INTO `file_uploads` (`id`, `original_name`, `file_name`, `file_path`, `file_size`, `mime_type`, `bucket_name`, `cos_url`, `uploaded_by`, `module`, `status`, `created_at`, `updated_at`, `deleted_at`) VALUES (20,'燃油箱总成.png','platform/1752919928_燃油箱总成_99957873.png','platform/1752919928_燃油箱总成_99957873.png',94654,'image/png','upload-1305444037','https://upload-1305444037.cos.ap-nanjing.myqcloud.com/platform/1752919928_燃油箱总成_99957873.png',3,'platform','active','2025-07-19 18:12:08','2025-07-19 18:12:08',NULL);
/*!40000 ALTER TABLE `file_uploads` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Dumping data for table `friend_links`
--

LOCK TABLES `friend_links` WRITE;
/*!40000 ALTER TABLE `friend_links` DISABLE KEYS */;
INSERT INTO `friend_links` (`id`, `created_at`, `updated_at`, `name`, `url`, `order`, `deleted_at`) VALUES (2,'2025-06-15 15:38:39.000','2025-06-15 15:38:39.000','中华人民共和国生态环境部','https://www.mee.gov.cn/',2,NULL);
INSERT INTO `friend_links` (`id`, `created_at`, `updated_at`, `name`, `url`, `order`, `deleted_at`) VALUES (3,'2025-06-15 15:38:39.000','2025-06-15 15:38:39.000','中国汽车技术研究中心有限公司','https://www.catarc.ac.cn/',3,NULL);
INSERT INTO `friend_links` (`id`, `created_at`, `updated_at`, `name`, `url`, `order`, `deleted_at`) VALUES (4,'2025-06-15 15:38:39.000','2025-06-15 15:38:39.000','SMVIC','https://www.smvic.com.cn/pages/index.html',4,NULL);
INSERT INTO `friend_links` (`id`, `created_at`, `updated_at`, `name`, `url`, `order`, `deleted_at`) VALUES (5,'2025-06-15 15:38:39.000','2025-06-15 15:38:39.000','California Air Resources Board','https://ww2.arb.ca.gov/homepage',5,NULL);
INSERT INTO `friend_links` (`id`, `created_at`, `updated_at`, `name`, `url`, `order`, `deleted_at`) VALUES (6,'2025-06-15 15:38:39.000','2025-06-15 15:38:39.000','瀚海检测','http://www.hhtesting.cn/',6,NULL);
INSERT INTO `friend_links` (`id`, `created_at`, `updated_at`, `name`, `url`, `order`, `deleted_at`) VALUES (7,'2025-06-15 15:38:39.000','2025-06-15 15:38:39.000','江苏东方汽车装饰件有限公司','http://www.dongfang-js.com/',7,NULL);
INSERT INTO `friend_links` (`id`, `created_at`, `updated_at`, `name`, `url`, `order`, `deleted_at`) VALUES (8,'2025-06-15 15:38:39.000','2025-06-15 15:38:39.000','泸州北方化学工业有限公司','http://lzbfhg.norincogroup.com.cn/',8,NULL);
INSERT INTO `friend_links` (`id`, `created_at`, `updated_at`, `name`, `url`, `order`, `deleted_at`) VALUES (9,'2025-06-15 15:38:39.000','2025-06-15 15:38:39.000','扬州长运塑料技术股份有限公司','http://www.cyjsgf.com/',9,NULL);
INSERT INTO `friend_links` (`id`, `created_at`, `updated_at`, `name`, `url`, `order`, `deleted_at`) VALUES (10,'2025-06-15 15:38:39.000','2025-06-15 15:38:39.000','扬州良诚汽车部件有限公司','http://www.yzlc.com.cn/',10,NULL);
INSERT INTO `friend_links` (`id`, `created_at`, `updated_at`, `name`, `url`, `order`, `deleted_at`) VALUES (11,'2025-06-30 01:35:06.377','2025-06-30 01:35:06.377','测试友情链接','https://www.example.com',1,NULL);
INSERT INTO `friend_links` (`id`, `created_at`, `updated_at`, `name`, `url`, `order`, `deleted_at`) VALUES (12,'2025-06-30 01:35:55.028','2025-06-30 01:35:55.028','前端测试链接','https://frontend.test.com',0,NULL);
/*!40000 ALTER TABLE `friend_links` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Dumping data for table `news`
--

LOCK TABLES `news` WRITE;
/*!40000 ALTER TABLE `news` DISABLE KEYS */;
INSERT INTO `news` (`id`, `created_at`, `updated_at`, `title`, `content`, `image`, `is_home_page`, `deleted_at`, `summary`, `cover_image`, `author`, `publish_time`, `is_published`, `view_count`) VALUES (1,'2025-06-23 23:50:39.944','2025-07-05 23:51:02.592','公司成立','蔚之领域智能科技有限公司正式成立，致力于为客户提供专业的智能科技解决方案。','https://picsum.photos/800/400?random=201',1,NULL,'公司成立',NULL,NULL,NULL,1,0);
INSERT INTO `news` (`id`, `created_at`, `updated_at`, `title`, `content`, `image`, `is_home_page`, `deleted_at`, `summary`, `cover_image`, `author`, `publish_time`, `is_published`, `view_count`) VALUES (2,'2025-06-23 23:50:39.946','2025-07-05 23:51:03.595','技术团队扩建','为了更好地服务客户，公司技术团队进一步扩建，引入更多优秀的技术人才。','https://picsum.photos/800/400?random=202',1,NULL,'技术团队扩建',NULL,NULL,NULL,1,0);
INSERT INTO `news` (`id`, `created_at`, `updated_at`, `title`, `content`, `image`, `is_home_page`, `deleted_at`, `summary`, `cover_image`, `author`, `publish_time`, `is_published`, `view_count`) VALUES (3,'2025-06-30 00:55:41.315','2025-07-05 23:51:04.388','测试新闻API完整功能','这是一条测试新闻，用于验证新闻管理功能的完整性。包括分页获取、创建、更新、删除以及首页显示设置等功能。通过前后端API的完整实现，实现了新闻内容的全生命周期管理。','https://picsum.photos/800/400?random=888',1,NULL,NULL,NULL,NULL,NULL,0,0);
INSERT INTO `news` (`id`, `created_at`, `updated_at`, `title`, `content`, `image`, `is_home_page`, `deleted_at`, `summary`, `cover_image`, `author`, `publish_time`, `is_published`, `view_count`) VALUES (4,'2025-01-15 10:30:00.000','2025-01-15 10:30:00.000','蔚之领域获得ISO9001质量管理体系认证','经过严格的审核和评估，江苏蔚之领域智能科技有限公司正式获得ISO9001:2015质量管理体系认证。这标志着公司在质量管理方面达到了国际先进水平，为客户提供更加可靠的产品和服务奠定了坚实基础。公司将继续秉承\"质量第一、客户至上\"的理念，不断提升产品质量和服务水平。','https://picsum.photos/800/400?random=101',1,NULL,'ISO9001质量管理体系认证',NULL,NULL,NULL,1,0);
INSERT INTO `news` (`id`, `created_at`, `updated_at`, `title`, `content`, `image`, `is_home_page`, `deleted_at`, `summary`, `cover_image`, `author`, `publish_time`, `is_published`, `view_count`) VALUES (5,'2025-01-10 14:20:00.000','2025-01-10 14:20:00.000','新一代EDS测试台架正式投入使用','公司自主研发的新一代EDS（电驱动系统）测试台架经过数月的调试和优化，正式投入使用。该测试台架采用先进的控制系统和高精度传感器，能够对电机性能进行全面、精确的测试和分析。新设备的投入使用将大幅提升公司的测试能力和服务质量，为新能源汽车行业的发展贡献力量。','https://picsum.photos/800/400?random=102',1,NULL,'新一代EDS测试台架投入使用',NULL,NULL,NULL,1,0);
INSERT INTO `news` (`id`, `created_at`, `updated_at`, `title`, `content`, `image`, `is_home_page`, `deleted_at`, `summary`, `cover_image`, `author`, `publish_time`, `is_published`, `view_count`) VALUES (6,'2025-01-05 09:15:00.000','2025-01-05 09:15:00.000','与知名汽车制造商达成战略合作','蔚之领域与国内知名汽车制造商正式签署战略合作协议，双方将在汽车动力系统测试、产品开发等领域展开深度合作。此次合作将充分发挥双方的技术优势和资源优势，共同推动汽车行业的技术创新和产业升级。合作项目预计将在未来三年内为公司带来显著的业务增长。','https://picsum.photos/800/400?random=103',1,NULL,'与知名汽车制造商达成战略合作',NULL,NULL,NULL,1,0);
INSERT INTO `news` (`id`, `created_at`, `updated_at`, `title`, `content`, `image`, `is_home_page`, `deleted_at`, `summary`, `cover_image`, `author`, `publish_time`, `is_published`, `view_count`) VALUES (7,'2024-12-28 16:45:00.000','2024-12-28 16:45:00.000','公司荣获\"高新技术企业\"称号','经过严格的评审程序，江苏蔚之领域智能科技有限公司被认定为\"高新技术企业\"。这一荣誉的获得，充分体现了公司在技术创新、研发投入和知识产权等方面的突出表现。公司将以此为契机，继续加大研发投入，提升技术创新能力，为行业发展贡献更多的智慧和力量。','https://picsum.photos/800/400?random=104',1,NULL,'荣获高新技术企业称号',NULL,NULL,NULL,1,0);
/*!40000 ALTER TABLE `news` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Dumping data for table `our_services`
--

LOCK TABLES `our_services` WRITE;
/*!40000 ALTER TABLE `our_services` DISABLE KEYS */;
INSERT INTO `our_services` (`id`, `created_at`, `updated_at`, `name`, `type`, `description`, `fullDescription`, `image`, `features`, `equipments`, `testItems`, `order`) VALUES (1,'2025-06-15 15:38:40.000','2025-06-15 15:38:40.000','设计开发','design_develop','提供专业的设计开发服务','我们提供全面的设计开发服务，包括概念设计、详细设计、样机制作等','https://images-1305444037.cos.ap-nanjing.myqcloud.com/our-service/cover/1.jpg','[\"专业团队\",\"先进设备\",\"快速响应\"]',NULL,NULL,1);
INSERT INTO `our_services` (`id`, `created_at`, `updated_at`, `name`, `type`, `description`, `fullDescription`, `image`, `features`, `equipments`, `testItems`, `order`) VALUES (2,'2025-06-15 15:38:40.000','2025-06-15 15:38:40.000','零件平台','ed','提供全面的零件平台服务','我们的零件平台提供各类汽车零部件，满足不同客户的需求','https://images-1305444037.cos.ap-nanjing.myqcloud.com/part-platform/1_bak.jpg','[\"品类齐全\",\"质量保证\",\"及时供应\"]',NULL,NULL,2);
INSERT INTO `our_services` (`id`, `created_at`, `updated_at`, `name`, `type`, `description`, `fullDescription`, `image`, `features`, `equipments`, `testItems`, `order`) VALUES (3,'2025-06-15 15:38:40.000','2025-06-15 15:38:40.000','试验验证','ev','提供专业的试验验证服务','我们提供全面的试验验证服务，确保产品质量和性能','https://images-1305444037.cos.ap-nanjing.myqcloud.com/our-service/cover/2.png','[\"专业设备\",\"标准流程\",\"详细报告\"]','[\"测试设备1\",\"测试设备2\"]','[\"测试项目1\",\"测试项目2\"]',3);
INSERT INTO `our_services` (`id`, `created_at`, `updated_at`, `name`, `type`, `description`, `fullDescription`, `image`, `features`, `equipments`, `testItems`, `order`) VALUES (4,'2025-06-15 15:38:40.000','2025-06-15 15:38:40.000','设备开发','design_develop','提供专业的设备开发服务','我们提供专业的设备开发服务，满足客户的特殊需求','https://images-1305444037.cos.ap-nanjing.myqcloud.com/our-service/cover/3.png','[\"定制开发\",\"技术支持\",\"售后服务\"]',NULL,NULL,4);
INSERT INTO `our_services` (`id`, `created_at`, `updated_at`, `name`, `type`, `description`, `fullDescription`, `image`, `features`, `equipments`, `testItems`, `order`) VALUES (5,'2025-06-15 15:38:40.000','2025-06-15 15:38:40.000','人力资源','hr','提供专业的人力资源服务','我们提供专业的人力资源服务，为企业发展提供人才支持','https://images-1305444037.cos.ap-nanjing.myqcloud.com/our-service/cover/4.png','[\"人才招聘\",\"培训发展\",\"绩效管理\"]',NULL,NULL,5);
/*!40000 ALTER TABLE `our_services` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Dumping data for table `part_platform`
--

LOCK TABLES `part_platform` WRITE;
/*!40000 ALTER TABLE `part_platform` DISABLE KEYS */;
INSERT INTO `part_platform` (`id`, `created_at`, `updated_at`, `name`, `url`, `description`, `parameters`, `applications`, `order`, `deleted_at`) VALUES (7,'2025-06-15 15:38:39.000','2025-06-15 15:38:39.000','用于燃油系统的各类阀门GVV/FLVV/FTIV等','https://images-1305444037.cos.ap-nanjing.myqcloud.com/part-platform/1.png','用于燃油系统的各类阀门GVV/FLVV/FTIV等','技术参数详情','应用于各类燃油系统',1,NULL);
INSERT INTO `part_platform` (`id`, `created_at`, `updated_at`, `name`, `url`, `description`, `parameters`, `applications`, `order`, `deleted_at`) VALUES (8,'2025-06-15 15:38:39.000','2025-06-15 15:38:39.000','用于燃油系统的各类炭罐总成','https://images-1305444037.cos.ap-nanjing.myqcloud.com/part-platform/2.png','用于燃油系统的各类炭罐总成','技术参数详情','应用于各类燃油系统',2,NULL);
INSERT INTO `part_platform` (`id`, `created_at`, `updated_at`, `name`, `url`, `description`, `parameters`, `applications`, `order`, `deleted_at`) VALUES (9,'2025-06-15 15:38:39.000','2025-06-15 15:38:39.000','用于燃油系统的各类油泵总成','https://images-1305444037.cos.ap-nanjing.myqcloud.com/part-platform/3.png','用于燃油系统的各类油泵总成','技术参数详情','应用于各类燃油系统',3,NULL);
INSERT INTO `part_platform` (`id`, `created_at`, `updated_at`, `name`, `url`, `description`, `parameters`, `applications`, `order`, `deleted_at`) VALUES (10,'2025-06-15 15:38:39.000','2025-06-15 15:38:39.000','各类管路，油管、气管、胶管等','https://images-1305444037.cos.ap-nanjing.myqcloud.com/part-platform/4.png','各类管路，油管、气管、胶管等','技术参数详情','应用于各类燃油系统',4,NULL);
INSERT INTO `part_platform` (`id`, `created_at`, `updated_at`, `name`, `url`, `description`, `parameters`, `applications`, `order`, `deleted_at`) VALUES (11,'2025-06-15 15:38:39.000','2025-06-15 15:38:39.000','用于燃油系统的各类气液分离器LVS','https://images-1305444037.cos.ap-nanjing.myqcloud.com/part-platform/5.png','用于燃油系统的各类气液分离器LVS','技术参数详情','应用于各类燃油系统',5,NULL);
INSERT INTO `part_platform` (`id`, `created_at`, `updated_at`, `name`, `url`, `description`, `parameters`, `applications`, `order`, `deleted_at`) VALUES (12,'2025-06-15 15:38:39.000','2025-06-15 15:38:39.000','各类快插接头QC、连接接头等','https://images-1305444037.cos.ap-nanjing.myqcloud.com/part-platform/6.png','各类快插接头QC、连接接头等','技术参数详情','应用于各类燃油系统',6,NULL);
INSERT INTO `part_platform` (`id`, `created_at`, `updated_at`, `name`, `url`, `description`, `parameters`, `applications`, `order`, `deleted_at`) VALUES (13,'2025-06-15 15:38:39.000','2025-06-15 15:38:39.000','用于燃油系统的各类锁盖','https://images-1305444037.cos.ap-nanjing.myqcloud.com/part-platform/7.png','用于燃油系统的各类锁盖','技术参数详情','应用于各类燃油系统',7,NULL);
INSERT INTO `part_platform` (`id`, `created_at`, `updated_at`, `name`, `url`, `description`, `parameters`, `applications`, `order`, `deleted_at`) VALUES (14,'2025-06-15 15:38:39.000','2025-06-15 15:38:39.000','用于燃油系统的各类加油管总成等','https://images-1305444037.cos.ap-nanjing.myqcloud.com/part-platform/8.png','用于燃油系统的各类加油管总成等','技术参数详情','应用于各类燃油系统',8,NULL);
INSERT INTO `part_platform` (`id`, `created_at`, `updated_at`, `name`, `url`, `description`, `parameters`, `applications`, `order`, `deleted_at`) VALUES (18,'2025-07-19 18:11:38.308','2025-07-19 18:12:48.919','工业阀组过滤器','https://upload-1305444037.cos.ap-nanjing.myqcloud.com/platform/1752919887_工业阀组过滤器_81101260.png','工业阀组过滤器','无','无',1,NULL);
INSERT INTO `part_platform` (`id`, `created_at`, `updated_at`, `name`, `url`, `description`, `parameters`, `applications`, `order`, `deleted_at`) VALUES (19,'2025-07-19 18:12:30.363','2025-07-19 18:12:44.144','燃油箱总成','https://upload-1305444037.cos.ap-nanjing.myqcloud.com/platform/1752919928_燃油箱总成_99957873.png','无','无','无',0,NULL);
/*!40000 ALTER TABLE `part_platform` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Dumping data for table `partners`
--

LOCK TABLES `partners` WRITE;
/*!40000 ALTER TABLE `partners` DISABLE KEYS */;
INSERT INTO `partners` (`id`, `created_at`, `updated_at`, `name`, `logo`, `deleted_at`) VALUES (3,'2025-06-15 15:38:39.000','2025-06-15 15:38:39.000','3','https://images-1305444037.cos.ap-nanjing.myqcloud.com/partners/3.png',NULL);
INSERT INTO `partners` (`id`, `created_at`, `updated_at`, `name`, `logo`, `deleted_at`) VALUES (4,'2025-06-15 15:38:39.000','2025-06-15 15:38:39.000','4','https://images-1305444037.cos.ap-nanjing.myqcloud.com/partners/4.png',NULL);
INSERT INTO `partners` (`id`, `created_at`, `updated_at`, `name`, `logo`, `deleted_at`) VALUES (8,'2025-06-15 15:38:39.000','2025-06-15 15:38:39.000','8','https://images-1305444037.cos.ap-nanjing.myqcloud.com/partners/8.png',NULL);
INSERT INTO `partners` (`id`, `created_at`, `updated_at`, `name`, `logo`, `deleted_at`) VALUES (14,'2025-07-19 17:16:25.752','2025-07-19 17:16:25.752','尼威动力','https://upload-1305444037.cos.ap-nanjing.myqcloud.com/partner/1752916573_尼威动力_23417857.png',NULL);
INSERT INTO `partners` (`id`, `created_at`, `updated_at`, `name`, `logo`, `deleted_at`) VALUES (15,'2025-07-19 17:16:25.752','2025-07-19 17:16:25.752','1','https://upload-1305444037.cos.ap-nanjing.myqcloud.com/partner/1752917509_HydroTech_99087545.png',NULL);
INSERT INTO `partners` (`id`, `created_at`, `updated_at`, `name`, `logo`, `deleted_at`) VALUES (16,'2025-07-19 17:16:25.752','2025-07-19 17:16:25.752','2','https://upload-1305444037.cos.ap-nanjing.myqcloud.com/partner/1752917519_ARGOHYTOS_13864275.png',NULL);
INSERT INTO `partners` (`id`, `created_at`, `updated_at`, `name`, `logo`, `deleted_at`) VALUES (17,'2025-07-19 17:16:25.752','2025-07-19 17:16:25.752','3','https://upload-1305444037.cos.ap-nanjing.myqcloud.com/partner/1752917527_TECHGOAL_19945045.png',NULL);
INSERT INTO `partners` (`id`, `created_at`, `updated_at`, `name`, `logo`, `deleted_at`) VALUES (18,'2025-07-19 17:16:25.752','2025-07-19 17:16:25.752','5','https://upload-1305444037.cos.ap-nanjing.myqcloud.com/partner/1752917539_扬州长运_94669250.png',NULL);
INSERT INTO `partners` (`id`, `created_at`, `updated_at`, `name`, `logo`, `deleted_at`) VALUES (19,'2025-07-19 17:16:25.752','2025-07-19 17:16:25.752','6','https://upload-1305444037.cos.ap-nanjing.myqcloud.com/partner/1752917548_ParkOhio_99426720.png',NULL);
INSERT INTO `partners` (`id`, `created_at`, `updated_at`, `name`, `logo`, `deleted_at`) VALUES (20,'2025-07-19 17:16:25.752','2025-07-19 17:16:25.752','7','https://upload-1305444037.cos.ap-nanjing.myqcloud.com/partner/1752917573_FD_56142273.png',NULL);
INSERT INTO `partners` (`id`, `created_at`, `updated_at`, `name`, `logo`, `deleted_at`) VALUES (21,'2025-07-19 17:16:25.752','2025-07-19 17:16:25.752','9','https://upload-1305444037.cos.ap-nanjing.myqcloud.com/partner/1752917584_SHYECHAN_92704341.png',NULL);
/*!40000 ALTER TABLE `partners` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Dumping data for table `project_cases`
--

LOCK TABLES `project_cases` WRITE;
/*!40000 ALTER TABLE `project_cases` DISABLE KEYS */;
INSERT INTO `project_cases` (`id`, `created_at`, `updated_at`, `url`, `title`, `description`, `sort`, `deleted_at`, `cover_image`, `tags`, `order`) VALUES (1,'2025-06-15 15:38:40.000','2025-06-15 15:38:40.000','https://images-1305444037.cos.ap-nanjing.myqcloud.com/our-service/design-and-development/swiper/1.png','1','1',1,NULL,NULL,NULL,0);
INSERT INTO `project_cases` (`id`, `created_at`, `updated_at`, `url`, `title`, `description`, `sort`, `deleted_at`, `cover_image`, `tags`, `order`) VALUES (2,'2025-06-15 15:38:40.000','2025-06-15 15:38:40.000','https://images-1305444037.cos.ap-nanjing.myqcloud.com/our-service/design-and-development/swiper/2.png','2','2',2,NULL,NULL,NULL,0);
INSERT INTO `project_cases` (`id`, `created_at`, `updated_at`, `url`, `title`, `description`, `sort`, `deleted_at`, `cover_image`, `tags`, `order`) VALUES (3,'2025-06-15 15:38:40.000','2025-06-15 15:38:40.000','https://images-1305444037.cos.ap-nanjing.myqcloud.com/our-service/design-and-development/swiper/3.png','3','3',3,NULL,NULL,NULL,0);
INSERT INTO `project_cases` (`id`, `created_at`, `updated_at`, `url`, `title`, `description`, `sort`, `deleted_at`, `cover_image`, `tags`, `order`) VALUES (4,'2025-06-15 15:38:40.000','2025-06-15 15:38:40.000','https://images-1305444037.cos.ap-nanjing.myqcloud.com/our-service/design-and-development/swiper/4.png','4','4',4,NULL,NULL,NULL,0);
INSERT INTO `project_cases` (`id`, `created_at`, `updated_at`, `url`, `title`, `description`, `sort`, `deleted_at`, `cover_image`, `tags`, `order`) VALUES (5,'2025-06-15 15:38:40.000','2025-06-15 15:38:40.000','https://images-1305444037.cos.ap-nanjing.myqcloud.com/our-service/design-and-development/swiper/5.png','5','5',5,NULL,NULL,NULL,0);
/*!40000 ALTER TABLE `project_cases` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Dumping data for table `recruitments`
--

LOCK TABLES `recruitments` WRITE;
/*!40000 ALTER TABLE `recruitments` DISABLE KEYS */;
INSERT INTO `recruitments` (`id`, `created_at`, `updated_at`, `name`, `location`, `content`, `order`, `deleted_at`, `position`, `department`, `description`, `requirement`, `is_active`) VALUES (1,'2025-06-15 15:38:40.000','2025-06-15 15:38:40.000','资深汽车电子工程师','南京','岗位职责：\n1. 负责新能源汽车电子控制系统的设计与开发\n2. 负责产品性能测试与优化\n3. 参与新技术研究与应用\n\n任职要求：\n1. 电子、自动化或相关专业硕士及以上学历\n2. 5年以上汽车电子领域工作经验\n3. 熟悉汽车电子系统开发流程和标准\n4. 具有良好的团队协作和沟通能力',1,NULL,'','','',NULL,1);
INSERT INTO `recruitments` (`id`, `created_at`, `updated_at`, `name`, `location`, `content`, `order`, `deleted_at`, `position`, `department`, `description`, `requirement`, `is_active`) VALUES (2,'2025-06-15 15:38:40.000','2025-06-15 15:38:40.000','结构设计工程师','上海','岗位职责：\n1. 负责汽车零部件结构设计\n2. 进行产品结构分析和改进\n3. 协助生产部门解决生产过程中的技术问题\n\n任职要求：\n1. 机械设计或相关专业本科及以上学历\n2. 3年以上汽车零部件结构设计经验\n3. 熟练使用CAD、CATIA等设计软件\n4. 具有创新思维和解决问题的能力',2,NULL,'','','',NULL,1);
INSERT INTO `recruitments` (`id`, `created_at`, `updated_at`, `name`, `location`, `content`, `order`, `deleted_at`, `position`, `department`, `description`, `requirement`, `is_active`) VALUES (3,'2025-06-15 15:38:40.000','2025-06-15 15:38:40.000','质量工程师','苏州','岗位职责：\n1. 负责产品质量控制与质量保证\n2. 制定并执行质量管理流程\n3. 分析解决生产过程中的质量问题\n\n任职要求：\n1. 质量管理或相关专业本科及以上学历\n2. 具有汽车行业质量管理经验\n3. 熟悉TS16949、ISO9001等质量管理体系\n4. 良好的数据分析能力和问题解决能力',3,NULL,'','','',NULL,1);
INSERT INTO `recruitments` (`id`, `created_at`, `updated_at`, `name`, `location`, `content`, `order`, `deleted_at`, `position`, `department`, `description`, `requirement`, `is_active`) VALUES (6,'2025-07-19 10:30:43.000','2025-07-19 10:30:43.000','高级机械设计工程师','扬州','负责汽车动力系统机械部件的设计开发，包括燃油系统、冷却系统等关键部件的结构设计与优化。',4,NULL,'高级工程师','研发部','参与新产品开发项目，负责机械设计方案制定、3D建模、工程图纸绘制等工作。','1. 机械设计或相关专业本科及以上学历\n2. 5年以上汽车零部件设计经验\n3. 熟练使用UG、CATIA、AutoCAD等设计软件\n4. 具备良好的沟通协调能力',1);
INSERT INTO `recruitments` (`id`, `created_at`, `updated_at`, `name`, `location`, `content`, `order`, `deleted_at`, `position`, `department`, `description`, `requirement`, `is_active`) VALUES (7,'2025-07-19 10:30:43.000','2025-07-19 10:30:43.000','CAE仿真工程师','扬州','负责汽车动力系统的仿真分析工作，包括结构分析、流体分析、热分析等，为产品设计提供技术支持。',5,NULL,'工程师','技术部','运用CAE软件进行产品性能仿真分析，优化产品设计方案，提升产品性能。','1. 力学、机械或相关专业硕士及以上学历\n2. 3年以上CAE仿真分析经验\n3. 熟练使用ANSYS、ABAQUS、FLUENT等仿真软件\n4. 具备扎实的理论基础和实践经验',1);
INSERT INTO `recruitments` (`id`, `created_at`, `updated_at`, `name`, `location`, `content`, `order`, `deleted_at`, `position`, `department`, `description`, `requirement`, `is_active`) VALUES (8,'2025-07-19 10:30:43.000','2025-07-19 10:30:43.000','电气工程师','扬州','负责测试设备的电气系统设计、调试和维护，确保设备正常运行和测试精度。',6,NULL,'工程师','工程部','设计测试台架的电气控制系统，编写PLC程序，调试设备电气系统。','1. 电气工程或自动化专业本科及以上学历\n2. 3年以上电气设计经验\n3. 熟练使用PLC编程、电气设计软件\n4. 具备良好的动手能力和问题解决能力',1);
INSERT INTO `recruitments` (`id`, `created_at`, `updated_at`, `name`, `location`, `content`, `order`, `deleted_at`, `position`, `department`, `description`, `requirement`, `is_active`) VALUES (9,'2025-07-19 10:30:43.000','2025-07-19 10:30:43.000','项目经理','扬州','负责项目的整体规划、执行和管理，协调各部门资源，确保项目按时按质完成。',7,NULL,'项目经理','项目部','制定项目计划，跟踪项目进度，管理项目风险，协调内外部资源。','1. 工程管理或相关专业本科及以上学历\n2. 5年以上项目管理经验\n3. 具备PMP证书优先\n4. 优秀的沟通协调和团队管理能力',1);
/*!40000 ALTER TABLE `recruitments` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Dumping data for table `services`
--

LOCK TABLES `services` WRITE;
/*!40000 ALTER TABLE `services` DISABLE KEYS */;
INSERT INTO `services` (`id`, `created_at`, `updated_at`, `deleted_at`, `title`, `description`, `icon`, `order`) VALUES (1,'2025-06-23 23:50:39.938','2025-06-23 23:50:39.938',NULL,'软件开发','提供专业的软件开发服务，包括Web应用、移动应用等',NULL,1);
INSERT INTO `services` (`id`, `created_at`, `updated_at`, `deleted_at`, `title`, `description`, `icon`, `order`) VALUES (2,'2025-06-23 23:50:39.940','2025-06-23 23:50:39.940',NULL,'系统集成','为企业提供完整的系统集成解决方案',NULL,2);
INSERT INTO `services` (`id`, `created_at`, `updated_at`, `deleted_at`, `title`, `description`, `icon`, `order`) VALUES (3,'2025-06-23 23:50:39.941','2025-06-23 23:50:39.941',NULL,'技术咨询','提供专业的技术咨询和架构设计服务',NULL,3);
INSERT INTO `services` (`id`, `created_at`, `updated_at`, `deleted_at`, `title`, `description`, `icon`, `order`) VALUES (4,'2025-06-23 23:50:39.943','2025-06-23 23:50:39.943',NULL,'运维服务','7x24小时专业运维服务，保障系统稳定运行',NULL,4);
/*!40000 ALTER TABLE `services` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Dumping data for table `swipers`
--

LOCK TABLES `swipers` WRITE;
/*!40000 ALTER TABLE `swipers` DISABLE KEYS */;
INSERT INTO `swipers` (`id`, `created_at`, `updated_at`, `url`, `title`, `order`, `deleted_at`, `status`) VALUES (1,'2025-06-15 15:38:40.000','2025-06-15 15:38:40.000','https://images-1305444037.cos.ap-nanjing.myqcloud.com/swiper/1.jpg','轮播图1',1,NULL,'active');
INSERT INTO `swipers` (`id`, `created_at`, `updated_at`, `url`, `title`, `order`, `deleted_at`, `status`) VALUES (2,'2025-06-15 15:38:40.000','2025-06-15 15:38:40.000','https://images-1305444037.cos.ap-nanjing.myqcloud.com/swiper/2.jpg','轮播图2',2,NULL,'active');
INSERT INTO `swipers` (`id`, `created_at`, `updated_at`, `url`, `title`, `order`, `deleted_at`, `status`) VALUES (3,'2025-06-15 15:38:40.000','2025-06-15 15:38:40.000','https://images-1305444037.cos.ap-nanjing.myqcloud.com/swiper/3.jpg','轮播图3',3,NULL,'active');
INSERT INTO `swipers` (`id`, `created_at`, `updated_at`, `url`, `title`, `order`, `deleted_at`, `status`) VALUES (4,'2025-06-15 15:38:40.000','2025-06-15 15:38:40.000','https://images-1305444037.cos.ap-nanjing.myqcloud.com/swiper/4.jpg','轮播图4',4,NULL,'active');
/*!40000 ALTER TABLE `swipers` ENABLE KEYS */;
UNLOCK TABLES;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*M!100616 SET NOTE_VERBOSITY=@OLD_NOTE_VERBOSITY */;

-- Dump completed on 2025-07-20  3:23:03

-- 恢复外键检查
SET FOREIGN_KEY_CHECKS = 1;

-- 显示完成信息
SELECT '数据库初始化完成！' as message;
SELECT '管理员账号: admin' as admin_account;
SELECT '管理员密码: admin123' as admin_password;

-- =====================================================
-- 管理员账号配置更新
-- =====================================================

-- admin 用户创建和角色分配已移除
-- 管理员用户将通过服务启动时的密码初始化功能自动创建

-- 恢复外键检查
SET FOREIGN_KEY_CHECKS = 1;

-- 显示完成信息
SELECT '数据库初始化完成！' as message;
SELECT CONCAT('管理员账号: ', 'admin') as admin_account;
SELECT '请首次登录后立即修改密码' as security_notice;
