#!/bin/bash

# 等待服务启动完成后执行数据导入
# 确保 Go 应用已经完全启动并创建了所有数据库表

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[DATA-IMPORT]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[DATA-IMPORT]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[DATA-IMPORT]${NC} $1"
}

log_error() {
    echo -e "${RED}[DATA-IMPORT]${NC} $1"
}

# 环境变量配置
SERVER_HOST=${SERVER_HOST:-server}
SERVER_PORT=${SERVER_PORT:-3001}
SERVER_HEALTH_ENDPOINT=${SERVER_HEALTH_ENDPOINT:-/api/health}

DB_HOST=${DB_HOST:-mysql}
DB_PORT=${DB_PORT:-3306}
DB_USERNAME=${DB_USERNAME:-root}
DB_PASSWORD=${DB_PASSWORD:-${MYSQL_ROOT_PASSWORD}}
DB_DATABASE=${DB_DATABASE:-${MYSQL_DATABASE:-weizhi}}

# 数据导入选项
IMPORT_FULL_DATA=${IMPORT_FULL_DATA:-false}

# 等待配置
MAX_WAIT_TIME=${MAX_WAIT_TIME:-300}  # 最大等待时间（秒）
CHECK_INTERVAL=${CHECK_INTERVAL:-10}  # 检查间隔（秒）

# 检查是否需要执行数据导入
should_import_data() {
    if [ "$IMPORT_FULL_DATA" = "true" ]; then
        return 0
    fi
    return 1
}

# 等待数据库连接
wait_for_database() {
    log_info "等待数据库连接 $DB_HOST:$DB_PORT..."
    
    local elapsed=0
    while [ $elapsed -lt $MAX_WAIT_TIME ]; do
        if mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USERNAME" -p"$DB_PASSWORD" -e "SELECT 1;" >/dev/null 2>&1; then
            log_success "数据库连接成功"
            return 0
        fi
        
        log_info "等待数据库连接... (${elapsed}s/${MAX_WAIT_TIME}s)"
        sleep $CHECK_INTERVAL
        elapsed=$((elapsed + CHECK_INTERVAL))
    done
    
    log_error "数据库连接超时"
    return 1
}

# 等待 Go 服务启动
wait_for_go_service() {
    log_info "等待 Go 服务启动 $SERVER_HOST:$SERVER_PORT..."
    
    local elapsed=0
    while [ $elapsed -lt $MAX_WAIT_TIME ]; do
        if curl -f "http://$SERVER_HOST:$SERVER_PORT$SERVER_HEALTH_ENDPOINT" >/dev/null 2>&1; then
            log_success "Go 服务启动成功"
            return 0
        fi
        
        log_info "等待 Go 服务启动... (${elapsed}s/${MAX_WAIT_TIME}s)"
        sleep $CHECK_INTERVAL
        elapsed=$((elapsed + CHECK_INTERVAL))
    done
    
    log_error "Go 服务启动超时"
    return 1
}

# 检查数据库表是否已创建
wait_for_database_tables() {
    log_info "等待数据库表创建完成..."
    
    local required_tables=("admin_users" "our_services" "swipers")
    local elapsed=0
    
    while [ $elapsed -lt $MAX_WAIT_TIME ]; do
        local all_tables_exist=true
        
        for table in "${required_tables[@]}"; do
            if ! mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USERNAME" -p"$DB_PASSWORD" -e "USE $DB_DATABASE; DESCRIBE $table;" >/dev/null 2>&1; then
                all_tables_exist=false
                break
            fi
        done
        
        if [ "$all_tables_exist" = "true" ]; then
            log_success "数据库表创建完成"
            return 0
        fi
        
        log_info "等待数据库表创建... (${elapsed}s/${MAX_WAIT_TIME}s)"
        sleep $CHECK_INTERVAL
        elapsed=$((elapsed + CHECK_INTERVAL))
    done
    
    log_error "数据库表创建超时"
    return 1
}

# 检查是否已有数据
has_existing_data() {
    local tables=("admin_users" "our_services" "swipers")
    
    for table in "${tables[@]}"; do
        local count=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USERNAME" -p"$DB_PASSWORD" -e "USE $DB_DATABASE; SELECT COUNT(*) FROM $table;" -s -N 2>/dev/null || echo "0")
        if [ "$count" -gt 0 ]; then
            log_info "表 $table 已有 $count 条数据"
            return 0
        fi
    done
    
    return 1
}

# 执行数据导入
execute_data_import() {
    log_info "开始执行数据导入..."
    
    # 设置环境变量
    export RUN_MODE=container
    export SCRIPT_DIR="/app/scripts"
    
    # 执行数据导入脚本
    if [ -f "./import-data.sh" ]; then
        if bash ./import-data.sh; then
            log_success "数据导入完成"
            return 0
        else
            log_error "数据导入失败"
            return 1
        fi
    else
        log_error "数据导入脚本不存在: ./import-data.sh"
        return 1
    fi
}

# 显示最终状态
show_final_status() {
    log_info "=== 最终状态 ==="
    
    # 显示服务状态
    if curl -f "http://$SERVER_HOST:$SERVER_PORT$SERVER_HEALTH_ENDPOINT" >/dev/null 2>&1; then
        log_success "✅ Go 服务运行正常"
    else
        log_warning "⚠️ Go 服务状态异常"
    fi
    
    # 显示数据库统计
    log_info "📊 数据库统计:"
    local tables=("admin_users" "our_services" "swipers" "news" "products" "cases")
    
    for table in "${tables[@]}"; do
        local count=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USERNAME" -p"$DB_PASSWORD" -e "USE $DB_DATABASE; SELECT COUNT(*) FROM $table;" -s -N 2>/dev/null || echo "0")
        log_info "  $table: $count 条"
    done
}

# 主函数
main() {
    log_info "=== 数据导入服务启动 ==="
    
    # 显示配置
    log_info "配置信息:"
    log_info "  Go 服务: $SERVER_HOST:$SERVER_PORT"
    log_info "  数据库: $DB_HOST:$DB_PORT/$DB_DATABASE"
    log_info "  数据导入选项:"
    log_info "    种子数据: $IMPORT_SEED_DATA"
    log_info "    管理员数据: $IMPORT_ADMIN_DATA"
    log_info "    完整数据: $IMPORT_FULL_DATA"
    log_info "  等待配置:"
    log_info "    最大等待时间: ${MAX_WAIT_TIME}s"
    log_info "    检查间隔: ${CHECK_INTERVAL}s"
    
    # 检查是否需要导入数据
    if ! should_import_data; then
        log_info "未启用任何数据导入选项，服务将退出"
        exit 0
    fi
    
    # 等待依赖服务
    if ! wait_for_database; then
        log_error "数据库连接失败，退出"
        exit 1
    fi
    
    if ! wait_for_go_service; then
        log_error "Go 服务启动失败，退出"
        exit 1
    fi
    
    # 等待数据库表创建（如果导入完整数据，跳过此检查）
    if [ "$IMPORT_FULL_DATA" != "true" ]; then
        if ! wait_for_database_tables; then
            log_error "数据库表创建失败，退出"
            exit 1
        fi
    else
        log_info "导入完整数据模式，跳过表创建检查"
    fi
    
    # 检查现有数据
    if has_existing_data; then
        log_warning "检测到现有数据，跳过导入以避免重复"
        show_final_status
        exit 0
    fi
    
    # 执行数据导入
    if execute_data_import; then
        log_success "🎉 数据导入服务完成！"
    else
        log_error "❌ 数据导入失败"
        exit 1
    fi
    
    # 显示最终状态
    show_final_status
    
    log_info "数据导入服务结束"
}

# 处理信号
trap 'log_info "收到终止信号，正在退出..."; exit 0' SIGTERM SIGINT

# 执行主函数
main "$@"
