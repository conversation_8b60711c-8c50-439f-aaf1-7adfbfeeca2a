<template>
  <div class="p-6">
    <!-- 页面标题 -->
    <div class="flex justify-between items-center mb-6">
      <h2 class="text-2xl font-bold">服务管理</h2>
      <el-button type="primary" @click="handleAdd">
        <i class="i-mdi-plus mr-2"></i>
        新建服务
      </el-button>
    </div>
    
    <!-- 搜索区域 -->
    <el-card class="mb-6">
      <el-form :model="searchParams" inline>
        <el-form-item label="关键词">
          <el-input 
            v-model="searchParams.keyword" 
            placeholder="服务名称"
            clearable
            class="w-200px"
          />
        </el-form-item>
        <el-form-item label="服务类型">
          <el-select v-model="searchParams.type" placeholder="请选择" clearable>
            <el-option 
              v-for="type in serviceTypes" 
              :key="type" 
              :label="type" 
              :value="type" 
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <i class="i-mdi-magnify mr-2"></i>
            搜索
          </el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>
    
    <!-- 表格区域 -->
    <el-card>
      <el-table
        :data="tableData"
        v-loading="loading"
        @selection-change="handleSelectionChange"
        border
        stripe
      >
        <el-table-column type="selection" width="55" resizable />
        <el-table-column prop="id" label="ID" width="80" resizable />
        <el-table-column label="图片" width="120" resizable>
          <template #default="{ row }">
            <el-image
              :src="row.image"
              class="w-16 h-10 rounded"
              fit="cover"
              :preview-src-list="[row.image]"
              :z-index="3000"
              preview-teleported
            />
          </template>
        </el-table-column>
        <el-table-column prop="name" label="服务名称" min-width="150" resizable show-overflow-tooltip />
        <el-table-column prop="type" label="类型" width="120" resizable />
        <el-table-column label="描述" min-width="200" resizable show-overflow-tooltip>
          <template #default="{ row }">
            <div class="truncate">
              {{ row.description.substring(0, 50) }}{{ row.description.length > 50 ? '...' : '' }}
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="order" label="排序" width="100" resizable />
        <el-table-column prop="created_at" label="创建时间" width="180" resizable>
          <template #default="{ row }">
            {{ formatDate(row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right" resizable>
          <template #default="{ row }">
            <el-button size="small" @click="handleEdit(row)">
              编辑
            </el-button>
            <el-button 
              size="small" 
              type="danger" 
              @click="handleDelete(row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <div class="flex justify-between items-center mt-4">
        <div>
          <el-button 
            v-if="selectedItems.length > 0"
            type="danger"
            @click="handleBatchDelete"
          >
            批量删除 ({{ selectedItems.length }})
          </el-button>
        </div>
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @current-change="handlePageChange"
          @size-change="handleSizeChange"
        />
      </div>
    </el-card>
    
    <!-- 服务表单弹窗 -->
    <el-dialog 
      v-model="dialogVisible" 
      :title="dialogTitle"
      width="900px"
      @close="resetForm"
    >
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="120px"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="服务名称" prop="name">
              <el-input v-model="formData.name" placeholder="请输入服务名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="服务类型" prop="type">
              <el-input v-model="formData.type" placeholder="请输入服务类型" />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-form-item label="图片链接" prop="image">
          <div class="flex items-center w-full">
            <el-input 
              v-model="formData.image" 
              placeholder="请输入图片链接或上传图片"
              class="flex-auto mr-4"
              style="min-width: 0; flex: 1 1 auto;"
            />
            <el-upload
              ref="uploadRef"
              :action="uploadAction"
              :headers="uploadHeaders"
              :data="uploadData"
              :auto-upload="true"
              :show-file-list="false"
              accept="image/*"
              :limit="1"
              :before-upload="beforeUpload"
              :on-success="handleUploadSuccess"
              :on-error="handleUploadError"
              :on-progress="handleUploadProgress"
              :on-exceed="handleExceed"
              class="flex-shrink-0"
            >
              <el-button 
                type="primary" 
                :loading="uploadLoading"
                size="default"
              >
                {{ uploadLoading ? '上传中...' : '上传图片' }}
              </el-button>
            </el-upload>
          </div>
          
          <!-- 图片预览 -->
          <div v-if="formData.image" class="mt-4">
            <div class="relative inline-block">
              <el-image 
                :src="formData.image" 
                class="w-32 h-20 rounded border border-gray-200"
                fit="cover"
                :preview-src-list="[formData.image]"
                :z-index="3000"
                preview-teleported
              />
              <!-- 删除按钮 -->
              <el-button
                type="danger"
                size="small"
                circle
                class="absolute -top-2 -right-2 w-6 h-6 p-0 shadow-lg"
                @click="handleDeleteImage"
                :loading="deleteImageLoading"
              >
                <i class="i-mdi-close text-xs"></i>
              </el-button>
            </div>
            <!-- 图片信息 -->
            <div class="mt-2 text-xs text-gray-500">
              <p>点击预览图可以查看大图</p>
              <p>点击右上角 × 可以删除图片{{ currentImageFileId ? '' : '（已有图片仅清空链接）' }}</p>
            </div>
          </div>
          
          <!-- 上传提示 -->
          <div v-else class="mt-2 text-xs text-gray-500">
            <p>支持 JPG、PNG、GIF、WebP 格式，文件大小不超过 5MB</p>
            <p>建议尺寸：800×600px 或等比例缩放</p>
          </div>
        </el-form-item>
        
        <el-form-item label="简要描述" prop="description">
          <el-input 
            v-model="formData.description" 
            type="textarea" 
            :rows="3"
            placeholder="请输入简要描述"
          />
        </el-form-item>
        
        <el-form-item label="详细描述" prop="fullDescription">
          <el-input 
            v-model="formData.fullDescription" 
            type="textarea" 
            :rows="4"
            placeholder="请输入详细描述"
          />
        </el-form-item>
        
        <el-form-item label="特色功能" prop="features">
          <el-input 
            v-model="formData.features" 
            type="textarea" 
            :rows="3"
            placeholder="请输入特色功能描述"
          />
        </el-form-item>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="设备信息" prop="equipments">
              <el-input 
                v-model="formData.equipments" 
                type="textarea" 
                :rows="3"
                placeholder="请输入设备信息（可选）"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="测试项目" prop="testItems">
              <el-input 
                v-model="formData.testItems" 
                type="textarea" 
                :rows="3"
                placeholder="请输入测试项目（可选）"
              />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-form-item label="排序" prop="order">
          <el-input-number 
            v-model="formData.order" 
            :min="0" 
            :max="999"
            placeholder="数字越小排序越靠前"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button 
          type="primary" 
          :loading="submitLoading"
          @click="handleSubmit"
        >
          确定
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { serviceApi } from '@/api/content'
import type { CreateServiceData, Service, UpdateServiceData } from '@/types/api'
import { formatDate } from '@/utils/dateUtils'
import type { FormInstance, FormRules } from 'element-plus'
import { ElMessage, ElMessageBox } from 'element-plus'
import { computed, onMounted, reactive, ref, watch } from 'vue'

// 响应式数据
const formRef = ref<FormInstance>()
const uploadRef = ref()
const loading = ref(false)
const dialogVisible = ref(false)
const submitLoading = ref(false)
const uploadLoading = ref(false)
const deleteImageLoading = ref(false)
const selectedItems = ref<Service[]>([])
const tableData = ref<Service[]>([])
const total = ref(0)
const serviceTypes = ref<string[]>([])

// 保存当前图片的文件ID，用于删除
const currentImageFileId = ref<number | null>(null)

const searchParams = reactive({
  keyword: '',
  type: ''
})

const pagination = reactive({
  page: 1,
  size: 10
})

const formData = reactive<CreateServiceData & { id?: number }>({
  name: '',
  type: '',
  description: '',
  fullDescription: '',
  image: '',
  features: '',
  equipments: '',
  testItems: '',
  order: 0
})

// 监听URL变化，当清空时重置上传组件状态
watch(() => formData.image, (newUrl, oldUrl) => {
  // 当URL从有值变为空值时，清理上传组件状态
  if (oldUrl && !newUrl && uploadRef.value) {
    uploadRef.value.clearFiles()
  }
})

// 计算属性
const isEdit = computed(() => !!formData.id)
const dialogTitle = computed(() => isEdit.value ? '编辑服务' : '新建服务')

// 上传相关计算属性
const uploadAction = computed(() => {
  return `${import.meta.env.VITE_API_BASE_URL || 'http://localhost:3000/api'}/files/upload`
})

const uploadHeaders = computed(() => {
  const token = localStorage.getItem('admin_token')
  return token ? { Authorization: `Bearer ${token}` } : {}
})

const uploadData = computed(() => {
  return { module: 'service' }
})

// 表单验证规则
const formRules: FormRules = {
  name: [
    { required: true, message: '请输入服务名称', trigger: 'blur' }
  ],
  type: [
    { required: true, message: '请输入服务类型', trigger: 'blur' }
  ],
  description: [
    { required: true, message: '请输入简要描述', trigger: 'blur' }
  ],
  fullDescription: [
    { required: true, message: '请输入详细描述', trigger: 'blur' }
  ],
  image: [
    { required: true, message: '请输入图片链接', trigger: 'blur' },
    { type: 'url', message: '请输入正确的URL格式', trigger: 'blur' }
  ],
  features: [
    { required: true, message: '请输入特色功能', trigger: 'blur' }
  ],
  order: [
    { required: true, message: '请输入排序', trigger: 'blur' },
    { type: 'number', min: 0, message: '排序必须大于等于0', trigger: 'blur' }
  ]
}

// 方法
const fetchData = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.page,
      size: pagination.size,
      ...searchParams
    }
    const response = await serviceApi.getList(params)
    tableData.value = response.list
    total.value = response.total
  } catch (error) {
    ElMessage.error('获取数据失败')
    console.error('获取服务列表失败:', error)
  } finally {
    loading.value = false
  }
}

const fetchServiceTypes = async () => {
  try {
    serviceTypes.value = await serviceApi.getTypes()
  } catch (error) {
    console.error('获取服务类型失败:', error)
  }
}

const handleSearch = () => {
  pagination.page = 1
  fetchData()
}

const handleReset = () => {
  searchParams.keyword = ''
  searchParams.type = ''
  pagination.page = 1
  fetchData()
}

const handleAdd = () => {
  resetForm()
  dialogVisible.value = true
}

const handleEdit = (row: Service) => {
  Object.assign(formData, row)
  // 编辑时清空文件ID，因为现有图片没有上传ID
  currentImageFileId.value = null
  dialogVisible.value = true
}

const handleDelete = async (row: Service) => {
  try {
    await ElMessageBox.confirm(`确定要删除"${row.name}"吗？`, '确认删除', {
      type: 'warning'
    })
    
    await serviceApi.delete(row.id)
    ElMessage.success('删除成功')
    fetchData()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
      console.error('删除服务失败:', error)
    }
  }
}

const handleBatchDelete = async () => {
  if (selectedItems.value.length === 0) {
    ElMessage.warning('请选择要删除的项目')
    return
  }
  
  try {
    await ElMessageBox.confirm(`确定要删除选中的 ${selectedItems.value.length} 个服务吗？`, '确认删除', {
      type: 'warning'
    })
    
    for (const item of selectedItems.value) {
      await serviceApi.delete(item.id)
    }
    
    ElMessage.success('批量删除成功')
    selectedItems.value = []
    fetchData()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('批量删除失败')
      console.error('批量删除服务失败:', error)
    }
  }
}

const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    submitLoading.value = true
    
    if (isEdit.value) {
      const { id, ...updateData } = formData
      await serviceApi.update(id!, updateData as UpdateServiceData)
      ElMessage.success('更新成功')
    } else {
      const { id, ...createData } = formData
      await serviceApi.create(createData as CreateServiceData)
      ElMessage.success('创建成功')
    }
    
    dialogVisible.value = false
    fetchData()
    fetchServiceTypes() // 重新获取服务类型
  } catch (error) {
    ElMessage.error(isEdit.value ? '更新失败' : '创建失败')
    console.error('提交服务失败:', error)
  } finally {
    submitLoading.value = false
  }
}

const handleSelectionChange = (selection: Service[]) => {
  selectedItems.value = selection
}

const handlePageChange = (page: number) => {
  pagination.page = page
  fetchData()
}

const handleSizeChange = (size: number) => {
  pagination.size = size
  pagination.page = 1
  fetchData()
}

const resetForm = () => {
  Object.assign(formData, {
    id: undefined,
    name: '',
    type: '',
    description: '',
    fullDescription: '',
    image: '',
    features: '',
    equipments: '',
    testItems: '',
    order: 0
  })
  
  // 清空文件ID
  currentImageFileId.value = null
  
  // 清理上传组件状态
  if (uploadRef.value) {
    uploadRef.value.clearFiles()
  }
  
  formRef.value?.resetFields()
}

// Element Plus Upload 组件事件处理函数
const beforeUpload = (file: File) => {
  const isImage = file.type.startsWith('image/')
  const isLt5M = file.size / 1024 / 1024 < 5

  if (!isImage) {
    ElMessage.error('只能上传图片文件!')
    return false
  }
  if (!isLt5M) {
    ElMessage.error('上传图片大小不能超过 5MB!')
    return false
  }
  
  uploadLoading.value = true
  return true
}

// 上传成功回调
const handleUploadSuccess = (response: any, file: any, fileList: any) => {
  console.log('📤 上传响应数据:', response)
  
  // 根据实际的响应格式解析数据
  if (response && response.data && response.data.cosUrl) {
    formData.image = response.data.cosUrl
    // 保存文件ID用于删除
    currentImageFileId.value = response.data.id
    ElMessage.success('图片上传成功')
    
    // 触发表单验证，清除image字段的验证错误
    formRef.value?.validateField('image')
  } else if (response && response.cosUrl) {
    // 兼容其他可能的响应格式
    formData.image = response.cosUrl
    // 如果有ID字段，保存它
    if (response.id) {
      currentImageFileId.value = response.id
    }
    ElMessage.success('图片上传成功')
    formRef.value?.validateField('image')
  } else {
    console.error('❌ 响应格式错误:', response)
    ElMessage.error('上传成功但响应格式错误')
  }
  
  // 清理上传组件的文件列表，确保下次可以重新上传
  if (uploadRef.value) {
    uploadRef.value.clearFiles()
  }
  
  uploadLoading.value = false
}

// 删除图片
const handleDeleteImage = async () => {
  if (!formData.image) return
  
  try {
    await ElMessageBox.confirm('确定要删除这张图片吗？', '确认删除', {
      type: 'warning',
      confirmButtonText: '确定',
      cancelButtonText: '取消'
    })
    
    deleteImageLoading.value = true
    
    console.log('🗑️ 准备删除图片:', formData.image)
    console.log('🆔 图片文件ID:', currentImageFileId.value)
    
    // 如果有文件ID，调用删除API
    if (currentImageFileId.value) {
      try {
        const response = await fetch(`${import.meta.env.VITE_API_BASE_URL || 'http://localhost:3000/api'}/files/${currentImageFileId.value}`, {
          method: 'DELETE',
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('admin_token')}`
          }
        })
        
        if (response.ok) {
          console.log('✅ 图片删除成功')
          ElMessage.success('图片删除成功')
        } else {
          console.warn('⚠️ 删除COS文件失败，但继续清空界面')
          ElMessage.warning('删除远程文件失败，但已清空界面链接')
        }
      } catch (error) {
        console.warn('⚠️ 删除COS文件失败，但继续清空界面:', error)
        ElMessage.warning('删除远程文件失败，但已清空界面链接')
      }
    } else {
      console.log('⚠️ 没有文件ID，跳过删除COS文件')
      ElMessage.warning('没有文件ID，仅清空界面链接')
    }
    
    // 清空表单中的图片链接和文件ID
    formData.image = ''
    currentImageFileId.value = null
    
    // 清理上传组件状态
    if (uploadRef.value) {
      uploadRef.value.clearFiles()
    }
    
  } catch (error) {
    if (error !== 'cancel') {
      console.error('❌ 删除图片失败:', error)
      ElMessage.error('删除图片失败')
    }
  } finally {
    deleteImageLoading.value = false
  }
}

// 上传失败回调
const handleUploadError = (error: any, file: any, fileList: any) => {
  ElMessage.error(`上传失败: ${error.message || '未知错误'}`)
  
  // 清理上传组件的文件列表
  if (uploadRef.value) {
    uploadRef.value.clearFiles()
  }
  
  uploadLoading.value = false
}

// 上传进度回调
const handleUploadProgress = (event: any, file: any, fileList: any) => {
  // 可以在这里显示上传进度
}

// 文件数量超出限制回调
const handleExceed = (files: any, fileList: any) => {
  // 只有在真正超出限制时才显示警告
  if (files.length > 1) {
    ElMessage.warning(`当前限制选择 1 个文件，本次选择了 ${files.length} 个文件，已自动选择第一个文件`)
  }
}

// 生命周期
onMounted(() => {
  fetchData()
  fetchServiceTypes()
})
</script> 