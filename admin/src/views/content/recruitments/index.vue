<template>
  <div class="p-6">
    <!-- 页面标题 -->
    <div class="flex justify-between items-center mb-6">
      <h2 class="text-2xl font-bold">招聘信息管理</h2>
      <el-button type="primary" @click="handleAdd">
        <i class="i-mdi-plus mr-2"></i>
        新建招聘
      </el-button>
    </div>
    
    <!-- 搜索区域 -->
    <el-card class="mb-6">
      <el-form :model="searchParams" inline>
        <el-form-item label="关键词">
          <el-input 
            v-model="searchParams.keyword" 
            placeholder="职位名称、部门"
            clearable
            class="w-200px"
          />
        </el-form-item>
        <el-form-item label="工作地点">
          <el-input 
            v-model="searchParams.location" 
            placeholder="工作地点"
            clearable
            class="w-200px"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <i class="i-mdi-magnify mr-2"></i>
            搜索
          </el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>
    
    <!-- 表格区域 -->
    <el-card>
      <el-table
        :data="tableData"
        v-loading="loading"
        @selection-change="handleSelectionChange"
        border
        stripe
      >
        <el-table-column type="selection" width="55" resizable />
        <el-table-column prop="id" label="ID" width="80" resizable />
        <el-table-column prop="position" label="职位" min-width="120" resizable show-overflow-tooltip />
        <el-table-column prop="department" label="部门" width="100" resizable />
        <el-table-column prop="name" label="职位名称" min-width="150" resizable show-overflow-tooltip />
        <el-table-column prop="location" label="工作地点" width="120" resizable />
        <el-table-column label="职位描述" min-width="200" resizable show-overflow-tooltip>
          <template #default="{ row }">
            <div class="truncate">
              {{ row.description.substring(0, 50) }}{{ row.description.length > 50 ? '...' : '' }}
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="isActive" label="状态" width="80" resizable>
          <template #default="{ row }">
            <el-tag :type="row.isActive ? 'success' : 'danger'">
              {{ row.isActive ? '激活' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="order" label="排序" width="100" resizable />
        <el-table-column prop="created_at" label="创建时间" width="180" resizable>
          <template #default="{ row }">
            {{ formatDate(row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right" resizable>
          <template #default="{ row }">
            <el-button size="small" @click="handleEdit(row)">
              编辑
            </el-button>
            <el-button 
              size="small" 
              type="danger" 
              @click="handleDelete(row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <div class="flex justify-between items-center mt-4">
        <div>
          <el-button 
            v-if="selectedItems.length > 0"
            type="danger"
            @click="handleBatchDelete"
          >
            批量删除 ({{ selectedItems.length }})
          </el-button>
        </div>
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @current-change="handlePageChange"
          @size-change="handleSizeChange"
        />
      </div>
    </el-card>
    
    <!-- 表单弹窗 -->
    <el-dialog 
      v-model="dialogVisible" 
      :title="dialogTitle"
      width="800px"
      @close="resetForm"
    >
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="100px"
      >
        <el-form-item label="职位" prop="position">
          <el-input v-model="formData.position" placeholder="请输入职位" />
        </el-form-item>
        <el-form-item label="部门" prop="department">
          <el-input v-model="formData.department" placeholder="请输入部门" />
        </el-form-item>
        <el-form-item label="职位名称" prop="name">
          <el-input v-model="formData.name" placeholder="请输入职位名称" />
        </el-form-item>
        <el-form-item label="工作地点" prop="location">
          <el-input v-model="formData.location" placeholder="请输入工作地点" />
        </el-form-item>
        <el-form-item label="职位描述" prop="description">
          <el-input 
            v-model="formData.description" 
            type="textarea"
            :rows="4"
            placeholder="请输入职位描述"
          />
        </el-form-item>
        <el-form-item label="具体内容" prop="content">
          <el-input 
            v-model="formData.content" 
            type="textarea"
            :rows="4"
            placeholder="请输入具体内容"
          />
        </el-form-item>
        <el-form-item label="职位要求" prop="requirement">
          <el-input 
            v-model="formData.requirement" 
            type="textarea"
            :rows="4"
            placeholder="请输入职位要求（可选）"
          />
        </el-form-item>
        <el-form-item label="状态" prop="isActive">
          <el-switch 
            v-model="formData.isActive"
            active-text="激活"
            inactive-text="禁用"
          />
        </el-form-item>
        <el-form-item label="排序" prop="order">
          <el-input-number 
            v-model="formData.order" 
            :min="0" 
            :max="999"
            placeholder="数字越小排序越靠前"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button 
          type="primary" 
          :loading="submitLoading"
          @click="handleSubmit"
        >
          确定
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { recruitmentApi } from '@/api/content'
import type { CreateRecruitmentData, Recruitment, UpdateRecruitmentData } from '@/types/api'
import { formatDate } from '@/utils/dateUtils'
import type { FormInstance, FormRules } from 'element-plus'
import { ElMessage, ElMessageBox } from 'element-plus'
import { computed, onMounted, reactive, ref } from 'vue'

// 响应式数据
const formRef = ref<FormInstance>()
const loading = ref(false)
const dialogVisible = ref(false)
const submitLoading = ref(false)
const selectedItems = ref<Recruitment[]>([])
const tableData = ref<Recruitment[]>([])
const total = ref(0)

const searchParams = reactive({
  keyword: '',
  location: ''
})

const pagination = reactive({
  page: 1,
  size: 10
})

const formData = reactive<CreateRecruitmentData & { id?: number }>({
  name: '',
  location: '',
  content: '',
  position: '',
  department: '',
  description: '',
  requirement: '',
  order: 0,
  isActive: true
})

// 计算属性
const isEdit = computed(() => !!formData.id)
const dialogTitle = computed(() => isEdit.value ? '编辑招聘信息' : '新建招聘信息')

// 表单验证规则
const formRules: FormRules = {
  position: [
    { required: true, message: '请输入职位', trigger: 'blur' }
  ],
  department: [
    { required: true, message: '请输入部门', trigger: 'blur' }
  ],
  name: [
    { required: true, message: '请输入职位名称', trigger: 'blur' }
  ],
  location: [
    { required: true, message: '请输入工作地点', trigger: 'blur' }
  ],
  description: [
    { required: true, message: '请输入职位描述', trigger: 'blur' }
  ],
  content: [
    { required: true, message: '请输入具体内容', trigger: 'blur' }
  ],
  order: [
    { required: true, message: '请输入排序', trigger: 'blur' },
    { type: 'number', min: 0, message: '排序必须大于等于0', trigger: 'blur' }
  ]
}

// 方法
const fetchData = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.page,
      size: pagination.size,
      ...searchParams
    }
    const response = await recruitmentApi.getList(params)
    tableData.value = response.list
    total.value = response.total
  } catch (error) {
    ElMessage.error('获取数据失败')
    console.error('获取招聘信息列表失败:', error)
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  pagination.page = 1
  fetchData()
}

const handleReset = () => {
  searchParams.keyword = ''
  searchParams.location = ''
  pagination.page = 1
  fetchData()
}

const handleAdd = () => {
  resetForm()
  dialogVisible.value = true
}

const handleEdit = (row: Recruitment) => {
  Object.assign(formData, row)
  dialogVisible.value = true
}

const handleDelete = async (row: Recruitment) => {
  try {
    await ElMessageBox.confirm(`确定要删除"${row.name}"吗？`, '确认删除', {
      type: 'warning'
    })
    
    await recruitmentApi.delete(row.id)
    ElMessage.success('删除成功')
    fetchData()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
      console.error('删除招聘信息失败:', error)
    }
  }
}

const handleBatchDelete = async () => {
  if (selectedItems.value.length === 0) {
    ElMessage.warning('请选择要删除的项目')
    return
  }
  
  try {
    await ElMessageBox.confirm(`确定要删除选中的 ${selectedItems.value.length} 个招聘信息吗？`, '确认删除', {
      type: 'warning'
    })
    
    for (const item of selectedItems.value) {
      await recruitmentApi.delete(item.id)
    }
    
    ElMessage.success('批量删除成功')
    selectedItems.value = []
    fetchData()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('批量删除失败')
      console.error('批量删除招聘信息失败:', error)
    }
  }
}

const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    submitLoading.value = true
    
    if (isEdit.value) {
      const { id, ...updateData } = formData
      await recruitmentApi.update(id!, updateData as UpdateRecruitmentData)
      ElMessage.success('更新成功')
    } else {
      const { id, ...createData } = formData
      await recruitmentApi.create(createData as CreateRecruitmentData)
      ElMessage.success('创建成功')
    }
    
    dialogVisible.value = false
    fetchData()
  } catch (error) {
    ElMessage.error(isEdit.value ? '更新失败' : '创建失败')
    console.error('提交招聘信息失败:', error)
  } finally {
    submitLoading.value = false
  }
}

const handleSelectionChange = (selection: Recruitment[]) => {
  selectedItems.value = selection
}

const handlePageChange = (page: number) => {
  pagination.page = page
  fetchData()
}

const handleSizeChange = (size: number) => {
  pagination.size = size
  pagination.page = 1
  fetchData()
}

const resetForm = () => {
  Object.assign(formData, {
    id: undefined,
    name: '',
    location: '',
    content: '',
    position: '',
    department: '',
    description: '',
    requirement: '',
    order: 0,
    isActive: true
  })
  formRef.value?.resetFields()
}

// 生命周期
onMounted(() => {
  fetchData()
})
</script> 