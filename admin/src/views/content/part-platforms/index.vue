<template>
  <div class="p-6">
    <!-- 页面标题 -->
    <div class="flex justify-between items-center mb-6">
      <h2 class="text-2xl font-bold">平台管理</h2>
      <el-button type="primary" @click="handleAdd">
        <i class="i-mdi-plus mr-2"></i>
        新建平台
      </el-button>
    </div>
    
    <!-- 搜索区域 -->
    <el-card class="mb-6">
      <el-form :model="searchParams" inline>
        <el-form-item label="关键词">
          <el-input 
            v-model="searchParams.keyword" 
            placeholder="平台名称"
            clearable
            class="w-200px"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <i class="i-mdi-magnify mr-2"></i>
            搜索
          </el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>
    
    <!-- 表格区域 -->
    <el-card>
      <el-table
        :data="tableData"
        v-loading="loading"
        @selection-change="handleSelectionChange"
        border
        stripe
      >
        <el-table-column type="selection" width="55" resizable />
        <el-table-column prop="id" label="ID" width="80" resizable />
        <el-table-column prop="name" label="平台名称" min-width="150" resizable show-overflow-tooltip />
        <el-table-column label="平台图片链接" min-width="220" resizable>
          <template #default="{ row }">
            <div class="flex items-center gap-2">
              <el-image
                v-if="row.url"
                :src="row.url"
                class="w-12 h-8 rounded border border-gray-200 flex-shrink-0"
                fit="cover"
                :preview-src-list="[row.url]"
                :z-index="3000"
                preview-teleported
              />
              <span v-else class="w-12 h-8 bg-gray-100 rounded border border-gray-200 flex-shrink-0 flex items-center justify-center">
                <i class="i-mdi-image text-gray-400 text-sm"></i>
              </span>
              <span class="text-xs text-gray-500 truncate flex-1" :title="row.url">
                {{ row.url || '暂无链接' }}
              </span>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="描述" min-width="150" resizable show-overflow-tooltip>
          <template #default="{ row }">
            <div class="truncate">
              {{ row.description.substring(0, 30) }}{{ row.description.length > 30 ? '...' : '' }}
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="order" label="排序" width="100" resizable />
        <el-table-column prop="created_at" label="创建时间" width="180" resizable>
          <template #default="{ row }">
            {{ formatDate(row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right" resizable>
          <template #default="{ row }">
            <el-button size="small" @click="handleEdit(row)">
              编辑
            </el-button>
            <el-button 
              size="small" 
              type="danger" 
              @click="handleDelete(row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <div class="flex justify-between items-center mt-4">
        <div>
          <el-button 
            v-if="selectedItems.length > 0"
            type="danger"
            @click="handleBatchDelete"
          >
            批量删除 ({{ selectedItems.length }})
          </el-button>
        </div>
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @current-change="handlePageChange"
          @size-change="handleSizeChange"
        />
      </div>
    </el-card>
    
    <!-- 表单弹窗 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="900px"
      @close="resetForm"
      @open="onDialogOpen"
    >
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="100px"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="平台名称" prop="name">
              <el-input v-model="formData.name" placeholder="请输入平台名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="排序" prop="order">
              <el-input-number 
                v-model="formData.order" 
                :min="0" 
                :max="999"
                placeholder="排序"
              />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-form-item label="平台图片链接" prop="url">
          <div class="flex items-center w-full">
            <el-input 
              v-model="formData.url" 
              placeholder="请输入平台图片链接或上传图片"
              class="flex-auto mr-4"
              style="min-width: 0; flex: 1 1 auto;"
            />
            <el-upload
              ref="uploadRef"
              :action="uploadAction"
              :headers="uploadHeaders"
              :data="uploadData"
              :auto-upload="true"
              :show-file-list="false"
              accept="image/*"
              :limit="1"
              :before-upload="beforeUpload"
              :on-success="handleUploadSuccess"
              :on-error="handleUploadError"
              :on-progress="handleUploadProgress"
              :on-exceed="handleExceed"
              class="flex-shrink-0"
            >
              <el-button 
                type="primary" 
                :loading="uploadLoading"
                size="default"
              >
                {{ uploadLoading ? '上传中...' : '上传图片' }}
              </el-button>
            </el-upload>
          </div>
          
          <!-- 图片预览 -->
          <div v-if="formData.url" class="mt-4">
            <div class="relative inline-block">
              <el-image 
                :src="formData.url" 
                class="w-32 h-20 rounded border border-gray-200"
                fit="cover"
                :preview-src-list="[formData.url]"
                :z-index="3000"
                preview-teleported
              />
              <!-- 删除按钮 -->
              <el-button
                type="danger"
                size="small"
                circle
                class="absolute -top-2 -right-2 w-6 h-6 p-0 shadow-lg"
                @click="handleDeleteImage"
                :loading="deleteImageLoading"
              >
                <i class="i-mdi-close text-xs"></i>
              </el-button>
            </div>
            <!-- 图片信息 -->
            <div class="mt-2 text-xs text-gray-500">
              <p>点击预览图可以查看大图</p>
              <p>点击右上角 × 可以删除图片{{ currentImageFileId ? '' : '（已有图片仅清空链接）' }}</p>
            </div>
          </div>
          
          <!-- 上传提示 -->
          <div v-else class="mt-2 text-xs text-gray-500">
            <p>支持 JPG、PNG、GIF、WebP 格式，文件大小不超过 5MB</p>
            <p>建议尺寸：400×300px 或等比例缩放</p>
          </div>
        </el-form-item>
        
        <el-form-item label="平台描述" prop="description">
          <el-input 
            v-model="formData.description" 
            type="textarea"
            :rows="3"
            placeholder="请输入平台描述"
          />
        </el-form-item>
        
        <el-form-item label="技术参数" prop="parameters">
          <el-input 
            v-model="formData.parameters" 
            type="textarea"
            :rows="4"
            placeholder="请输入技术参数"
          />
        </el-form-item>
        
        <el-form-item label="应用领域" prop="applications">
          <el-input 
            v-model="formData.applications" 
            type="textarea"
            :rows="3"
            placeholder="请输入应用领域"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button 
          type="primary" 
          :loading="submitLoading"
          @click="handleSubmit"
        >
          确定
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { partPlatformApi } from '@/api/content'
import type { CreatePartPlatformData, PartPlatform, UpdatePartPlatformData } from '@/types/api'
import { formatDate } from '@/utils/dateUtils'
import type { FormInstance, FormRules } from 'element-plus'
import { ElMessage, ElMessageBox } from 'element-plus'
import { computed, nextTick, onMounted, reactive, ref, watch } from 'vue'

// 响应式数据
const formRef = ref<FormInstance>()
const loading = ref(false)
const dialogVisible = ref(false)
const submitLoading = ref(false)
const selectedItems = ref<PartPlatform[]>([])
const tableData = ref<PartPlatform[]>([])
const total = ref(0)

// 上传相关
const uploadRef = ref()
const uploadLoading = ref(false)
const deleteImageLoading = ref(false)
const currentImageFileId = ref<number | null>(null)

const searchParams = reactive({
  keyword: ''
})

const pagination = reactive({
  page: 1,
  size: 10
})

const formData = reactive<CreatePartPlatformData & { id?: number }>({
  name: '',
  url: '',
  description: '',
  parameters: '',
  applications: '',
  order: 0
})

// 计算属性
const isEdit = computed(() => !!formData.id)
const dialogTitle = computed(() => isEdit.value ? '编辑平台' : '新建平台')

// 上传相关计算属性
const uploadAction = computed(() => {
  return `${import.meta.env.VITE_API_BASE_URL}/files/upload`
})

const uploadHeaders = computed(() => {
  const token = localStorage.getItem('admin_token')
  return token ? { Authorization: `Bearer ${token}` } : {}
})

const uploadData = computed(() => {
  return { module: 'platform' }
})

// 表单验证规则
const formRules: FormRules = {
  name: [
    { required: true, message: '请输入平台名称', trigger: 'blur' }
  ],
  url: [
    { required: true, message: '请输入平台图片链接', trigger: 'blur' }
  ],
  description: [
    { required: true, message: '请输入平台描述', trigger: 'blur' }
  ],
  parameters: [
    { required: true, message: '请输入技术参数', trigger: 'blur' }
  ],
  applications: [
    { required: true, message: '请输入应用领域', trigger: 'blur' }
  ],
  order: [
    { required: true, message: '请输入排序', trigger: 'blur' },
    { type: 'number', min: 0, message: '排序必须大于等于0', trigger: 'blur' }
  ]
}

// 方法
const fetchData = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.page,
      size: pagination.size,
      ...searchParams
    }
    const response = await partPlatformApi.getList(params)
    tableData.value = response.list
    total.value = response.total
  } catch (error) {
    ElMessage.error('获取数据失败')
    console.error('获取平台列表失败:', error)
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  pagination.page = 1
  fetchData()
}

const handleReset = () => {
  searchParams.keyword = ''
  pagination.page = 1
  fetchData()
}

const handleAdd = () => {
  console.log('➕ [Platform] 新建平台')
  resetForm()
  // 使用 nextTick 确保表单重置完成后再显示对话框
  nextTick(() => {
    dialogVisible.value = true
  })
}

const handleEdit = (row: PartPlatform) => {
  resetForm() // 先重置表单，确保清空之前的数据
  // 使用 nextTick 确保重置完成后再填充数据
  nextTick(() => {
    Object.assign(formData, row)
    // 编辑时清空文件ID，因为现有图片没有上传ID
    currentImageFileId.value = null
  })
  dialogVisible.value = true
}

const handleDelete = async (row: PartPlatform) => {
  try {
    await ElMessageBox.confirm(`确定要删除"${row.name}"吗？`, '确认删除', {
      type: 'warning'
    })
    
    await partPlatformApi.delete(row.id)
    ElMessage.success('删除成功')
    fetchData()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败')
      console.error('删除平台失败:', error)
    }
  }
}

const handleBatchDelete = async () => {
  if (selectedItems.value.length === 0) {
    ElMessage.warning('请选择要删除的项目')
    return
  }
  
  try {
    await ElMessageBox.confirm(`确定要删除选中的 ${selectedItems.value.length} 个平台吗？`, '确认删除', {
      type: 'warning'
    })
    
    for (const item of selectedItems.value) {
      await partPlatformApi.delete(item.id)
    }
    
    ElMessage.success('批量删除成功')
    selectedItems.value = []
    fetchData()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('批量删除失败')
      console.error('批量删除平台失败:', error)
    }
  }
}

const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    submitLoading.value = true
    
    if (isEdit.value) {
      const { id, ...updateData } = formData
      await partPlatformApi.update(id!, updateData as UpdatePartPlatformData)
      ElMessage.success('更新成功')
    } else {
      const { id, ...createData } = formData
      await partPlatformApi.create(createData as CreatePartPlatformData)
      ElMessage.success('创建成功')
    }
    
    dialogVisible.value = false
    fetchData()
  } catch (error) {
    ElMessage.error(isEdit.value ? '更新失败' : '创建失败')
    console.error('提交平台失败:', error)
  } finally {
    submitLoading.value = false
  }
}

const handleSelectionChange = (selection: PartPlatform[]) => {
  selectedItems.value = selection
}

const handlePageChange = (page: number) => {
  pagination.page = page
  fetchData()
}

const handleSizeChange = (size: number) => {
  pagination.size = size
  pagination.page = 1
  fetchData()
}

const resetForm = () => {
  console.log('🔄 [Platform] 重置表单数据')

  // 先重置表单验证状态
  formRef.value?.resetFields()

  // 清空表单数据
  Object.assign(formData, {
    id: undefined,
    name: '',
    url: '',
    description: '',
    parameters: '',
    applications: '',
    order: 0
  })

  // 重置上传相关状态
  currentImageFileId.value = null
  uploadLoading.value = false
  deleteImageLoading.value = false

  // 清空上传组件
  if (uploadRef.value) {
    uploadRef.value.clearFiles()
  }

  console.log('✅ [Platform] 表单重置完成:', formData)
}

const onDialogOpen = () => {
  console.log('📖 [Platform] 对话框打开，当前模式:', isEdit.value ? '编辑' : '新建')
  console.log('📊 [Platform] 当前表单数据:', formData)

  // 如果是新建模式但表单中有数据，说明可能没有正确重置，再次重置
  if (!isEdit.value && (formData.name || formData.url || formData.description)) {
    console.log('⚠️ [Platform] 检测到新建模式但表单有数据，强制重置')
    resetForm()
  }
}

// 上传相关方法
const beforeUpload = (file: File) => {
  const isValidType = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'].includes(file.type)
  if (!isValidType) {
    ElMessage.error('只能上传 JPG、PNG、GIF、WebP 格式的图片!')
    return false
  }
  
  const isLt5M = file.size / 1024 / 1024 < 5
  if (!isLt5M) {
    ElMessage.error('图片大小不能超过 5MB!')
    return false
  }
  
  uploadLoading.value = true
  return true
}

const handleUploadSuccess = (response: any) => {
  uploadLoading.value = false
  
  if (response.code === 200 && response.data) {
    formData.url = response.data.url || response.data.cosUrl
    // 保存文件ID，用于后续删除
    if (response.data.id) {
      currentImageFileId.value = response.data.id
    }
    ElMessage.success('图片上传成功')
  } else {
    ElMessage.error(response.message || '图片上传失败')
  }
}

const handleUploadError = (error: any) => {
  uploadLoading.value = false
  console.error('上传失败:', error)
  ElMessage.error('图片上传失败，请重试')
}

const handleUploadProgress = (event: any) => {
  // 可以在这里处理上传进度
  console.log('上传进度:', event.percent)
}

const handleExceed = () => {
  ElMessage.warning('只能上传一张图片')
}

const handleDeleteImage = async () => {
  try {
    if (currentImageFileId.value) {
      // 有文件ID，说明是新上传的文件，需要删除COS中的文件
      await ElMessageBox.confirm('确定要删除这张图片吗？删除后将无法恢复。', '确认删除', {
        type: 'warning'
      })
      
      deleteImageLoading.value = true
      
      // 调用删除文件接口
      await fetch(`${import.meta.env.VITE_API_BASE_URL}/files/${currentImageFileId.value}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('admin_token')}`
        }
      })
      
      ElMessage.success('图片删除成功')
    } else {
      // 没有文件ID，说明是已有文件或手动输入的URL，只清空界面
      await ElMessageBox.confirm('确定要清空图片链接吗？', '确认清空', {
        type: 'warning'
      })
      ElMessage.success('图片链接已清空')
    }
    
    // 清空图片URL和文件ID
    formData.url = ''
    currentImageFileId.value = null
    
    // 清空上传组件
    if (uploadRef.value) {
      uploadRef.value.clearFiles()
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除图片失败:', error)
      ElMessage.error('删除图片失败，请重试')
    }
  } finally {
    deleteImageLoading.value = false
  }
}

// 监听器
watch(() => formData.url, (newUrl, oldUrl) => {
  if (oldUrl && !newUrl && uploadRef.value) {
    uploadRef.value.clearFiles()
  }
})

// 生命周期
onMounted(() => {
  fetchData()
})
</script> 