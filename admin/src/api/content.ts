import type {
  CreateFriendLinkData,
  CreateNewsData,
  CreatePartnerData,
  CreatePartPlatformData,
  CreateProjectCaseData,
  CreateRecruitmentData,
  CreateServiceData,
  CreateSwiperData,
  FriendLink,
  News,
  PaginatedResponse,
  PaginationParams,
  Partner,
  PartPlatform,
  ProjectCase,
  Recruitment,
  Service,
  Swiper,
  UpdateFriendLinkData,
  UpdateNewsData,
  UpdatePartnerData,
  UpdatePartPlatformData,
  UpdateProjectCaseData,
  UpdateRecruitmentData,
  UpdateServiceData,
  UpdateSwiperData
} from '@/types/api'
import { http } from '@/utils/request'

// 轮播图 API
export const swiperApi = {
  getList: (params?: PaginationParams & { keyword?: string }): Promise<PaginatedResponse<Swiper>> => {
    return http.get('/swipers', params)
  },
  
  getById: (id: number): Promise<Swiper> => {
    return http.get(`/swipers/${id}`)
  },
  
  create: (data: CreateSwiperData): Promise<Swiper> => {
    return http.post('/swipers', data)
  },
  
  update: (id: number, data: UpdateSwiperData): Promise<Swiper> => {
    return http.put(`/swipers/${id}`, data)
  },
  
  delete: (id: number): Promise<void> => {
    return http.delete(`/swipers/${id}`)
  },

  updateOrder: (id: number, order: number): Promise<Swiper> => {
    return http.patch(`/swipers/${id}/order`, { order })
  }
}

// 新闻 API
export const newsApi = {
  getList: (params?: PaginationParams & { 
    keyword?: string
    isHomePage?: boolean 
  }): Promise<PaginatedResponse<News>> => {
    return http.get('/news', params)
  },
  
  getById: (id: number): Promise<News> => {
    return http.get(`/news/${id}`)
  },
  
  create: (data: CreateNewsData): Promise<News> => {
    return http.post('/news', data)
  },
  
  update: (id: number, data: UpdateNewsData): Promise<News> => {
    return http.put(`/news/${id}`, data)
  },
  
  delete: (id: number): Promise<void> => {
    return http.delete(`/news/${id}`)
  },

  setHomePage: (id: number, isHomePage: boolean): Promise<News> => {
    return http.patch(`/news/${id}/home-page`, { isHomePage })
  }
}

// 服务 API
export const serviceApi = {
  getList: (params?: PaginationParams & { 
    keyword?: string
    type?: string 
  }): Promise<PaginatedResponse<Service>> => {
    return http.get('/services', params)
  },
  
  getById: (id: number): Promise<Service> => {
    return http.get(`/services/${id}`)
  },
  
  create: (data: CreateServiceData): Promise<Service> => {
    return http.post('/services', data)
  },
  
  update: (id: number, data: UpdateServiceData): Promise<Service> => {
    return http.put(`/services/${id}`, data)
  },
  
  delete: (id: number): Promise<void> => {
    return http.delete(`/services/${id}`)
  },

  updateOrder: (id: number, order: number): Promise<Service> => {
    return http.patch(`/services/${id}/order`, { order })
  },

  getTypes: (): Promise<string[]> => {
    return http.get('/services/types')
  }
}

// 项目案例 API
export const projectCaseApi = {
  getList: (params?: PaginationParams & { keyword?: string }): Promise<PaginatedResponse<ProjectCase>> => {
    return http.get('/project-cases', params)
  },
  
  getById: (id: number): Promise<ProjectCase> => {
    return http.get(`/project-cases/${id}`)
  },
  
  create: (data: CreateProjectCaseData): Promise<ProjectCase> => {
    return http.post('/project-cases', data)
  },
  
  update: (id: number, data: UpdateProjectCaseData): Promise<ProjectCase> => {
    return http.put(`/project-cases/${id}`, data)
  },
  
  delete: (id: number): Promise<void> => {
    return http.delete(`/project-cases/${id}`)
  },

  updateSort: (id: number, sort: number): Promise<ProjectCase> => {
    return http.patch(`/project-cases/${id}/sort`, { sort })
  }
}

// 合作伙伴 API
export const partnerApi = {
  getList: (params?: PaginationParams & { keyword?: string }): Promise<PaginatedResponse<Partner>> => {
    return http.get('/partners', params)
  },
  
  getById: (id: number): Promise<Partner> => {
    return http.get(`/partners/${id}`)
  },
  
  create: (data: CreatePartnerData): Promise<Partner> => {
    return http.post('/partners', data)
  },
  
  update: (id: number, data: UpdatePartnerData): Promise<Partner> => {
    return http.put(`/partners/${id}`, data)
  },
  
  delete: (id: number): Promise<void> => {
    return http.delete(`/partners/${id}`)
  }
}

// 友情链接 API
export const friendLinkApi = {
  getList: (params?: PaginationParams & { keyword?: string }): Promise<PaginatedResponse<FriendLink>> => {
    return http.get('/friendlinks', params)
  },
  
  getById: (id: number): Promise<FriendLink> => {
    return http.get(`/friendlinks/${id}`)
  },
  
  create: (data: CreateFriendLinkData): Promise<FriendLink> => {
    return http.post('/friendlinks', data)
  },
  
  update: (id: number, data: UpdateFriendLinkData): Promise<FriendLink> => {
    return http.put(`/friendlinks/${id}`, data)
  },
  
  delete: (id: number): Promise<void> => {
    return http.delete(`/friendlinks/${id}`)
  },

  updateOrder: (id: number, order: number): Promise<FriendLink> => {
    return http.patch(`/friendlinks/${id}/order`, { order })
  }
}

// 招聘信息 API
export const recruitmentApi = {
  getList: (params?: PaginationParams & { 
    keyword?: string
    location?: string 
  }): Promise<PaginatedResponse<Recruitment>> => {
    return http.get('/recruitments', params)
  },
  
  getById: (id: number): Promise<Recruitment> => {
    return http.get(`/recruitments/${id}`)
  },
  
  create: (data: CreateRecruitmentData): Promise<Recruitment> => {
    return http.post('/recruitments', data)
  },
  
  update: (id: number, data: UpdateRecruitmentData): Promise<Recruitment> => {
    return http.put(`/recruitments/${id}`, data)
  },
  
  delete: (id: number): Promise<void> => {
    return http.delete(`/recruitments/${id}`)
  },

  updateOrder: (id: number, order: number): Promise<Recruitment> => {
    return http.patch(`/recruitments/${id}/order`, { order })
  }
}

// 平台 API
export const partPlatformApi = {
  getList: (params?: PaginationParams & { keyword?: string }): Promise<PaginatedResponse<PartPlatform>> => {
    return http.get('/part-platforms', params)
  },
  
  getById: (id: number): Promise<PartPlatform> => {
    return http.get(`/part-platforms/${id}`)
  },
  
  create: (data: CreatePartPlatformData): Promise<PartPlatform> => {
    return http.post('/part-platforms', data)
  },
  
  update: (id: number, data: UpdatePartPlatformData): Promise<PartPlatform> => {
    return http.put(`/part-platforms/${id}`, data)
  },
  
  delete: (id: number): Promise<void> => {
    return http.delete(`/part-platforms/${id}`)
  },

  updateOrder: (id: number, order: number): Promise<PartPlatform> => {
    return http.patch(`/part-platforms/${id}/order`, { order })
  }
} 