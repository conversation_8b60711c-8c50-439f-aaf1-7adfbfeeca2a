#!/bin/bash

# 服务器部署文件准备脚本
# 只准备运行Docker镜像所需的配置文件

set -e

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 配置
DEPLOY_DIR="server-deploy"
CURRENT_DIR=$(pwd)

# 显示帮助信息
show_help() {
    echo "服务器部署文件准备脚本"
    echo "只准备运行Docker镜像所需的配置文件"
    echo
    echo "用法: $0 [选项]"
    echo
    echo "选项:"
    echo "  -h, --help              显示帮助信息"
    echo "  -o, --output DIR        指定输出目录 (默认: server-deploy)"
    echo "  -c, --clean             清理输出目录"
    echo "  --compress              创建压缩包"
    echo
    echo "示例:"
    echo "  $0                      # 准备部署文件到 server-deploy 目录"
    echo "  $0 --compress           # 创建压缩包"
    echo "  $0 -c --compress        # 清理并创建压缩包"
}

# 清理输出目录
clean_output() {
    if [ -d "$DEPLOY_DIR" ]; then
        print_info "清理输出目录: $DEPLOY_DIR"
        rm -rf "$DEPLOY_DIR"
    fi
}

# 创建目录结构
create_structure() {
    print_info "创建目录结构..."
    mkdir -p "$DEPLOY_DIR"
}

# 复制部署配置
copy_deployment_config() {
    print_info "复制部署配置文件..."

    # 复制Docker Compose生产环境配置
    if [ -f "deployment/docker-compose.prod.yml" ]; then
        cp deployment/docker-compose.prod.yml "$DEPLOY_DIR/"
        print_success "已复制: docker-compose.prod.yml"
    else
        print_error "deployment/docker-compose.prod.yml 不存在"
        exit 1
    fi

    # 复制MySQL配置文件
    if [ -d "deployment/mysql" ]; then
        cp -r deployment/mysql "$DEPLOY_DIR/"
        print_success "已复制: mysql配置目录"
    else
        print_error "deployment/mysql 目录不存在"
        exit 1
    fi

    print_info "Caddy配置已内置在Docker镜像中"
}

# 复制环境配置
copy_env_config() {
    print_info "复制环境配置文件..."
    
    # 复制环境变量配置
    if [ -f "docker.env" ]; then
        cp docker.env "$DEPLOY_DIR/"
        print_success "已复制: docker.env"
    else
        # 如果没有docker.env，复制示例文件
        if [ -f "docker.env.example" ]; then
            cp docker.env.example "$DEPLOY_DIR/docker.env.example"
            print_warning "docker.env 不存在，已复制示例文件"
        fi
    fi
}

# 创建部署脚本
create_deploy_scripts() {
    print_info "创建部署脚本..."

    # 复制健康检查脚本
    if [ -f "deployment/check-services.sh" ]; then
        cp "deployment/check-services.sh" "$DEPLOY_DIR/"
        chmod +x "$DEPLOY_DIR/check-services.sh"
        print_success "已复制: check-services.sh"
    fi

    # 创建启动脚本
    cat > "$DEPLOY_DIR/start.sh" << 'EOF'
#!/bin/bash

# 启动服务脚本

set -e

# 颜色输出
GREEN='\033[0;32m'
BLUE='\033[0;34m'
RED='\033[0;31m'
NC='\033[0m'

print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查Docker是否安装
if ! command -v docker &> /dev/null; then
    print_error "Docker 未安装，请先安装Docker"
    exit 1
fi

# 检查Docker Compose是否安装
if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
    print_error "Docker Compose 未安装，请先安装Docker Compose"
    exit 1
fi

# 检查环境配置文件
if [ ! -f "docker.env" ]; then
    print_error "docker.env 文件不存在，请先配置环境变量"
    print_info "可以从 docker.env.example 复制并修改"
    exit 1
fi

print_info "启动微智领域服务..."

# 拉取最新镜像
print_info "拉取最新镜像..."
docker-compose -f docker-compose.prod.yml --env-file docker.env pull

# 启动服务
print_info "启动服务..."
docker-compose -f docker-compose.prod.yml --env-file docker.env up -d

# 等待服务启动
sleep 5

# 检查服务状态
print_info "检查服务状态..."
docker-compose -f docker-compose.prod.yml --env-file docker.env ps

print_success "服务启动完成！"

# 运行健康检查
if [ -f "./check-services.sh" ]; then
    print_info "运行健康检查..."
    echo ""
    ./check-services.sh
else
    print_info "可以使用以下命令查看日志："
    echo "  docker-compose -f docker-compose.prod.yml --env-file docker.env logs -f"
    echo ""
    print_info "可以使用以下命令进行健康检查："
    echo "  ./check-services.sh"
fi
EOF

    # 创建停止脚本
    cat > "$DEPLOY_DIR/stop.sh" << 'EOF'
#!/bin/bash

# 停止服务脚本

set -e

# 颜色输出
GREEN='\033[0;32m'
BLUE='\033[0;34m'
NC='\033[0m'

print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_info "停止微智领域服务..."

# 停止服务
docker-compose -f docker-compose.prod.yml --env-file docker.env down

print_success "服务已停止"
EOF

    # 创建更新脚本
    cat > "$DEPLOY_DIR/update.sh" << 'EOF'
#!/bin/bash

# 更新服务脚本

set -e

# 颜色输出
GREEN='\033[0;32m'
BLUE='\033[0;34m'
NC='\033[0m'

print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_info "更新微智领域服务..."

# 拉取最新镜像
print_info "拉取最新镜像..."
docker-compose -f docker-compose.prod.yml --env-file docker.env pull

# 重启服务
print_info "重启服务..."
docker-compose -f docker-compose.prod.yml --env-file docker.env up -d

print_success "服务更新完成！"
EOF

    # 给脚本添加执行权限
    chmod +x "$DEPLOY_DIR/start.sh"
    chmod +x "$DEPLOY_DIR/stop.sh"
    chmod +x "$DEPLOY_DIR/update.sh"
    
    print_success "已创建部署脚本: start.sh, stop.sh, update.sh"
}

# 创建部署说明文件
create_deploy_instructions() {
    print_info "创建部署说明文件..."
    
    cat > "$DEPLOY_DIR/DEPLOY_INSTRUCTIONS.md" << 'EOF'
# 服务器部署说明

## 环境要求
- Docker 20.10+
- Docker Compose 2.0+
- 网络连接（用于拉取镜像）

## 部署步骤

### 1. 配置环境变量
```bash
# 如果没有 docker.env 文件，从示例复制
cp docker.env.example docker.env

# 编辑环境配置文件
vim docker.env
```

**重要配置项：**
- `MYSQL_ROOT_PASSWORD`: MySQL root密码
- `MYSQL_PASSWORD`: 应用数据库密码
- `JWT_SECRET`: JWT密钥
- `ADMIN_PASSWORD`: 管理员密码
- `DOMAIN`: 域名配置

### 2. 启动服务
```bash
# 使用启动脚本（推荐）
./start.sh

# 或手动启动
docker-compose -f docker-compose.prod.yml --env-file docker.env up -d
```

### 3. 检查服务状态
```bash
# 查看服务状态
docker-compose -f docker-compose.prod.yml --env-file docker.env ps

# 查看日志
docker-compose -f docker-compose.prod.yml --env-file docker.env logs -f

# 查看特定服务日志
docker-compose -f docker-compose.prod.yml --env-file docker.env logs -f web
```

### 4. 服务管理
```bash
# 停止服务
./stop.sh

# 更新服务（拉取最新镜像并重启）
./update.sh

# 重启服务
docker-compose -f docker-compose.prod.yml --env-file docker.env restart

# 查看资源使用情况
docker stats
```

## 访问地址
- 前端网站: http://your-domain.com
- 管理后台: http://your-domain.com/admin
- API接口: http://your-domain.com/api

## 健康检查

### 自动健康检查
```bash
# 运行完整的健康检查
./check-services.sh
```

健康检查脚本会自动检查：
- Docker环境
- 容器状态和健康状态
- 端口连通性
- HTTP服务响应
- 日志中的错误
- 资源使用情况

### 手动检查命令
```bash
# 查看所有容器状态
docker ps

# 查看服务日志
docker-compose -f docker-compose.prod.yml --env-file docker.env logs -f

# 查看特定服务日志
docker logs -f weishi-web-prod
docker logs -f weishi-server-prod
docker logs -f weishi-caddy-prod
docker logs -f weishi-mysql-prod

# 检查端口监听
netstat -tlnp | grep -E ":(80|443|3002|3003|3307)"

# 测试HTTP响应
curl -I http://localhost
curl -I http://localhost/admin
curl -I http://localhost/health
```

## 故障排除

### 1. 服务无法启动
```bash
# 查看详细日志
docker-compose -f docker-compose.prod.yml --env-file docker.env logs

# 检查端口占用
netstat -tlnp | grep :80
netstat -tlnp | grep :443
```

### 2. 数据库连接失败
```bash
# 检查MySQL服务状态
docker-compose -f docker-compose.prod.yml --env-file docker.env logs mysql

# 进入MySQL容器
docker-compose -f docker-compose.prod.yml --env-file docker.env exec mysql mysql -u root -p
```

### 3. 镜像拉取失败
```bash
# 手动拉取镜像
docker pull registry.cn-hangzhou.aliyuncs.com/vest/weizhi_server:latest
docker pull registry.cn-hangzhou.aliyuncs.com/vest/weizhi_web:latest
docker pull registry.cn-hangzhou.aliyuncs.com/vest/weizhi_admin_caddy:latest
```

## 备份和恢复

### 数据备份
```bash
# 备份MySQL数据
docker-compose -f docker-compose.prod.yml --env-file docker.env exec mysql mysqldump -u root -p weizhi > backup.sql

# 备份上传文件（如果有）
tar -czf uploads_backup.tar.gz uploads/
```

### 数据恢复
```bash
# 恢复MySQL数据
docker-compose -f docker-compose.prod.yml --env-file docker.env exec -T mysql mysql -u root -p weizhi < backup.sql
```

## 监控和维护
- 定期检查磁盘空间
- 定期备份数据库
- 监控服务日志
- 定期更新镜像
EOF
}

# 创建压缩包
create_archive() {
    print_info "创建压缩包..."
    
    local archive_name="weishi-deploy-$(date +%Y%m%d-%H%M%S).tar.gz"
    
    tar -czf "$archive_name" -C "$DEPLOY_DIR" .
    
    print_success "压缩包已创建: $archive_name"
    print_info "压缩包大小: $(du -h "$archive_name" | cut -f1)"
}

# 显示文件列表
show_file_list() {
    print_info "准备的文件列表:"
    find "$DEPLOY_DIR" -type f | sort | sed 's|^'"$DEPLOY_DIR"'/||' | while read -r file; do
        echo "  $file"
    done
}

# 主函数
main() {
    print_info "=== 服务器部署文件准备 ==="
    print_info "输出目录: $DEPLOY_DIR"
    
    # 检查是否在项目根目录
    if [ ! -d "deployment" ]; then
        print_error "请在项目根目录运行此脚本"
        exit 1
    fi
    
    # 清理输出目录（如果需要）
    if [ "$CLEAN_OUTPUT" = "true" ]; then
        clean_output
    fi
    
    # 创建目录结构
    create_structure
    
    # 复制文件
    copy_deployment_config
    copy_env_config
    create_deploy_scripts
    create_deploy_instructions
    
    # 显示结果
    show_file_list
    
    print_success "部署文件准备完成！"
    print_info "总文件数: $(find "$DEPLOY_DIR" -type f | wc -l)"
    print_info "总大小: $(du -sh "$DEPLOY_DIR" | cut -f1)"
    
    # 创建压缩包（如果需要）
    if [ "$CREATE_ARCHIVE" = "true" ]; then
        create_archive
    fi
    
    echo
    print_info "接下来的步骤:"
    echo "1. 将 $DEPLOY_DIR 目录上传到服务器"
    echo "2. 在服务器上配置 docker.env 文件"
    echo "3. 运行 ./start.sh 启动服务"
    echo "4. 如果创建了压缩包，可以直接上传压缩包并解压"
}

# 初始化变量
CLEAN_OUTPUT=false
CREATE_ARCHIVE=false

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -o|--output)
            DEPLOY_DIR="$2"
            shift 2
            ;;
        -c|--clean)
            CLEAN_OUTPUT=true
            shift
            ;;
        --compress)
            CREATE_ARCHIVE=true
            shift
            ;;
        *)
            print_error "未知选项: $1"
            show_help
            exit 1
            ;;
    esac
done

# 执行主函数
main
