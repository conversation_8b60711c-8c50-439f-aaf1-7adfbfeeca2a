#!/bin/bash

# 蔚之领域智能科技 - 快速部署脚本
# 一键完成从环境准备到服务部署的全过程

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_step() {
    echo -e "${PURPLE}[STEP]${NC} $1"
}

print_banner() {
    echo -e "${CYAN}"
    cat << 'EOF'
    ╔══════════════════════════════════════════════════════════════╗
    ║                    蔚之领域智能科技                          ║
    ║                  阿里云Docker快速部署                       ║
    ║                                                              ║
    ║  本脚本将自动完成服务器环境准备和应用部署                    ║
    ╚══════════════════════════════════════════════════════════════╝
EOF
    echo -e "${NC}"
}

# 检查运行环境
check_environment() {
    print_step "检查运行环境..."
    
    # 检查是否为root用户
    if [ "$EUID" -ne 0 ]; then
        print_error "请使用root用户运行此脚本"
        exit 1
    fi
    
    # 检查网络连接
    if ! ping -c 1 google.com &> /dev/null && ! ping -c 1 baidu.com &> /dev/null; then
        print_error "网络连接失败，请检查网络设置"
        exit 1
    fi
    
    print_success "环境检查通过"
}

# 收集用户配置
collect_config() {
    print_step "收集部署配置信息..."
    
    echo "请提供以下配置信息："
    echo
    
    # 域名配置
    while [ -z "$DOMAIN" ]; do
        read -p "请输入您的域名 (例如: example.com): " DOMAIN
        if [ -z "$DOMAIN" ]; then
            print_warning "域名不能为空"
        fi
    done
    
    # 数据库密码
    while [ -z "$MYSQL_ROOT_PASSWORD" ]; do
        read -s -p "请设置MySQL root密码: " MYSQL_ROOT_PASSWORD
        echo
        if [ ${#MYSQL_ROOT_PASSWORD} -lt 8 ]; then
            print_warning "密码长度至少8位"
            MYSQL_ROOT_PASSWORD=""
        fi
    done
    
    while [ -z "$MYSQL_PASSWORD" ]; do
        read -s -p "请设置应用数据库密码: " MYSQL_PASSWORD
        echo
        if [ ${#MYSQL_PASSWORD} -lt 8 ]; then
            print_warning "密码长度至少8位"
            MYSQL_PASSWORD=""
        fi
    done
    
    # JWT密钥
    while [ -z "$JWT_SECRET" ]; do
        read -s -p "请设置JWT密钥 (至少32位): " JWT_SECRET
        echo
        if [ ${#JWT_SECRET} -lt 32 ]; then
            print_warning "JWT密钥长度至少32位"
            JWT_SECRET=""
        fi
    done
    
    # 管理员密码
    while [ -z "$ADMIN_PASSWORD" ]; do
        read -s -p "请设置管理员密码: " ADMIN_PASSWORD
        echo
        if [ ${#ADMIN_PASSWORD} -lt 8 ]; then
            print_warning "密码长度至少8位"
            ADMIN_PASSWORD=""
        fi
    done
    
    # 镜像仓库配置
    echo
    print_info "阿里云容器镜像服务配置 (可选，如果跳过将使用默认镜像):"
    read -p "阿里云容器镜像服务用户名 (可选): " REGISTRY_USERNAME
    if [ -n "$REGISTRY_USERNAME" ]; then
        read -s -p "阿里云容器镜像服务密码: " REGISTRY_PASSWORD
        echo
    fi
    
    # 邮箱配置
    echo
    read -p "告警邮箱地址 (可选): " ALERT_EMAIL
    
    print_success "配置信息收集完成"
}

# 确认配置
confirm_config() {
    print_step "确认配置信息..."
    
    echo
    echo "请确认以下配置信息："
    echo "================================"
    echo "域名: $DOMAIN"
    echo "MySQL root密码: [已设置]"
    echo "应用数据库密码: [已设置]"
    echo "JWT密钥: [已设置]"
    echo "管理员密码: [已设置]"
    echo "镜像仓库用户名: ${REGISTRY_USERNAME:-未设置}"
    echo "告警邮箱: ${ALERT_EMAIL:-未设置}"
    echo "================================"
    echo
    
    read -p "确认配置正确？(y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        print_error "部署已取消"
        exit 1
    fi
}

# 生成配置文件
generate_config() {
    print_step "生成配置文件..."
    
    # 生成随机密钥
    local cos_bucket="weishi-$(date +%s)"
    local oss_bucket="weishi-$(date +%s)"
    
    # 生成生产环境配置
    cat > deployment/production.env << EOF
# 蔚之领域智能科技 - 生产环境配置
# 由快速部署脚本自动生成

# 镜像仓库配置
REGISTRY_URL=registry.cn-hangzhou.aliyuncs.com
NAMESPACE=weishi
VERSION=latest

# 域名配置
DOMAIN=$DOMAIN
NUXT_PUBLIC_API_BASE=https://$DOMAIN

# 数据库配置
MYSQL_ROOT_PASSWORD=$MYSQL_ROOT_PASSWORD
MYSQL_PASSWORD=$MYSQL_PASSWORD
MYSQL_DATABASE=weizhi
MYSQL_USER=weishi
MYSQL_PORT=3306

# JWT安全配置
JWT_SECRET=$JWT_SECRET
JWT_EXPIRE_TIME=28800

# 管理员账号配置
ADMIN_USERNAME=admin
ADMIN_PASSWORD=$ADMIN_PASSWORD
ADMIN_INIT_PASSWORD=true

# 服务端口配置
SERVER_PORT=3001
WEB_PORT=3000
ADMIN_PORT=8080

# 文件上传配置
MAX_FILE_SIZE=52428800
ALLOWED_FILE_TYPES=image/jpeg,image/png,image/gif,image/webp,application/pdf,text/plain

# 其他配置
NODE_ENV=production
APP_ENV=production
DEBUG=false
VERBOSE_LOGGING=false

# 监控配置
ENABLE_MONITORING=true
LOG_LEVEL=info
LOG_RETENTION_DAYS=30

# 备份配置
ENABLE_BACKUP=true
BACKUP_RETENTION_DAYS=30
BACKUP_SCHEDULE="0 2 * * *"

# 安全配置
ENABLE_FIREWALL=true
SSL_EMAIL=admin@$DOMAIN
ENABLE_AUTO_SSL=true
EOF

    # 生成镜像仓库配置
    cat > deployment/aliyun-registry.env << EOF
# 阿里云容器镜像服务配置
REGISTRY_URL=registry.cn-hangzhou.aliyuncs.com
REGISTRY_USERNAME=${REGISTRY_USERNAME:-}
REGISTRY_PASSWORD=${REGISTRY_PASSWORD:-}
NAMESPACE=weizhi
PROJECT_NAME=weizhi
VERSION=latest
CLEANUP_LOCAL=false
ALIYUN_REGION=cn-hangzhou
EOF

    print_success "配置文件生成完成"
}

# 初始化服务器环境
setup_server() {
    print_step "初始化服务器环境..."
    
    if [ -f "deployment/scripts/setup-server.sh" ]; then
        chmod +x deployment/scripts/setup-server.sh
        ./deployment/scripts/setup-server.sh
    else
        print_error "服务器初始化脚本不存在"
        exit 1
    fi
}

# 部署服务
deploy_services() {
    print_step "部署服务..."
    
    if [ -f "deployment/scripts/deploy.sh" ]; then
        chmod +x deployment/scripts/deploy.sh
        
        # 设置环境变量
        export REGISTRY_USERNAME
        export REGISTRY_PASSWORD
        export SKIP_BACKUP=true  # 首次部署跳过备份
        
        ./deployment/scripts/deploy.sh
    else
        print_error "部署脚本不存在"
        exit 1
    fi
}

# 配置定时任务
setup_cron() {
    print_step "配置定时任务..."
    
    # 备份任务
    (crontab -l 2>/dev/null; echo "0 2 * * * /opt/weishi/deployment/scripts/backup.sh --compress") | crontab -
    
    # 监控任务
    (crontab -l 2>/dev/null; echo "*/30 * * * * /opt/weishi/deployment/scripts/monitor.sh") | crontab -
    
    print_success "定时任务配置完成"
}

# 显示部署结果
show_result() {
    print_step "部署完成！"
    
    echo
    echo -e "${GREEN}╔══════════════════════════════════════════════════════════════╗${NC}"
    echo -e "${GREEN}║                      部署成功！                             ║${NC}"
    echo -e "${GREEN}╚══════════════════════════════════════════════════════════════╝${NC}"
    echo
    
    print_info "=== 访问地址 ==="
    echo "🌐 网站首页: https://$DOMAIN"
    echo "🔧 管理后台: https://$DOMAIN/admin"
    echo "📚 API文档: https://$DOMAIN/api/docs"
    echo
    
    print_info "=== 管理员账号 ==="
    echo "👤 用户名: admin"
    echo "🔑 密码: [您设置的管理员密码]"
    echo
    
    print_info "=== 管理命令 ==="
    echo "📊 监控检查: ./deployment/scripts/monitor.sh"
    echo "💾 数据备份: ./deployment/scripts/backup.sh"
    echo "🔄 重启服务: docker-compose -f deployment/docker-compose.prod.yml restart"
    echo "📋 查看日志: docker-compose -f deployment/docker-compose.prod.yml logs -f"
    echo
    
    print_warning "=== 重要提醒 ==="
    echo "1. 请确保域名DNS已正确解析到服务器IP"
    echo "2. 首次访问可能需要等待SSL证书申请完成"
    echo "3. 建议立即登录管理后台修改默认设置"
    echo "4. 定期检查系统更新和备份"
    echo
    
    if [ -n "$ALERT_EMAIL" ]; then
        echo "📧 告警邮箱: $ALERT_EMAIL"
        echo "   系统会自动发送监控告警到此邮箱"
        echo
    fi
    
    print_success "感谢使用蔚之领域智能科技！"
}

# 主函数
main() {
    print_banner
    
    check_environment
    collect_config
    confirm_config
    generate_config
    setup_server
    deploy_services
    setup_cron
    show_result
}

# 显示帮助信息
show_help() {
    echo "蔚之领域智能科技 - 快速部署脚本"
    echo
    echo "用法: $0 [选项]"
    echo
    echo "选项:"
    echo "  -h, --help              显示帮助信息"
    echo "  --unattended            无人值守模式（需要预设环境变量）"
    echo
    echo "无人值守模式环境变量:"
    echo "  DOMAIN                  域名"
    echo "  MYSQL_ROOT_PASSWORD     MySQL root密码"
    echo "  MYSQL_PASSWORD          应用数据库密码"
    echo "  JWT_SECRET              JWT密钥"
    echo "  ADMIN_PASSWORD          管理员密码"
    echo "  REGISTRY_USERNAME       镜像仓库用户名（可选）"
    echo "  REGISTRY_PASSWORD       镜像仓库密码（可选）"
    echo "  ALERT_EMAIL             告警邮箱（可选）"
    echo
    echo "示例:"
    echo "  $0                      # 交互式部署"
    echo "  DOMAIN=example.com MYSQL_ROOT_PASSWORD=pass123 ... $0 --unattended"
}

# 无人值守模式
unattended_mode() {
    print_banner
    print_info "无人值守模式部署..."
    
    # 检查必需的环境变量
    if [ -z "$DOMAIN" ] || [ -z "$MYSQL_ROOT_PASSWORD" ] || [ -z "$MYSQL_PASSWORD" ] || [ -z "$JWT_SECRET" ] || [ -z "$ADMIN_PASSWORD" ]; then
        print_error "无人值守模式需要设置以下环境变量: DOMAIN, MYSQL_ROOT_PASSWORD, MYSQL_PASSWORD, JWT_SECRET, ADMIN_PASSWORD"
        exit 1
    fi
    
    check_environment
    generate_config
    setup_server
    deploy_services
    setup_cron
    show_result
}

# 解析命令行参数
case "${1:-}" in
    -h|--help)
        show_help
        exit 0
        ;;
    --unattended)
        unattended_mode
        ;;
    "")
        main
        ;;
    *)
        print_error "未知选项: $1"
        show_help
        exit 1
        ;;
esac
