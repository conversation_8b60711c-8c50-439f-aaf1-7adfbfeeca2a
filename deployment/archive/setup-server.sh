#!/bin/bash

# 阿里云服务器环境准备脚本
# 用于初始化服务器环境，安装必要软件

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查是否为root用户
check_root() {
    if [ "$EUID" -ne 0 ]; then
        print_error "请使用root用户运行此脚本"
        exit 1
    fi
}

# 检测操作系统
detect_os() {
    if [ -f /etc/os-release ]; then
        . /etc/os-release
        OS=$NAME
        VER=$VERSION_ID
    else
        print_error "无法检测操作系统"
        exit 1
    fi
    
    print_info "检测到操作系统: $OS $VER"
}

# 更新系统
update_system() {
    print_info "更新系统包..."
    
    if [[ $OS == *"Ubuntu"* ]] || [[ $OS == *"Debian"* ]]; then
        apt-get update -y
        apt-get upgrade -y
        apt-get install -y curl wget git vim htop unzip software-properties-common apt-transport-https ca-certificates gnupg lsb-release
    elif [[ $OS == *"CentOS"* ]] || [[ $OS == *"Red Hat"* ]] || [[ $OS == *"Alibaba Cloud Linux"* ]]; then
        yum update -y
        yum install -y curl wget git vim htop unzip yum-utils device-mapper-persistent-data lvm2
    else
        print_error "不支持的操作系统: $OS"
        exit 1
    fi
    
    print_success "系统更新完成"
}

# 安装Docker
install_docker() {
    print_info "安装Docker..."
    
    # 检查Docker是否已安装
    if command -v docker &> /dev/null; then
        print_warning "Docker已安装，版本: $(docker --version)"
        return 0
    fi
    
    if [[ $OS == *"Ubuntu"* ]] || [[ $OS == *"Debian"* ]]; then
        # 添加Docker官方GPG密钥
        curl -fsSL https://mirrors.aliyun.com/docker-ce/linux/ubuntu/gpg | apt-key add -
        
        # 添加Docker仓库
        add-apt-repository "deb [arch=amd64] https://mirrors.aliyun.com/docker-ce/linux/ubuntu $(lsb_release -cs) stable"
        
        # 安装Docker
        apt-get update -y
        apt-get install -y docker-ce docker-ce-cli containerd.io
        
    elif [[ $OS == *"CentOS"* ]] || [[ $OS == *"Red Hat"* ]] || [[ $OS == *"Alibaba Cloud Linux"* ]]; then
        # 添加Docker仓库
        yum-config-manager --add-repo https://mirrors.aliyun.com/docker-ce/linux/centos/docker-ce.repo
        
        # 安装Docker
        yum install -y docker-ce docker-ce-cli containerd.io
    fi
    
    # 启动Docker服务
    systemctl start docker
    systemctl enable docker
    
    # 配置Docker镜像加速器（阿里云）
    mkdir -p /etc/docker
    cat > /etc/docker/daemon.json << EOF
{
    "registry-mirrors": [
        "https://mirror.ccs.tencentyun.com",
        "https://registry.cn-hangzhou.aliyuncs.com",
        "https://docker.mirrors.ustc.edu.cn"
    ],
    "log-driver": "json-file",
    "log-opts": {
        "max-size": "100m",
        "max-file": "3"
    },
    "storage-driver": "overlay2",
    "storage-opts": [
        "overlay2.override_kernel_check=true"
    ]
}
EOF
    
    # 重启Docker服务
    systemctl restart docker
    
    print_success "Docker安装完成，版本: $(docker --version)"
}

# 安装Docker Compose
install_docker_compose() {
    print_info "安装Docker Compose..."
    
    # 检查Docker Compose是否已安装
    if command -v docker-compose &> /dev/null; then
        print_warning "Docker Compose已安装，版本: $(docker-compose --version)"
        return 0
    fi
    
    # 下载Docker Compose
    COMPOSE_VERSION="2.24.0"
    curl -L "https://github.com/docker/compose/releases/download/v${COMPOSE_VERSION}/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
    
    # 设置执行权限
    chmod +x /usr/local/bin/docker-compose
    
    # 创建软链接
    ln -sf /usr/local/bin/docker-compose /usr/bin/docker-compose
    
    print_success "Docker Compose安装完成，版本: $(docker-compose --version)"
}

# 配置防火墙
configure_firewall() {
    print_info "配置防火墙..."
    
    if [[ $OS == *"Ubuntu"* ]] || [[ $OS == *"Debian"* ]]; then
        # 安装ufw
        apt-get install -y ufw
        
        # 配置防火墙规则
        ufw --force reset
        ufw default deny incoming
        ufw default allow outgoing
        
        # 允许SSH
        ufw allow 22/tcp
        
        # 允许HTTP和HTTPS
        ufw allow 80/tcp
        ufw allow 443/tcp
        
        # 允许应用端口（可选，生产环境建议通过反向代理访问）
        # ufw allow 3000/tcp  # Web前端
        # ufw allow 3001/tcp  # API后端
        # ufw allow 8080/tcp  # 管理后台
        
        # 启用防火墙
        ufw --force enable
        
    elif [[ $OS == *"CentOS"* ]] || [[ $OS == *"Red Hat"* ]] || [[ $OS == *"Alibaba Cloud Linux"* ]]; then
        # 安装firewalld
        yum install -y firewalld
        systemctl start firewalld
        systemctl enable firewalld
        
        # 配置防火墙规则
        firewall-cmd --permanent --add-service=ssh
        firewall-cmd --permanent --add-service=http
        firewall-cmd --permanent --add-service=https
        
        # 重载防火墙配置
        firewall-cmd --reload
    fi
    
    print_success "防火墙配置完成"
}

# 创建部署目录
create_directories() {
    print_info "创建部署目录..."
    
    # 创建主目录
    mkdir -p /opt/weishi
    mkdir -p /opt/weishi/data
    mkdir -p /opt/weishi/logs
    mkdir -p /opt/weishi/backups
    mkdir -p /opt/weishi/scripts
    mkdir -p /opt/weishi/config
    
    # 设置权限
    chmod 755 /opt/weishi
    chmod 755 /opt/weishi/data
    chmod 755 /opt/weishi/logs
    chmod 755 /opt/weishi/backups
    chmod 755 /opt/weishi/scripts
    chmod 755 /opt/weishi/config
    
    print_success "部署目录创建完成"
}

# 安装监控工具
install_monitoring() {
    print_info "安装监控工具..."
    
    if [[ $OS == *"Ubuntu"* ]] || [[ $OS == *"Debian"* ]]; then
        apt-get install -y htop iotop nethogs ncdu tree
    elif [[ $OS == *"CentOS"* ]] || [[ $OS == *"Red Hat"* ]] || [[ $OS == *"Alibaba Cloud Linux"* ]]; then
        yum install -y htop iotop nethogs ncdu tree
    fi
    
    print_success "监控工具安装完成"
}

# 配置系统优化
optimize_system() {
    print_info "优化系统配置..."
    
    # 增加文件描述符限制
    cat >> /etc/security/limits.conf << EOF
* soft nofile 65536
* hard nofile 65536
* soft nproc 65536
* hard nproc 65536
EOF
    
    # 优化内核参数
    cat >> /etc/sysctl.conf << EOF
# 网络优化
net.core.rmem_max = 16777216
net.core.wmem_max = 16777216
net.ipv4.tcp_rmem = 4096 65536 16777216
net.ipv4.tcp_wmem = 4096 65536 16777216
net.core.netdev_max_backlog = 5000
net.ipv4.tcp_congestion_control = bbr

# 文件系统优化
fs.file-max = 2097152
vm.swappiness = 10
vm.dirty_ratio = 15
vm.dirty_background_ratio = 5
EOF
    
    # 应用内核参数
    sysctl -p
    
    print_success "系统优化完成"
}

# 创建部署用户
create_deploy_user() {
    print_info "创建部署用户..."
    
    # 创建weishi用户
    if ! id "weishi" &>/dev/null; then
        useradd -m -s /bin/bash weishi
        usermod -aG docker weishi
        
        # 设置sudo权限
        echo "weishi ALL=(ALL) NOPASSWD:ALL" > /etc/sudoers.d/weishi
        
        print_success "部署用户weishi创建完成"
    else
        print_warning "用户weishi已存在"
    fi
}

# 主函数
main() {
    print_info "开始初始化阿里云服务器环境..."
    echo
    
    check_root
    detect_os
    update_system
    install_docker
    install_docker_compose
    configure_firewall
    create_directories
    install_monitoring
    optimize_system
    create_deploy_user
    
    print_success "服务器环境初始化完成！"
    echo
    print_info "接下来可以："
    print_info "1. 上传项目文件到 /opt/weishi/"
    print_info "2. 配置环境变量文件"
    print_info "3. 运行部署脚本"
    echo
    print_warning "重要提醒："
    print_warning "1. 请修改SSH端口并配置密钥认证"
    print_warning "2. 请配置域名DNS解析"
    print_warning "3. 请设置强密码和密钥"
    print_warning "4. 请定期更新系统和软件"
}

# 显示帮助信息
show_help() {
    echo "阿里云服务器环境准备脚本"
    echo
    echo "用法: $0 [选项]"
    echo
    echo "选项:"
    echo "  -h, --help     显示帮助信息"
    echo "  --skip-fw      跳过防火墙配置"
    echo "  --skip-opt     跳过系统优化"
    echo
    echo "此脚本将安装和配置："
    echo "  - Docker和Docker Compose"
    echo "  - 防火墙规则"
    echo "  - 监控工具"
    echo "  - 系统优化"
    echo "  - 部署目录和用户"
}

# 解析命令行参数
SKIP_FIREWALL=false
SKIP_OPTIMIZATION=false

while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        --skip-fw)
            SKIP_FIREWALL=true
            shift
            ;;
        --skip-opt)
            SKIP_OPTIMIZATION=true
            shift
            ;;
        *)
            print_error "未知选项: $1"
            show_help
            exit 1
            ;;
    esac
done

# 执行主函数
main
