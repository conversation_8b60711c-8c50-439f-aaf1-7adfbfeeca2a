# 蔚之领域智能科技 - 阿里云Docker部署指南

本文档详细介绍如何将蔚之领域智能科技项目部署到阿里云服务器。

## 📋 目录

- [系统要求](#系统要求)
- [准备工作](#准备工作)
- [快速部署](#快速部署)
- [详细部署步骤](#详细部署步骤)
- [配置说明](#配置说明)
- [监控和维护](#监控和维护)
- [故障排除](#故障排除)
- [安全建议](#安全建议)

## 🖥️ 系统要求

### 服务器配置
- **CPU**: 2核心或以上
- **内存**: 4GB或以上（推荐8GB）
- **存储**: 40GB或以上SSD
- **网络**: 公网IP，带宽5Mbps或以上

### 操作系统支持
- Ubuntu 18.04/20.04/22.04 LTS
- CentOS 7/8
- Alibaba Cloud Linux 2/3
- Debian 9/10/11

### 必需软件
- Docker 20.10+
- Docker Compose 2.0+
- Git
- Curl

## 🚀 准备工作

### 1. 购买阿里云服务器

1. 登录[阿里云控制台](https://ecs.console.aliyun.com/)
2. 创建ECS实例，推荐配置：
   - 实例规格：ecs.c6.large（2vCPU 4GB）或以上
   - 镜像：Ubuntu 22.04 LTS
   - 系统盘：40GB SSD云盘
   - 网络：分配公网IP
   - 安全组：开放22、80、443端口

### 2. 域名配置

1. 在域名服务商处添加A记录：
   ```
   @ -> 服务器公网IP
   www -> 服务器公网IP
   ```

2. 等待DNS解析生效（通常5-30分钟）

### 3. 阿里云容器镜像服务

1. 开通[容器镜像服务](https://cr.console.aliyun.com/)
2. 创建命名空间：`weishi`
3. 获取访问凭证（用户名和密码）

## ⚡ 快速部署

### 多阶段构建架构

本项目采用Docker多阶段构建，将构建环境和运行环境分离：

- **Web前端**: Node.js构建环境 → 精简Node.js运行时
- **Go后端**: Go编译环境 → Alpine最小运行时
- **Vue管理后台**: Node.js构建环境 → Caddy静态文件服务

### 本地测试构建

```bash
# 1. 本地测试构建（推荐先测试）
./deployment/build-local.sh -t

# 2. 启动本地测试环境
docker-compose -f deployment/docker-compose.local.yml up -d

# 3. 访问测试
# 前端: http://localhost:3000
# API: http://localhost:3001
# 管理后台: http://localhost:8080
# 统一入口: http://localhost (需启用caddy profile)
```

### 生产环境部署

```bash
# 1. 连接服务器
ssh root@your-server-ip

# 2. 下载部署文件
git clone https://github.com/your-repo/weishi-deployment.git /opt/weishi
cd /opt/weishi

# 3. 初始化服务器环境
chmod +x deployment/scripts/*.sh
./deployment/scripts/setup-server.sh

# 4. 配置环境变量
cp deployment/production.env.example deployment/production.env
cp deployment/aliyun-registry.env.example deployment/aliyun-registry.env

# 编辑配置文件（重要！）
vim deployment/production.env
vim deployment/aliyun-registry.env

# 5. 构建并推送镜像（在本地执行）
# 并行构建（推荐）
./deployment/build-and-push.sh -p

# 或串行构建
./deployment/build-and-push.sh

# 6. 部署服务
./deployment/scripts/deploy.sh
```

## 📝 详细部署步骤

### 步骤1：服务器初始化

```bash
# 连接服务器
ssh root@your-server-ip

# 更新系统
apt update && apt upgrade -y

# 下载部署文件
git clone https://github.com/your-repo/weishi-deployment.git /opt/weishi
cd /opt/weishi

# 运行服务器初始化脚本
./deployment/scripts/setup-server.sh
```

初始化脚本会自动：
- 安装Docker和Docker Compose
- 配置防火墙规则
- 优化系统参数
- 创建部署目录和用户

### 步骤2：配置环境变量

```bash
# 复制配置模板
cp deployment/production.env.example deployment/production.env
cp deployment/aliyun-registry.env.example deployment/aliyun-registry.env
```

编辑 `deployment/production.env`：

```bash
# 必须修改的配置
DOMAIN=your-domain.com
MYSQL_ROOT_PASSWORD=your_very_secure_root_password
MYSQL_PASSWORD=your_very_secure_db_password
JWT_SECRET=your-super-complex-jwt-secret-key
ADMIN_PASSWORD=your_very_secure_admin_password

# 可选配置
COS_SECRET_ID=your_cos_secret_id
COS_SECRET_KEY=your_cos_secret_key
OSS_ACCESS_KEY_ID=your_oss_access_key_id
OSS_ACCESS_KEY_SECRET=your_oss_access_key_secret
```

编辑 `deployment/aliyun-registry.env`：

```bash
REGISTRY_USERNAME=your_aliyun_username
REGISTRY_PASSWORD=your_aliyun_password
NAMESPACE=weishi
```

### 步骤3：构建和推送镜像

在本地开发机器上执行：

```bash
# 设置环境变量
export REGISTRY_USERNAME=your_aliyun_username
export REGISTRY_PASSWORD=your_aliyun_password

# 方式1：并行构建（推荐，速度更快）
./deployment/build-and-push.sh -p

# 方式2：串行构建
./deployment/build-and-push.sh

# 方式3：只构建不推送（测试用）
./deployment/build-and-push.sh --build-only

# 方式4：只推送不构建（镜像已存在）
./deployment/build-and-push.sh --push-only

# 本地测试构建
./deployment/build-local.sh -t
```

**构建选项说明**：
- `-p, --parallel`: 启用并行构建，显著提升构建速度
- `--no-cache`: 禁用构建缓存，确保完全重新构建
- `--build-only`: 只构建镜像，不推送到远程仓库
- `--push-only`: 只推送镜像，不进行构建

### 步骤4：部署服务

在服务器上执行：

```bash
# 部署所有服务
./deployment/scripts/deploy.sh

# 查看服务状态
docker-compose -f deployment/docker-compose.prod.yml ps

# 查看日志
docker-compose -f deployment/docker-compose.prod.yml logs -f
```

### 步骤5：验证部署

```bash
# 检查服务健康状态
./deployment/scripts/monitor.sh

# 访问网站
curl -I https://your-domain.com

# 访问API
curl https://your-domain.com/api/health

# 访问管理后台
curl -I https://your-domain.com/admin
```

## 🏗️ 多阶段构建详解

### 构建架构

本项目采用Docker多阶段构建，将构建过程和运行环境完全分离：

#### 1. Web前端 (Nuxt3)
```dockerfile
# 构建阶段：Node.js + 源码 → .output构建产物
FROM node:22-alpine AS builder
# ... 安装依赖、构建应用

# 运行阶段：精简Node.js + 构建产物
FROM node:22-alpine AS runner
# ... 只复制构建产物和运行时依赖
```

#### 2. Go后端
```dockerfile
# 构建阶段：Go编译环境 → 二进制文件
FROM golang:1.23-alpine AS builder
# ... 编译Go应用，启用UPX压缩

# 运行阶段：Alpine + 二进制文件
FROM alpine:3.18 AS runner
# ... 只包含运行时必需的系统库
```

#### 3. Vue管理后台
```dockerfile
# 构建阶段：Node.js + 源码 → 静态文件
FROM node:22-alpine AS builder
# ... 构建Vue应用

# 运行阶段：Caddy + 静态文件
FROM caddy:2-alpine AS runner
# ... 使用Caddy提供静态文件服务
```

### 构建优势

- **镜像大小**: 减少60-80%的镜像大小
- **安全性**: 不包含源码、构建工具和开发依赖
- **构建效率**: 支持并行构建和智能缓存
- **部署速度**: 更小的镜像意味着更快的拉取和启动

### 本地测试

```bash
# 构建测试
./deployment/build-local.sh -t -v

# 启动本地环境
docker-compose -f deployment/docker-compose.local.yml up -d

# 查看镜像大小对比
docker images | grep weishi
```

## ⚙️ 配置说明

### 环境变量详解

| 变量名 | 说明 | 必填 | 默认值 |
|--------|------|------|--------|
| `DOMAIN` | 网站域名 | ✅ | - |
| `MYSQL_ROOT_PASSWORD` | MySQL root密码 | ✅ | - |
| `MYSQL_PASSWORD` | 应用数据库密码 | ✅ | - |
| `JWT_SECRET` | JWT密钥 | ✅ | - |
| `ADMIN_PASSWORD` | 管理员密码 | ✅ | - |
| `COS_SECRET_ID` | 腾讯云COS密钥ID | ❌ | - |
| `OSS_ACCESS_KEY_ID` | 阿里云OSS密钥ID | ❌ | - |

### 服务端口

| 服务 | 内部端口 | 外部端口 | 说明 |
|------|----------|----------|------|
| Caddy | 80/443 | 80/443 | 反向代理和SSL终端 |
| Web前端 | 3000 | - | Nuxt3应用 |
| API后端 | 3001 | - | Go API服务 |
| 管理后台 | 8080 | - | Vue管理界面 |
| MySQL | 3306 | 3306 | 数据库服务 |

### 目录结构

```
/opt/weishi/
├── deployment/
│   ├── docker-compose.prod.yml    # 生产环境Docker Compose
│   ├── production.env             # 生产环境变量
│   ├── aliyun-registry.env        # 镜像仓库配置
│   ├── caddy/                     # Caddy配置
│   ├── mysql/                     # MySQL配置
│   └── scripts/                   # 部署脚本
├── data/                          # 数据目录
├── logs/                          # 日志目录
└── backups/                       # 备份目录
```

## 📊 监控和维护

### 服务监控

```bash
# 一次性监控检查
./deployment/scripts/monitor.sh

# 持续监控（每5分钟检查一次）
./deployment/scripts/monitor.sh -c 300

# 生成监控报告
./deployment/scripts/monitor.sh -r
```

### 数据备份

```bash
# 完整备份
./deployment/scripts/backup.sh

# 仅备份数据库
./deployment/scripts/backup.sh -d

# 压缩备份
./deployment/scripts/backup.sh --compress
```

### 日志管理

```bash
# 查看所有服务日志
docker-compose -f deployment/docker-compose.prod.yml logs

# 查看特定服务日志
docker-compose -f deployment/docker-compose.prod.yml logs web

# 实时跟踪日志
docker-compose -f deployment/docker-compose.prod.yml logs -f server
```

### 服务管理

```bash
# 重启所有服务
docker-compose -f deployment/docker-compose.prod.yml restart

# 重启特定服务
docker-compose -f deployment/docker-compose.prod.yml restart web

# 停止所有服务
docker-compose -f deployment/docker-compose.prod.yml down

# 更新服务
./deployment/scripts/deploy.sh
```

## 🔧 故障排除

### 常见问题

#### 1. 容器启动失败

```bash
# 查看容器状态
docker ps -a

# 查看容器日志
docker logs container_name

# 检查配置文件
docker-compose -f deployment/docker-compose.prod.yml config
```

#### 2. 数据库连接失败

```bash
# 检查MySQL容器状态
docker exec weishi-mysql-prod mysqladmin ping -h localhost -u root -p

# 查看数据库日志
docker logs weishi-mysql-prod

# 重启数据库
docker-compose -f deployment/docker-compose.prod.yml restart mysql
```

#### 3. SSL证书问题

```bash
# 检查Caddy日志
docker logs weishi-caddy-prod

# 手动申请证书
docker exec weishi-caddy-prod caddy reload --config /etc/caddy/Caddyfile

# 检查域名解析
nslookup your-domain.com
```

#### 4. 性能问题

```bash
# 检查系统资源
htop
df -h
free -h

# 检查Docker资源使用
docker stats

# 优化配置
vim deployment/production.env
```

### 日志位置

- 应用日志：`/opt/weishi/logs/`
- Docker日志：`/var/lib/docker/containers/`
- 系统日志：`/var/log/`
- Caddy日志：Docker volume `weishi_caddy_logs`

## 🔒 安全建议

### 1. 服务器安全

```bash
# 修改SSH端口
vim /etc/ssh/sshd_config
# Port 2222

# 禁用密码登录，使用密钥认证
# PasswordAuthentication no

# 重启SSH服务
systemctl restart sshd
```

### 2. 防火墙配置

```bash
# 查看防火墙状态
ufw status

# 只开放必要端口
ufw allow 2222/tcp  # SSH
ufw allow 80/tcp    # HTTP
ufw allow 443/tcp   # HTTPS
```

### 3. 定期更新

```bash
# 更新系统包
apt update && apt upgrade -y

# 更新Docker镜像
./deployment/build-and-push.sh
./deployment/scripts/deploy.sh
```

### 4. 备份策略

```bash
# 设置定时备份
crontab -e

# 每天凌晨2点备份
0 2 * * * /opt/weishi/deployment/scripts/backup.sh --compress
```

## 📞 技术支持

如果在部署过程中遇到问题，请：

1. 查看本文档的故障排除部分
2. 检查日志文件获取详细错误信息
3. 联系技术支持团队

---

**注意**: 请确保在生产环境中修改所有默认密码和密钥，并定期进行安全更新。
