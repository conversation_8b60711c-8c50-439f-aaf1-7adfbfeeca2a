#!/bin/bash

# 蔚之领域智能科技 - 阿里云一键部署脚本
# 用于在阿里云服务器上部署整个应用栈

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 配置变量
DEPLOY_DIR="/opt/weishi"
BACKUP_DIR="/opt/weishi/backups"
ENV_FILE="production.env"
COMPOSE_FILE="docker-compose.prod.yml"
REGISTRY_ENV_FILE="aliyun-registry.env"

# 检查必要文件
check_requirements() {
    print_info "检查部署环境..."
    
    # 检查Docker
    if ! command -v docker &> /dev/null; then
        print_error "Docker未安装，请先运行 setup-server.sh"
        exit 1
    fi
    
    # 检查Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        print_error "Docker Compose未安装，请先运行 setup-server.sh"
        exit 1
    fi
    
    # 检查部署目录
    if [ ! -d "$DEPLOY_DIR" ]; then
        print_error "部署目录不存在: $DEPLOY_DIR"
        exit 1
    fi
    
    # 检查环境变量文件
    if [ ! -f "$ENV_FILE" ]; then
        print_error "环境变量文件不存在: $ENV_FILE"
        print_info "请复制 production.env.example 为 $ENV_FILE 并配置"
        exit 1
    fi
    
    # 检查Docker Compose文件
    if [ ! -f "$COMPOSE_FILE" ]; then
        print_error "Docker Compose文件不存在: $COMPOSE_FILE"
        exit 1
    fi
    
    print_success "环境检查通过"
}

# 加载环境变量
load_environment() {
    print_info "加载环境变量..."
    
    # 加载生产环境变量
    if [ -f "$ENV_FILE" ]; then
        set -a
        source "$ENV_FILE"
        set +a
        print_success "生产环境变量已加载"
    fi
    
    # 加载镜像仓库变量
    if [ -f "$REGISTRY_ENV_FILE" ]; then
        set -a
        source "$REGISTRY_ENV_FILE"
        set +a
        print_success "镜像仓库变量已加载"
    fi
}

# 登录镜像仓库
login_registry() {
    print_info "登录阿里云容器镜像服务..."
    
    if [ -z "$REGISTRY_USERNAME" ] || [ -z "$REGISTRY_PASSWORD" ]; then
        print_warning "未设置镜像仓库认证信息"
        print_info "请设置 REGISTRY_USERNAME 和 REGISTRY_PASSWORD 环境变量"
        read -p "是否手动登录？(y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            docker login "$REGISTRY_URL"
        else
            print_error "无法登录镜像仓库"
            exit 1
        fi
    else
        echo "$REGISTRY_PASSWORD" | docker login "$REGISTRY_URL" -u "$REGISTRY_USERNAME" --password-stdin
        print_success "镜像仓库登录成功"
    fi
}

# 拉取最新镜像
pull_images() {
    print_info "拉取最新镜像..."
    
    # 定义镜像列表
    IMAGES=(
        "${REGISTRY_URL}/${NAMESPACE}/weishi-server-go:${VERSION:-latest}"
        "${REGISTRY_URL}/${NAMESPACE}/weishi-web:${VERSION:-latest}"
        "${REGISTRY_URL}/${NAMESPACE}/weishi-admin:${VERSION:-latest}"
    )
    
    # 拉取镜像
    for image in "${IMAGES[@]}"; do
        print_info "拉取镜像: $image"
        docker pull "$image"
    done
    
    print_success "镜像拉取完成"
}

# 备份当前部署
backup_current() {
    if [ "$SKIP_BACKUP" = "true" ]; then
        print_warning "跳过备份"
        return 0
    fi
    
    print_info "备份当前部署..."
    
    # 创建备份目录
    BACKUP_TIMESTAMP=$(date +%Y%m%d_%H%M%S)
    CURRENT_BACKUP_DIR="$BACKUP_DIR/backup_$BACKUP_TIMESTAMP"
    mkdir -p "$CURRENT_BACKUP_DIR"
    
    # 备份数据库
    if docker ps | grep -q "weishi-mysql-prod"; then
        print_info "备份数据库..."
        docker exec weishi-mysql-prod mysqldump -u root -p"$MYSQL_ROOT_PASSWORD" --all-databases > "$CURRENT_BACKUP_DIR/database.sql"
        print_success "数据库备份完成"
    fi
    
    # 备份配置文件
    cp -r . "$CURRENT_BACKUP_DIR/config"
    
    # 备份Docker volumes
    if docker volume ls | grep -q "weishi"; then
        print_info "备份Docker volumes..."
        docker run --rm -v weishi_mysql_data:/data -v "$CURRENT_BACKUP_DIR":/backup alpine tar czf /backup/mysql_data.tar.gz -C /data .
        docker run --rm -v weishi_server_uploads:/data -v "$CURRENT_BACKUP_DIR":/backup alpine tar czf /backup/server_uploads.tar.gz -C /data .
    fi
    
    print_success "备份完成: $CURRENT_BACKUP_DIR"
}

# 停止现有服务
stop_services() {
    print_info "停止现有服务..."
    
    if [ -f "$COMPOSE_FILE" ]; then
        docker-compose -f "$COMPOSE_FILE" --env-file="$ENV_FILE" down
        print_success "服务已停止"
    else
        print_warning "Docker Compose文件不存在，跳过停止服务"
    fi
}

# 清理旧资源
cleanup_old() {
    if [ "$SKIP_CLEANUP" = "true" ]; then
        print_warning "跳过清理"
        return 0
    fi
    
    print_info "清理旧资源..."
    
    # 清理未使用的镜像
    docker image prune -f
    
    # 清理未使用的容器
    docker container prune -f
    
    # 清理未使用的网络
    docker network prune -f
    
    print_success "清理完成"
}

# 启动服务
start_services() {
    print_info "启动服务..."
    
    # 构建并启动服务
    docker-compose -f "$COMPOSE_FILE" --env-file="$ENV_FILE" up -d
    
    print_success "服务启动完成"
}

# 等待服务就绪
wait_for_services() {
    print_info "等待服务就绪..."
    
    # 等待MySQL就绪
    print_info "等待MySQL服务..."
    timeout=300
    while [ $timeout -gt 0 ]; do
        if docker exec weishi-mysql-prod mysqladmin ping -h localhost -u root -p"$MYSQL_ROOT_PASSWORD" &>/dev/null; then
            print_success "MySQL服务就绪"
            break
        fi
        sleep 5
        timeout=$((timeout-5))
    done
    
    if [ $timeout -le 0 ]; then
        print_error "MySQL服务启动超时"
        exit 1
    fi
    
    # 等待后端API就绪
    print_info "等待后端API服务..."
    timeout=180
    while [ $timeout -gt 0 ]; do
        if curl -f http://localhost:3001/api/health &>/dev/null; then
            print_success "后端API服务就绪"
            break
        fi
        sleep 5
        timeout=$((timeout-5))
    done
    
    if [ $timeout -le 0 ]; then
        print_error "后端API服务启动超时"
        exit 1
    fi
    
    # 等待前端服务就绪
    print_info "等待前端服务..."
    timeout=180
    while [ $timeout -gt 0 ]; do
        if curl -f http://localhost:3000/api/health &>/dev/null; then
            print_success "前端服务就绪"
            break
        fi
        sleep 5
        timeout=$((timeout-5))
    done
    
    if [ $timeout -le 0 ]; then
        print_error "前端服务启动超时"
        exit 1
    fi
    
    print_success "所有服务已就绪"
}

# 健康检查
health_check() {
    print_info "执行健康检查..."
    
    # 检查容器状态
    print_info "检查容器状态..."
    docker-compose -f "$COMPOSE_FILE" --env-file="$ENV_FILE" ps
    
    # 检查服务健康状态
    services=("mysql" "server" "web" "admin" "caddy")
    for service in "${services[@]}"; do
        container_name="weishi-${service}-prod"
        if docker ps --format "table {{.Names}}\t{{.Status}}" | grep -q "$container_name"; then
            status=$(docker ps --format "table {{.Names}}\t{{.Status}}" | grep "$container_name" | awk '{print $2}')
            if [[ $status == *"healthy"* ]] || [[ $status == *"Up"* ]]; then
                print_success "$service 服务健康"
            else
                print_warning "$service 服务状态异常: $status"
            fi
        else
            print_error "$service 服务未运行"
        fi
    done
    
    # 检查端口监听
    print_info "检查端口监听..."
    ports=("80:Caddy" "443:Caddy HTTPS" "3000:Web" "3001:API" "3306:MySQL" "8080:Admin")
    for port_info in "${ports[@]}"; do
        port=$(echo "$port_info" | cut -d: -f1)
        service=$(echo "$port_info" | cut -d: -f2)
        if ss -tlnp | grep -q ":$port " || netstat -tlnp 2>/dev/null | grep -q ":$port "; then
            print_success "$service 端口 $port 正常监听"
        else
            print_warning "$service 端口 $port 未监听"
        fi
    done
}

# 显示部署信息
show_deployment_info() {
    print_success "部署完成！"
    echo
    print_info "=== 服务访问地址 ==="
    print_info "网站首页: https://${DOMAIN:-localhost}"
    print_info "管理后台: https://${DOMAIN:-localhost}/admin"
    print_info "API文档: https://${DOMAIN:-localhost}/api/docs"
    echo
    print_info "=== 直接访问地址（调试用）==="
    print_info "前端服务: http://localhost:3000"
    print_info "后端API: http://localhost:3001"
    print_info "管理后台: http://localhost:8080"
    print_info "数据库: localhost:3306"
    echo
    print_info "=== 管理命令 ==="
    print_info "查看日志: docker-compose -f $COMPOSE_FILE logs -f [service]"
    print_info "重启服务: docker-compose -f $COMPOSE_FILE restart [service]"
    print_info "停止服务: docker-compose -f $COMPOSE_FILE down"
    print_info "查看状态: docker-compose -f $COMPOSE_FILE ps"
    echo
    print_info "=== 重要提醒 ==="
    print_warning "1. 请确保域名DNS已正确解析到服务器IP"
    print_warning "2. 请检查防火墙设置，确保80和443端口开放"
    print_warning "3. 首次登录管理后台请修改默认密码"
    print_warning "4. 建议配置SSL证书和定期备份"
}

# 主函数
main() {
    print_info "开始部署蔚之领域智能科技项目..."
    echo
    
    check_requirements
    load_environment
    login_registry
    pull_images
    backup_current
    stop_services
    cleanup_old
    start_services
    wait_for_services
    health_check
    show_deployment_info
}

# 显示帮助信息
show_help() {
    echo "蔚之领域智能科技 - 阿里云一键部署脚本"
    echo
    echo "用法: $0 [选项]"
    echo
    echo "选项:"
    echo "  -h, --help          显示帮助信息"
    echo "  --skip-backup       跳过备份"
    echo "  --skip-cleanup      跳过清理"
    echo "  --env-file FILE     指定环境变量文件 (默认: production.env)"
    echo "  --compose-file FILE 指定Docker Compose文件 (默认: docker-compose.prod.yml)"
    echo
    echo "环境变量:"
    echo "  REGISTRY_USERNAME   镜像仓库用户名"
    echo "  REGISTRY_PASSWORD   镜像仓库密码"
    echo "  SKIP_BACKUP         跳过备份 (true/false)"
    echo "  SKIP_CLEANUP        跳过清理 (true/false)"
    echo
    echo "示例:"
    echo "  $0                  # 标准部署"
    echo "  $0 --skip-backup    # 跳过备份的快速部署"
    echo "  $0 --env-file prod.env  # 使用自定义环境文件"
}

# 解析命令行参数
SKIP_BACKUP=false
SKIP_CLEANUP=false

while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        --skip-backup)
            SKIP_BACKUP=true
            shift
            ;;
        --skip-cleanup)
            SKIP_CLEANUP=true
            shift
            ;;
        --env-file)
            ENV_FILE="$2"
            shift 2
            ;;
        --compose-file)
            COMPOSE_FILE="$2"
            shift 2
            ;;
        *)
            print_error "未知选项: $1"
            show_help
            exit 1
            ;;
    esac
done

# 切换到部署目录
cd "$DEPLOY_DIR"

# 执行主函数
main
