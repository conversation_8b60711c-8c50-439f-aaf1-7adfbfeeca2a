# 蔚之领域智能科技 - 部署检查清单

## 📋 部署前准备

### 服务器准备
- [ ] 购买阿里云ECS实例（推荐2核4GB或以上）
- [ ] 配置安全组，开放22、80、443端口
- [ ] 获取服务器公网IP地址
- [ ] 确保服务器可以正常SSH连接

### 域名配置
- [ ] 购买域名并完成实名认证
- [ ] 配置DNS解析，添加A记录指向服务器IP
- [ ] 验证域名解析是否生效 (`nslookup your-domain.com`)

### 阿里云服务
- [ ] 开通容器镜像服务
- [ ] 创建命名空间 `weishi`
- [ ] 获取镜像仓库访问凭证
- [ ] （可选）开通OSS对象存储服务

## 🚀 部署过程

### 1. 环境初始化
- [ ] 连接到服务器 (`ssh root@your-server-ip`)
- [ ] 下载部署文件到 `/opt/weishi`
- [ ] 运行服务器初始化脚本 (`./deployment/scripts/setup-server.sh`)
- [ ] 验证Docker和Docker Compose安装成功

### 2. 配置文件设置
- [ ] 复制环境变量模板文件
- [ ] 配置 `deployment/production.env`
  - [ ] 设置域名 (`DOMAIN`)
  - [ ] 设置数据库密码 (`MYSQL_ROOT_PASSWORD`, `MYSQL_PASSWORD`)
  - [ ] 设置JWT密钥 (`JWT_SECRET`)
  - [ ] 设置管理员密码 (`ADMIN_PASSWORD`)
  - [ ] （可选）配置COS/OSS存储
- [ ] 配置 `deployment/aliyun-registry.env`
  - [ ] 设置镜像仓库用户名和密码

### 3. 镜像构建和推送
- [ ] 在本地构建Docker镜像
- [ ] 推送镜像到阿里云容器镜像服务
- [ ] 验证镜像推送成功

### 4. 服务部署
- [ ] 运行部署脚本 (`./deployment/scripts/deploy.sh`)
- [ ] 等待所有服务启动完成
- [ ] 检查容器状态 (`docker ps`)
- [ ] 验证服务健康检查通过

## ✅ 部署后验证

### 服务可用性检查
- [ ] 访问网站首页 (`https://your-domain.com`)
- [ ] 访问管理后台 (`https://your-domain.com/admin`)
- [ ] 测试API接口 (`https://your-domain.com/api/health`)
- [ ] 检查SSL证书是否正常

### 功能测试
- [ ] 管理员登录测试
- [ ] 文件上传功能测试
- [ ] 数据库连接测试
- [ ] 前后端数据交互测试

### 性能和监控
- [ ] 运行监控脚本 (`./deployment/scripts/monitor.sh`)
- [ ] 检查系统资源使用情况
- [ ] 验证日志记录正常
- [ ] 测试备份功能

## 🔒 安全配置

### 服务器安全
- [ ] 修改SSH默认端口
- [ ] 禁用密码登录，使用密钥认证
- [ ] 配置防火墙规则
- [ ] 设置fail2ban防暴力破解

### 应用安全
- [ ] 修改所有默认密码
- [ ] 配置强密码策略
- [ ] 启用HTTPS强制跳转
- [ ] 配置安全头信息

### 数据安全
- [ ] 配置数据库访问权限
- [ ] 设置定时备份任务
- [ ] 测试数据恢复流程
- [ ] 配置备份文件加密

## 📊 监控和维护

### 监控配置
- [ ] 配置系统监控
- [ ] 设置告警邮箱或Webhook
- [ ] 配置日志轮转
- [ ] 设置磁盘空间监控

### 定时任务
- [ ] 配置自动备份 (crontab)
- [ ] 配置日志清理任务
- [ ] 配置系统更新检查
- [ ] 配置SSL证书续期检查

### 文档和记录
- [ ] 记录服务器配置信息
- [ ] 记录重要密码和密钥
- [ ] 创建运维文档
- [ ] 建立变更记录

## 🔧 故障排除

### 常见问题检查
- [ ] 域名解析是否正确
- [ ] 防火墙端口是否开放
- [ ] Docker服务是否正常运行
- [ ] 容器日志是否有错误信息

### 性能优化
- [ ] 检查服务器资源使用情况
- [ ] 优化数据库配置
- [ ] 配置CDN加速（可选）
- [ ] 优化镜像大小

## 📝 部署完成确认

### 最终检查
- [ ] 所有服务正常运行
- [ ] 网站可以正常访问
- [ ] 管理后台功能正常
- [ ] 监控和告警配置完成
- [ ] 备份策略已实施
- [ ] 安全配置已完成

### 交付文档
- [ ] 提供服务器访问信息
- [ ] 提供管理员账号信息
- [ ] 提供运维操作手册
- [ ] 提供故障排除指南

## 📞 支持联系

如果在部署过程中遇到问题，请按以下顺序排查：

1. **查看日志**: 检查容器日志和系统日志
2. **检查配置**: 验证环境变量和配置文件
3. **网络检查**: 确认域名解析和端口开放
4. **资源检查**: 确认服务器资源充足
5. **联系支持**: 如果问题仍未解决，请联系技术支持

---

## 📋 快速命令参考

```bash
# 服务管理
docker-compose -f deployment/docker-compose.prod.yml ps          # 查看服务状态
docker-compose -f deployment/docker-compose.prod.yml logs -f     # 查看日志
docker-compose -f deployment/docker-compose.prod.yml restart     # 重启服务

# 监控和维护
./deployment/scripts/monitor.sh                                   # 监控检查
./deployment/scripts/backup.sh                                    # 数据备份
./deployment/scripts/deploy.sh                                    # 重新部署

# 系统检查
htop                                                              # 系统资源
df -h                                                             # 磁盘空间
docker stats                                                      # 容器资源
```

---

**注意**: 请在生产环境部署前仔细检查所有配置项，确保安全性和稳定性。
