# 阿里云容器镜像服务配置
# 复制此文件为 aliyun-registry.env 并填写实际配置值

# 阿里云容器镜像服务配置
# 获取方式：登录阿里云控制台 -> 容器镜像服务 -> 访问凭证
REGISTRY_URL=registry.cn-hangzhou.aliyuncs.com
REGISTRY_USERNAME=your_aliyun_username
REGISTRY_PASSWORD=your_aliyun_password

# 镜像仓库配置
NAMESPACE=weizhi

# 服务到仓库的映射关系：
# server-go -> weizhi_server
# web -> weizhi_web
# admin -> weizhi_admin_caddy

# 构建配置
VERSION=latest
CLEANUP_LOCAL=false

# 阿里云地域配置
# 可选地域：
# - cn-hangzhou (华东1-杭州)
# - cn-shanghai (华东2-上海) 
# - cn-beijing (华北2-北京)
# - cn-shenzhen (华南1-深圳)
# - cn-hongkong (香港)
ALIYUN_REGION=cn-hangzhou

# Kubernetes集群配置
K8S_CLUSTER_ID=your_cluster_id
K8S_NAMESPACE=weizhi-prod

# 部署配置
DEPLOY_ENV=production
DOMAIN=your-domain.com

# 数据库配置（如果使用RDS）
RDS_ENDPOINT=your-rds-endpoint.mysql.rds.aliyuncs.com
RDS_PORT=3306
RDS_DATABASE=weizhi
RDS_USERNAME=weizhi
RDS_PASSWORD=your_rds_password

# 对象存储配置（OSS）
OSS_ENDPOINT=oss-cn-hangzhou.aliyuncs.com
OSS_BUCKET=your-oss-bucket
OSS_ACCESS_KEY_ID=your_oss_access_key_id
OSS_ACCESS_KEY_SECRET=your_oss_access_key_secret

# 负载均衡配置
SLB_ID=your_slb_id
SLB_LISTENER_PORT=80
SLB_LISTENER_HTTPS_PORT=443

# 监控配置
ENABLE_MONITORING=true
LOG_PROJECT=weizhi-logs
LOG_STORE=application-logs

# 安全配置
ENABLE_WAF=true
SECURITY_GROUP_ID=your_security_group_id

# 备份配置
BACKUP_ENABLED=true
BACKUP_RETENTION_DAYS=30

# 注意事项：
# 1. 请勿将此文件提交到版本控制系统
# 2. 生产环境请使用强密码
# 3. 定期轮换访问密钥
# 4. 限制镜像仓库的访问权限
# 5. 启用镜像安全扫描
