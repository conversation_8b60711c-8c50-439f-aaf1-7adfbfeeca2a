#!/bin/bash

# 配置生成脚本
# 用于根据环境和变量生成最终的配置文件

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示使用说明
show_usage() {
    echo "使用方法: $0 <environment> [options]"
    echo ""
    echo "参数:"
    echo "  environment    部署环境 (production)"
    echo ""
    echo "选项:"
    echo "  -o, --output   输出文件路径 (默认: .env)"
    echo "  -v, --validate 验证配置文件"
    echo "  -h, --help     显示帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 production -o production.env"
    echo "  $0 production -v"
}

# 验证环境参数
validate_environment() {
    local env=$1
    if [[ "$env" != "production" ]]; then
        log_error "无效的环境: $env"
        log_error "支持的环境: production"
        exit 1
    fi
}

# 检查必需的环境变量
check_required_vars() {
    local env=$1
    local missing_vars=()

    # 通用必需变量
    local common_vars=(
        "REGISTRY_URL"
        "NAMESPACE"
        "MYSQL_ROOT_PASSWORD"
        "MYSQL_PASSWORD"
        "JWT_SECRET"
        "ADMIN_PASSWORD"
    )

    # 生产环境必需变量
    local prod_vars=(
        "PROD_DOMAIN"
        "SSL_EMAIL"
        "MYSQL_DATABASE"
        "MYSQL_USER"
    )

    # 检查通用变量
    for var in "${common_vars[@]}"; do
        if [[ -z "${!var}" ]]; then
            missing_vars+=("$var")
        fi
    done

    # 检查生产环境变量
    for var in "${prod_vars[@]}"; do
        if [[ -z "${!var}" ]]; then
            missing_vars+=("$var")
        fi
    done

    # 报告缺失的变量
    if [[ ${#missing_vars[@]} -gt 0 ]]; then
        log_error "缺失必需的环境变量:"
        for var in "${missing_vars[@]}"; do
            echo "  - $var"
        done
        return 1
    fi

    return 0
}

# 验证环境参数
validate_environment() {
    local env=$1
    if [[ "$env" != "production" && "$env" != "staging" ]]; then
        log_error "无效的环境: $env"
        log_error "支持的环境: production, staging"
        exit 1
    fi
}

# 检查必需的环境变量
check_required_vars() {
    local env=$1
    local missing_vars=()
    
    # 通用必需变量
    local common_vars=(
        "REGISTRY_URL"
        "NAMESPACE"
        "MYSQL_ROOT_PASSWORD"
        "MYSQL_PASSWORD"
        "JWT_SECRET"
        "ADMIN_PASSWORD"
    )
    
    # 生产环境必需变量
    local prod_vars=(
        "PROD_DOMAIN"
        "SSL_EMAIL"
    )
    
    # 测试环境必需变量
    local staging_vars=(
        "STAGING_DOMAIN"
    )
    
    # 检查通用变量
    for var in "${common_vars[@]}"; do
        if [[ -z "${!var}" ]]; then
            missing_vars+=("$var")
        fi
    done
    
    # 检查环境特定变量
    if [[ "$env" == "production" ]]; then
        for var in "${prod_vars[@]}"; do
            if [[ -z "${!var}" ]]; then
                missing_vars+=("$var")
            fi
        done
    elif [[ "$env" == "staging" ]]; then
        for var in "${staging_vars[@]}"; do
            if [[ -z "${!var}" ]]; then
                missing_vars+=("$var")
            fi
        done
    fi
    
    # 报告缺失的变量
    if [[ ${#missing_vars[@]} -gt 0 ]]; then
        log_error "缺失必需的环境变量:"
        for var in "${missing_vars[@]}"; do
            log_error "  - $var"
        done
        return 1
    fi
    
    return 0
}

# 替换模板变量
replace_template_vars() {
    local template_file=$1
    local output_file=$2
    
    log_info "处理模板文件: $template_file"
    
    # 复制模板文件
    cp "$template_file" "$output_file"
    
    # 获取所有环境变量
    local env_vars=$(env | grep -E '^[A-Z_]+=')
    
    # 替换模板中的变量
    while IFS='=' read -r var_name var_value; do
        # 跳过空值
        if [[ -n "$var_value" ]]; then
            # 转义特殊字符
            local escaped_value=$(printf '%s\n' "$var_value" | sed 's/[[\.*^$()+?{|]/\\&/g')
            # 替换变量
            sed -i.bak "s/{{${var_name}}}/${escaped_value}/g" "$output_file"
            sed -i.bak "s/{{${var_name}:-[^}]*}}/${escaped_value}/g" "$output_file"
        fi
    done <<< "$env_vars"
    
    # 处理默认值
    sed -i.bak 's/{{[^:}]*:-\([^}]*\)}}/\1/g' "$output_file"
    
    # 清理未替换的变量（设为空）
    sed -i.bak 's/{{[^}]*}}//g' "$output_file"
    
    # 删除备份文件
    rm -f "${output_file}.bak"
}

# 合并配置文件
merge_configs() {
    local env=$1
    local output_file=$2
    local temp_dir=$(mktemp -d)
    
    log_info "开始生成 $env 环境配置..."
    
    # 处理基础配置
    local base_config="$temp_dir/base.env"
    replace_template_vars "config-templates/base.env" "$base_config"
    
    # 处理环境特定配置
    local env_config="$temp_dir/${env}.env"
    replace_template_vars "config-templates/${env}.env" "$env_config"
    
    # 合并配置文件
    log_info "合并配置文件..."
    {
        echo "# 蔚之领域智能科技 - $env 环境配置"
        echo "# 生成时间: $(date)"
        echo "# 此文件由脚本自动生成，请勿手动编辑"
        echo ""
        cat "$base_config"
        echo ""
        echo "# ================================"
        echo "# $env 环境特定配置"
        echo "# ================================"
        cat "$env_config"
    } > "$output_file"
    
    # 清理临时文件
    rm -rf "$temp_dir"
    
    log_success "配置文件已生成: $output_file"
}

# 验证配置文件
validate_config() {
    local config_file=$1
    
    log_info "验证配置文件: $config_file"
    
    if [[ ! -f "$config_file" ]]; then
        log_error "配置文件不存在: $config_file"
        return 1
    fi
    
    # 检查是否有未替换的模板变量
    local unresolved=$(grep -o '{{[^}]*}}' "$config_file" || true)
    if [[ -n "$unresolved" ]]; then
        log_warning "发现未解析的模板变量:"
        echo "$unresolved" | sort | uniq | while read -r var; do
            log_warning "  - $var"
        done
    fi
    
    # 检查是否有空值的重要配置
    local important_vars=("MYSQL_ROOT_PASSWORD" "JWT_SECRET" "ADMIN_PASSWORD")
    for var in "${important_vars[@]}"; do
        local value=$(grep "^${var}=" "$config_file" | cut -d'=' -f2- || true)
        if [[ -z "$value" || "$value" == "{{*}}" ]]; then
            log_error "重要配置项 $var 为空或未设置"
            return 1
        fi
    done
    
    log_success "配置文件验证通过"
    return 0
}

# 主函数
main() {
    local environment=""
    local output_file=".env"
    local validate_only=false
    
    # 解析参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -o|--output)
                output_file="$2"
                shift 2
                ;;
            -v|--validate)
                validate_only=true
                shift
                ;;
            -h|--help)
                show_usage
                exit 0
                ;;
            -*)
                log_error "未知选项: $1"
                show_usage
                exit 1
                ;;
            *)
                if [[ -z "$environment" ]]; then
                    environment="$1"
                else
                    log_error "多余的参数: $1"
                    show_usage
                    exit 1
                fi
                shift
                ;;
        esac
    done
    
    # 检查必需参数
    if [[ -z "$environment" ]]; then
        log_error "缺少环境参数"
        show_usage
        exit 1
    fi
    
    # 验证环境
    validate_environment "$environment"
    
    # 切换到脚本目录
    cd "$(dirname "$0")/.."
    
    # 如果只是验证现有配置
    if [[ "$validate_only" == "true" ]]; then
        validate_config "$output_file"
        exit $?
    fi
    
    # 检查必需的环境变量
    if ! check_required_vars "$environment"; then
        log_error "请设置所有必需的环境变量"
        exit 1
    fi
    
    # 生成配置文件
    merge_configs "$environment" "$output_file"
    
    # 验证生成的配置
    if validate_config "$output_file"; then
        log_success "配置生成完成: $output_file"
        log_info "可以使用以下命令启动服务:"
        log_info "  docker-compose -f docker-compose.${environment}.yml --env-file $output_file up -d"
    else
        log_error "配置验证失败"
        exit 1
    fi
}

# 执行主函数
main "$@"
