#!/bin/bash

# 阿里云容器镜像构建和推送脚本
# 用于构建所有服务的Docker镜像并推送到阿里云容器镜像服务

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 配置变量
REGISTRY_URL="${REGISTRY_URL:-registry.cn-hangzhou.aliyuncs.com}"
NAMESPACE="${NAMESPACE:-vest}"
VERSION="${VERSION:-$(date +%Y%m%d-%H%M%S)}"

# 服务列表和对应的仓库名称
SERVICES=("server-go" "web" "caddy-admin")

# 获取服务对应的仓库名称
get_repo_name() {
    local service=$1
    case $service in
        "server-go")
            echo "weizhi_server"
            ;;
        "web")
            echo "weizhi_web"
            ;;
        "caddy-admin")
            echo "weizhi_admin_caddy"
            ;;
        *)
            echo "unknown"
            ;;
    esac
}

# 显示配置信息
print_info "=== 阿里云容器镜像构建配置 ==="
print_info "镜像仓库: ${REGISTRY_URL}"
print_info "命名空间: ${NAMESPACE}"
print_info "版本标签: ${VERSION}"
print_info "项目名称: ${PROJECT_NAME}"
print_info "服务列表: ${SERVICES[*]}"
echo

# 检查必要工具
check_requirements() {
    print_info "检查必要工具..."
    
    if ! command -v docker &> /dev/null; then
        print_error "Docker未安装，请先安装Docker"
        exit 1
    fi
    
    print_success "Docker已安装: $(docker --version)"
}

# 登录阿里云容器镜像服务
login_registry() {
    print_info "登录阿里云容器镜像服务..."
    
    if [ -z "$REGISTRY_USERNAME" ] || [ -z "$REGISTRY_PASSWORD" ]; then
        print_warning "未设置REGISTRY_USERNAME或REGISTRY_PASSWORD环境变量"
        print_info "请手动登录: docker login ${REGISTRY_URL}"
        read -p "按回车键继续..."
    else
        echo "$REGISTRY_PASSWORD" | docker login "$REGISTRY_URL" -u "$REGISTRY_USERNAME" --password-stdin
        print_success "登录成功"
    fi
}

# 构建镜像
build_image() {
    local service=$1
    local dockerfile="deployment/Dockerfile.${service}"
    local repo_name=$(get_repo_name "$service")
    local image_name="${REGISTRY_URL}/${NAMESPACE}/${repo_name}"

    print_info "构建 ${service} 镜像..."

    if [ ! -f "$dockerfile" ]; then
        print_error "Dockerfile不存在: $dockerfile"
        return 1
    fi

    # 设置构建参数
    local build_args=""
    case $service in
        "server-go")
            build_args="--build-arg VERSION=${VERSION} --build-arg BUILD_TIME=$(date -u +%Y-%m-%dT%H:%M:%SZ)"
            ;;
        "web")
            build_args="--build-arg NODE_ENV=production --build-arg NITRO_PRESET=node-server"
            ;;
        "admin")
            build_args="--build-arg NODE_ENV=production --build-arg VITE_API_BASE_URL=/api"
            ;;
    esac

    # 根据服务类型设置target
    local target_stage=""
    case $service in
        "server-go"|"web")
            target_stage="--target runner"
            ;;
        "admin")
            # admin使用默认的最后阶段，不需要指定target
            target_stage=""
            ;;
    esac

    # 构建镜像（启用BuildKit和缓存，强制AMD64架构）
    DOCKER_BUILDKIT=1 docker build \
        --platform linux/amd64 \
        -f "$dockerfile" \
        $build_args \
        $target_stage \
        --cache-from "${image_name}:latest" \
        --cache-from "${image_name}:cache" \
        -t "${image_name}:${VERSION}" \
        -t "${image_name}:latest" \
        --progress=plain \
        .

    # 验证镜像
    if docker inspect "${image_name}:${VERSION}" >/dev/null 2>&1; then
        print_success "${service} 镜像构建完成"

        # 显示镜像信息
        local image_size=$(docker images "${image_name}:${VERSION}" --format "table {{.Size}}" | tail -n 1)
        print_info "镜像大小: $image_size"

        return 0
    else
        print_error "${service} 镜像构建失败"
        return 1
    fi
}

# 推送镜像
push_image() {
    local service=$1
    local repo_name=$(get_repo_name "$service")
    local image_name="${REGISTRY_URL}/${NAMESPACE}/${repo_name}"
    
    print_info "推送 ${service} 镜像..."
    
    # 推送版本标签
    docker push "${image_name}:${VERSION}"
    print_success "推送 ${image_name}:${VERSION} 完成"
    
    # 推送latest标签
    docker push "${image_name}:latest"
    print_success "推送 ${image_name}:latest 完成"
}

# 清理本地镜像
cleanup_local_images() {
    if [ "$CLEANUP_LOCAL" = "true" ]; then
        print_info "清理本地镜像..."
        for service in "${SERVICES[@]}"; do
            local repo_name=$(get_repo_name "$service")
            local image_name="${REGISTRY_URL}/${NAMESPACE}/${repo_name}"
            docker rmi "${image_name}:${VERSION}" "${image_name}:latest" 2>/dev/null || true
        done
        print_success "本地镜像清理完成"
    fi
}

# 显示镜像信息
show_image_info() {
    print_info "=== 构建的镜像信息 ==="
    for service in "${SERVICES[@]}"; do
        local repo_name=$(get_repo_name "$service")
        local image_name="${REGISTRY_URL}/${NAMESPACE}/${repo_name}"
        echo "  ${service} -> ${repo_name}:"
        echo "    - ${image_name}:${VERSION}"
        echo "    - ${image_name}:latest"
    done
    echo
}

# 并行构建镜像
parallel_build() {
    print_info "开始并行构建镜像..."

    local pids=()
    local failed_services=()

    # 启动并行构建
    for service in "${SERVICES[@]}"; do
        (
            if build_image "$service"; then
                echo "SUCCESS:$service" > "/tmp/build_result_$service"
            else
                echo "FAILED:$service" > "/tmp/build_result_$service"
            fi
        ) &
        pids+=($!)
    done

    # 等待所有构建完成
    for pid in "${pids[@]}"; do
        wait $pid
    done

    # 检查构建结果
    for service in "${SERVICES[@]}"; do
        if [ -f "/tmp/build_result_$service" ]; then
            result=$(cat "/tmp/build_result_$service")
            if [[ $result == "FAILED:$service" ]]; then
                failed_services+=("$service")
            fi
            rm -f "/tmp/build_result_$service"
        else
            failed_services+=("$service")
        fi
    done

    if [ ${#failed_services[@]} -gt 0 ]; then
        print_error "以下服务构建失败: ${failed_services[*]}"
        return 1
    fi

    print_success "所有镜像并行构建完成"
    return 0
}

# 优化Docker构建缓存
optimize_build_cache() {
    print_info "优化Docker构建缓存..."

    # 启用BuildKit
    export DOCKER_BUILDKIT=1
    export BUILDKIT_PROGRESS=plain

    # 清理悬空镜像
    docker image prune -f >/dev/null 2>&1 || true

    # 拉取缓存镜像
    for service in "${SERVICES[@]}"; do
        local repo_name=$(get_repo_name "$service")
        local image_name="${REGISTRY_URL}/${NAMESPACE}/${repo_name}"
        print_info "拉取 ${service} 缓存镜像..."
        docker pull "${image_name}:latest" >/dev/null 2>&1 || true
        docker pull "${image_name}:cache" >/dev/null 2>&1 || true
    done

    print_success "构建缓存优化完成"
}

# 主函数
main() {
    print_info "开始构建和推送Docker镜像..."
    echo

    # 检查环境
    check_requirements

    # 登录镜像仓库
    login_registry

    # 优化构建缓存
    optimize_build_cache

    # 选择构建模式
    if [ "$PARALLEL_BUILD" = "true" ]; then
        if ! parallel_build; then
            print_error "并行构建失败"
            exit 1
        fi
    else
        # 串行构建所有镜像
        for service in "${SERVICES[@]}"; do
            if ! build_image "$service"; then
                print_error "构建 ${service} 镜像失败"
                exit 1
            fi
        done
    fi

    # 推送所有镜像
    for service in "${SERVICES[@]}"; do
        if ! push_image "$service"; then
            print_error "推送 ${service} 镜像失败"
            exit 1
        fi
    done

    # 显示镜像信息
    show_image_info

    # 清理本地镜像
    cleanup_local_images

    print_success "所有镜像构建和推送完成！"
    print_info "可以使用以下命令部署："
    print_info "docker-compose -f deployment/docker-compose.prod.yml up -d"
}

# 帮助信息
show_help() {
    echo "阿里云容器镜像构建和推送脚本"
    echo "支持多阶段构建、并行构建和缓存优化"
    echo
    echo "用法: $0 [选项]"
    echo
    echo "选项:"
    echo "  -h, --help              显示帮助信息"
    echo "  -v, --version VERSION   指定版本标签 (默认: 时间戳)"
    echo "  -n, --namespace NS      指定命名空间 (默认: weishi)"
    echo "  -r, --registry URL      指定镜像仓库URL"
    echo "  -c, --cleanup           构建后清理本地镜像"
    echo "  -p, --parallel          启用并行构建"
    echo "  --no-cache              禁用构建缓存"
    echo "  --push-only             只推送镜像，不构建"
    echo "  --build-only            只构建镜像，不推送"

    echo
    echo "环境变量:"
    echo "  REGISTRY_USERNAME       阿里云容器镜像服务用户名"
    echo "  REGISTRY_PASSWORD       阿里云容器镜像服务密码"
    echo "  REGISTRY_URL            镜像仓库URL (默认: registry.cn-hangzhou.aliyuncs.com)"
    echo "  NAMESPACE               命名空间 (默认: weishi)"
    echo "  VERSION                 版本标签"
    echo "  CLEANUP_LOCAL           是否清理本地镜像 (true/false)"
    echo "  PARALLEL_BUILD          是否启用并行构建 (true/false)"
    echo "  NO_CACHE                是否禁用缓存 (true/false)

    echo
    echo "示例:"
    echo "  $0                      # 使用默认配置串行构建"
    echo "  $0 -v v1.0.0           # 指定版本标签"
    echo "  $0 -p                  # 启用并行构建"
    echo "  $0 -c --no-cache       # 禁用缓存并清理本地镜像"
    echo "  $0 --build-only        # 只构建不推送"
    echo "  $0 --push-only         # 只推送不构建"

    echo
    echo "多阶段构建说明:"
    echo "  - Web前端: Node.js构建 -> 精简运行时"
    echo "  - Go后端: Go编译 -> Alpine运行时"
    echo "  - Vue管理后台: Node.js构建 -> Caddy静态服务"
}

# 初始化变量
PARALLEL_BUILD=${PARALLEL_BUILD:-false}
NO_CACHE=${NO_CACHE:-false}
BUILD_ONLY=false
PUSH_ONLY=false


# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -v|--version)
            VERSION="$2"
            shift 2
            ;;
        -n|--namespace)
            NAMESPACE="$2"
            shift 2
            ;;
        -r|--registry)
            REGISTRY_URL="$2"
            shift 2
            ;;
        -c|--cleanup)
            CLEANUP_LOCAL="true"
            shift
            ;;
        -p|--parallel)
            PARALLEL_BUILD="true"
            shift
            ;;
        --no-cache)
            NO_CACHE="true"
            shift
            ;;
        --build-only)
            BUILD_ONLY=true
            shift
            ;;
        --push-only)
            PUSH_ONLY=true
            shift
            ;;

        *)
            print_error "未知选项: $1"
            show_help
            exit 1
            ;;
    esac
done

# 验证参数
if [ "$BUILD_ONLY" = true ] && [ "$PUSH_ONLY" = true ]; then
    print_error "--build-only 和 --push-only 不能同时使用"
    exit 1
fi



# 执行主函数或特定操作
if [ "$BUILD_ONLY" = true ]; then
    print_info "只构建镜像模式..."
    check_requirements
    optimize_build_cache
    if [ "$PARALLEL_BUILD" = "true" ]; then
        parallel_build
    else
        for service in "${SERVICES[@]}"; do
            build_image "$service"
        done
    fi
    show_image_info
elif [ "$PUSH_ONLY" = true ]; then
    print_info "只推送镜像模式..."
    check_requirements
    login_registry
    for service in "${SERVICES[@]}"; do
        push_image "$service"
    done
    show_image_info
else
    main
fi
