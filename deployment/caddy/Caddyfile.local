# 本地开发环境Caddy配置
# 用于本地测试和开发

# 全局配置
{
    # 本地开发，禁用自动HTTPS
    auto_https off
    
    # 本地日志配置
    log {
        output stdout
        level INFO
    }
}

# 本地主域名配置
localhost {
    # 健康检查端点
    handle /health {
        respond "Local Caddy OK" 200
    }
    
    # API代理到后端服务
    handle /api/* {
        reverse_proxy weishi-server-local:3001 {
            # 请求头设置
            header_up Host {host}
            header_up X-Real-IP {remote_host}
            header_up X-Forwarded-For {remote_host}
            header_up X-Forwarded-Proto {scheme}
            header_up X-Forwarded-Host {host}
        }
    }
    
    # 管理后台代理
    handle /admin/* {
        reverse_proxy weishi-admin-local:8080 {
            # 请求头设置
            header_up Host {host}
            header_up X-Real-IP {remote_host}
            header_up X-Forwarded-For {remote_host}
            header_up X-Forwarded-Proto {scheme}
            header_up X-Forwarded-Host {host}
        }
    }
    
    # 上传文件代理
    handle /uploads/* {
        reverse_proxy weishi-server-local:3001 {
            header_up Host {host}
            header_up X-Real-IP {remote_host}
            header_up X-Forwarded-For {remote_host}
            header_up X-Forwarded-Proto {scheme}
        }
    }
    
    # WebSocket支持（如果需要）
    handle /ws/* {
        reverse_proxy weishi-server-local:3001 {
            header_up Host {host}
            header_up X-Real-IP {remote_host}
            header_up X-Forwarded-For {remote_host}
            header_up X-Forwarded-Proto {scheme}
            header_up Connection {>Connection}
            header_up Upgrade {>Upgrade}
        }
    }
    
    # 前端应用（默认处理）
    handle {
        reverse_proxy weishi-web-local:3000 {
            # 请求头设置
            header_up Host {host}
            header_up X-Real-IP {remote_host}
            header_up X-Forwarded-For {remote_host}
            header_up X-Forwarded-Proto {scheme}
            header_up X-Forwarded-Host {host}
        }
    }
    
    # 启用压缩
    encode gzip
    
    # 开发环境安全头（较宽松）
    header {
        X-Content-Type-Options nosniff
        X-Frame-Options SAMEORIGIN
        -Server
    }
}

# 直接访问各个服务的端口配置
:8081 {
    # 直接访问前端服务
    reverse_proxy weishi-web-local:3000
}

:8082 {
    # 直接访问后端API
    reverse_proxy weishi-server-local:3001
}

:8083 {
    # 直接访问管理后台
    reverse_proxy weishi-admin-local:8080
}

# 管理端口
:2019 {
    # Caddy管理接口
    admin localhost:2019
}
