# 蔚之领域智能科技 - 生产环境Caddy配置
# 自动HTTPS、反向代理、静态文件服务

# 全局配置
{
    # 邮箱用于Let's Encrypt证书申请
    email {$SSL_EMAIL:<EMAIL>}

    # Caddy管理接口
    admin localhost:2019

    # 全局日志配置
    log {
        level INFO
        output file /var/log/caddy/access.log {
            roll_size 100mb
            roll_keep 10
            roll_keep_for 720h
        }
        format json
    }
}

# 主域名配置
{$DOMAIN} {
    # 安全头配置
    header {
        # 安全相关头
        Strict-Transport-Security "max-age=31536000; includeSubDomains; preload"
        X-Content-Type-Options "nosniff"
        X-Frame-Options "SAMEORIGIN"
        X-XSS-Protection "1; mode=block"
        Referrer-Policy "strict-origin-when-cross-origin"
        
        # 隐藏服务器信息
        -Server
        -X-Powered-By
        
        # 内容安全策略
        Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self' https:; media-src 'self'; object-src 'none'; frame-src 'none';"
    }
    
    # 健康检查端点
    handle /health {
        respond "OK" 200
    }
    
    # API代理到后端服务
    handle /api/* {
        reverse_proxy server:3001 {
            # 健康检查
            health_uri /api/health
            health_interval 30s
            health_timeout 10s
            
            # 负载均衡（如果有多个后端实例）
            lb_policy round_robin
            
            # 请求头设置
            header_up Host {host}
            header_up X-Real-IP {remote_host}
            header_up X-Forwarded-For {remote_host}
            header_up X-Forwarded-Proto {scheme}
            header_up X-Forwarded-Host {host}
        }
    }
    

    
    # 静态文件服务（上传的文件等）
    handle /static/* {
        root * /opt/weishi/static
        file_server {
            # 静态文件缓存
            precompressed gzip
        }
        
        # 静态资源缓存头
        header {
            Cache-Control "public, max-age=31536000, immutable"
            Vary "Accept-Encoding"
        }
    }
    
    # 上传文件代理
    handle /uploads/* {
        reverse_proxy server:3001 {
            header_up Host {host}
            header_up X-Real-IP {remote_host}
            header_up X-Forwarded-For {remote_host}
            header_up X-Forwarded-Proto {scheme}
        }
    }
    
    # WebSocket支持（如果需要）
    handle /ws/* {
        reverse_proxy server:3001 {
            header_up Host {host}
            header_up X-Real-IP {remote_host}
            header_up X-Forwarded-For {remote_host}
            header_up X-Forwarded-Proto {scheme}
            header_up Connection {>Connection}
            header_up Upgrade {>Upgrade}
        }
    }
    
    # 前端应用（默认处理）
    handle {
        reverse_proxy web:3000 {
            # 健康检查
            health_uri /api/health
            health_interval 30s
            health_timeout 10s
            
            # 请求头设置
            header_up Host {host}
            header_up X-Real-IP {remote_host}
            header_up X-Forwarded-For {remote_host}
            header_up X-Forwarded-Proto {scheme}
            header_up X-Forwarded-Host {host}
        }
    }
    
    # 启用压缩
    encode {
        gzip 6
        zstd
        minimum_length 1024
        match {
            header Content-Type text/*
            header Content-Type application/json*
            header Content-Type application/javascript*
            header Content-Type application/xml*
            header Content-Type application/rss+xml*
            header Content-Type application/atom+xml*
            header Content-Type image/svg+xml*
        }
    }
    
    # 访问日志
    log {
        output file /var/log/caddy/{$DOMAIN}_access.log {
            roll_size 100mb
            roll_keep 30
            roll_keep_for 720h
        }
        format json {
            time_format "2006-01-02T15:04:05.000Z07:00"
            message_key "message"
            level_key "level"
            time_key "timestamp"
        }
    }
}

# www子域名重定向到主域名
www.{$DOMAIN} {
    redir https://{$DOMAIN}{uri} permanent
}

# HTTP重定向到HTTPS（如果需要强制HTTPS）
http://{$DOMAIN} {
    redir https://{$DOMAIN}{uri} permanent
}

http://www.{$DOMAIN} {
    redir https://{$DOMAIN}{uri} permanent
}

# localhost 配置（用于本地测试）
localhost {
    # 前端应用（默认处理）
    handle {
        reverse_proxy web:3000 {
            # 请求头设置
            header_up Host {host}
            header_up X-Real-IP {remote_host}
            header_up X-Forwarded-For {remote_host}
            header_up X-Forwarded-Proto {scheme}
            header_up X-Forwarded-Host {host}
        }
    }

    # API代理到后端服务
    handle /api/* {
        reverse_proxy server:3001 {
            header_up Host {host}
            header_up X-Real-IP {remote_host}
            header_up X-Forwarded-For {remote_host}
            header_up X-Forwarded-Proto {scheme}
            header_up X-Forwarded-Host {host}
        }
    }

    # 启用压缩
    encode gzip
}

# 管理端口（可选，用于内部监控）
:8080 {
    # 限制访问（只允许内网）
    @internal {
        remote_ip 10.0.0.0/8 **********/12 ***********/16 127.0.0.1
    }

    handle @internal {
        # 健康检查
        handle /health {
            respond "Caddy OK" 200
        }

        # 状态信息
        handle /status {
            respond "Status: Running" 200
        }
    }

    # 拒绝外部访问
    handle {
        respond "Access Denied" 403
    }
}

# 管理后台独立域名配置
{$ADMIN_DOMAIN} {
    # 编码配置
    encode gzip

    # 安全头配置
    header {
        # 安全相关头
        Strict-Transport-Security "max-age=31536000; includeSubDomains; preload"
        X-Content-Type-Options "nosniff"
        X-Frame-Options "SAMEORIGIN"  # 管理后台允许同源嵌入
        X-XSS-Protection "1; mode=block"
        Referrer-Policy "strict-origin-when-cross-origin"

        # 隐藏服务器信息
        -Server
        -X-Powered-By

        # 管理后台的内容安全策略（相对宽松）
        Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self' https:; media-src 'self'; object-src 'none'; frame-src 'self';"
    }

    # 健康检查端点
    handle /health {
        respond "Admin OK" 200
    }

    # 管理后台API代理到Go后端
    handle /api/* {
        reverse_proxy server:3001 {
            # 健康检查
            health_uri /api/health
            health_interval 30s
            health_timeout 10s

            # 负载均衡（如果有多个后端实例）
            lb_policy round_robin

            # 请求头设置
            header_up Host {host}
            header_up X-Real-IP {remote_host}
            header_up X-Forwarded-For {remote_host}
            header_up X-Forwarded-Proto {scheme}
            header_up X-Forwarded-Host {host}
        }
    }

    # 管理后台静态文件服务（根路径）
    handle {
        root * /opt/weishi/admin

        # SPA路由支持 - try_files 必须在 file_server 之前
        try_files {path} /index.html

        file_server {
            # 静态文件缓存和压缩
            precompressed gzip
        }

        # 静态资源缓存头
        @static {
            path *.css *.js *.png *.jpg *.jpeg *.gif *.ico *.svg *.woff *.woff2 *.ttf *.eot
        }
        header @static {
            Cache-Control "public, max-age=31536000, immutable"
            Vary "Accept-Encoding"
        }

        # 对于HTML文件，使用较短的缓存时间
        @html {
            path *.html
        }
        header @html {
            Cache-Control "public, max-age=3600"
        }
    }

    # 访问日志
    log {
        output file /var/log/caddy/{$ADMIN_DOMAIN}_access.log {
            roll_size 100mb
            roll_keep 30
            roll_keep_for 720h
        }
        format json {
            time_format "2006-01-02T15:04:05.000Z07:00"
            message_key "message"
            level_key "level"
            time_key "timestamp"
        }
    }
}
