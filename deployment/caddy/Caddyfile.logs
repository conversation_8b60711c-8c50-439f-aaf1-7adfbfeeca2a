# Caddy 配置文件 - 启用访问日志
# 用于生产环境的反向代理和日志记录

{
    # 全局配置
    admin off
    
    # 日志配置
    log {
        output file /var/log/caddy/access.log {
            roll_size 100mb
            roll_keep 10
            roll_keep_for 720h
        }
        format json
        level INFO
    }
    
    # 错误日志
    log error {
        output file /var/log/caddy/error.log {
            roll_size 100mb
            roll_keep 10
            roll_keep_for 720h
        }
        format json
        level ERROR
    }
}

# 主站点配置
{$DOMAIN:localhost} {
    # 访问日志
    log {
        output file /var/log/caddy/access.log {
            roll_size 100mb
            roll_keep 10
            roll_keep_for 720h
        }
        format json {
            time_format "2006-01-02T15:04:05.000Z07:00"
            message_key "msg"
            level_key "level"
            time_key "ts"
            logger_key "logger"
            caller_key "caller"
        }
    }
    
    # 反向代理到前端服务
    reverse_proxy weizhi-web-prod:3000 {
        header_up Host {host}
        header_up X-Real-IP {remote}
        header_up X-Forwarded-For {remote}
        header_up X-Forwarded-Proto {scheme}
        
        # 健康检查
        health_uri /api/health
        health_interval 30s
        health_timeout 10s
    }
    
    # 安全头
    header {
        # 安全相关头部
        X-Frame-Options "SAMEORIGIN"
        X-XSS-Protection "1; mode=block"
        X-Content-Type-Options "nosniff"
        Referrer-Policy "strict-origin-when-cross-origin"
        
        # 隐藏服务器信息
        -Server
    }
    
    # 静态文件缓存
    @static {
        path /_nuxt/* /favicon.ico /robots.txt
    }
    header @static {
        Cache-Control "public, max-age=31536000, immutable"
    }
    
    # API 路由代理到后端
    handle /api/* {
        reverse_proxy weizhi-server-prod:3001 {
            header_up Host {host}
            header_up X-Real-IP {remote}
            header_up X-Forwarded-For {remote}
            header_up X-Forwarded-Proto {scheme}
        }
    }
}

# 管理后台配置
{$ADMIN_DOMAIN:admin.localhost} {
    # 管理后台访问日志
    log {
        output file /var/log/caddy/admin_access.log {
            roll_size 100mb
            roll_keep 10
            roll_keep_for 720h
        }
        format json
    }
    
    # 静态文件服务（管理后台构建文件）
    root * /srv/admin
    file_server
    
    # SPA 路由支持
    try_files {path} /index.html
    
    # 安全头
    header {
        X-Frame-Options "DENY"
        X-XSS-Protection "1; mode=block"
        X-Content-Type-Options "nosniff"
        Referrer-Policy "strict-origin-when-cross-origin"
        -Server
    }
    
    # API 代理到后端
    handle /api/* {
        reverse_proxy weizhi-server-prod:3001 {
            header_up Host {host}
            header_up X-Real-IP {remote}
            header_up X-Forwarded-For {remote}
            header_up X-Forwarded-Proto {scheme}
        }
    }
}

# 健康检查端点
:80/health {
    respond "OK" 200
}
