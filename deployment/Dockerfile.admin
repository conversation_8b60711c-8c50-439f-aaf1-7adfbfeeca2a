# 管理后台构建专用 Dockerfile
# 只用于构建静态文件，不运行服务

# 构建阶段
FROM docker.cnb.cool/yuandongbin/docker-sync/node:22.11.0-alpine AS base

# 设置 Alpine 镜像源为国内源
RUN sed -i 's/dl-cdn.alpinelinux.org/mirrors.aliyun.com/g' /etc/apk/repositories

# 配置 npm 镜像源
RUN npm config set registry https://registry.npmmirror.com

# 安装 pnpm
RUN npm install -g pnpm@latest

# 配置 pnpm 镜像源
RUN pnpm config set registry https://registry.npmmirror.com \
    && pnpm config set enable-pre-post-scripts true \
    && pnpm config set auto-install-peers true

# 安装构建依赖
RUN apk add --no-cache python3 make g++ curl

# 设置工作目录
WORKDIR /app

# 复制依赖配置文件（优化缓存）
COPY admin/package.json ./

# 安装依赖
RUN pnpm install --no-frozen-lockfile

# 构建阶段
FROM base AS builder

# 复制源代码
COPY admin/ .

# 设置构建环境为生产环境
ENV NODE_ENV=production
ENV VITE_API_BASE_URL=/api
ENV VITE_APP_TITLE="蔚之领域智能科技 - 管理系统"
ENV VITE_APP_VERSION=1.0.0
ENV VITE_APP_ENV=production
ENV VITE_DEBUG=false
# 重要：设置正确的 base 路径，确保资源文件路径正确
ENV VITE_BASE=/admin/

# 显示环境变量（调试用）
RUN echo "Environment variables:" && env | grep VITE

# 构建应用（使用生产环境配置）
RUN pnpm build

# 最终阶段 - 只包含构建产物
FROM docker.cnb.cool/yuandongbin/docker-sync/node:22.11.0-alpine

# 创建输出目录
RUN mkdir -p /app/dist

# 复制构建产物
COPY --from=builder /app/dist /app/dist

# 设置工作目录
WORKDIR /app

# 默认命令（在 docker-compose 中会被覆盖）
CMD ["echo", "Build completed"]
