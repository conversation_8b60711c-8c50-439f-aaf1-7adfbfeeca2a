#!/bin/bash

# 蔚之领域数据导入脚本
# 在所有服务启动后执行，导入完整的业务数据
# 支持容器内和容器外执行

set -e

# 环境变量配置
DB_HOST=${DB_HOST:-mysql}
DB_PORT=${DB_PORT:-3306}
DB_USERNAME=${DB_USERNAME:-root}
DB_PASSWORD=${DB_PASSWORD:-${MYSQL_ROOT_PASSWORD}}
DB_DATABASE=${DB_DATABASE:-${MYSQL_DATABASE:-weizhi}}

# Docker Compose 配置
COMPOSE_FILE=${COMPOSE_FILE:-docker-compose.prod.yml}
ENV_FILE=${ENV_FILE:-docker.env}

# 运行模式：container（容器内）或 host（宿主机）
RUN_MODE=${RUN_MODE:-auto}

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 自动检测运行模式
detect_run_mode() {
    if [ "$RUN_MODE" = "auto" ]; then
        if [ -f "/.dockerenv" ] || [ -n "$KUBERNETES_SERVICE_HOST" ]; then
            RUN_MODE="container"
            log_info "检测到容器环境，使用容器模式"
        else
            RUN_MODE="host"
            log_info "检测到宿主机环境，使用宿主机模式"
        fi
    fi
}

# 执行MySQL命令
exec_mysql() {
    local sql="$1"
    local database="${2:-}"

    if [ "$RUN_MODE" = "container" ]; then
        # 容器内直接连接
        if [ -n "$database" ]; then
            mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USERNAME" -p"$DB_PASSWORD" "$database" -e "$sql"
        else
            mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USERNAME" -p"$DB_PASSWORD" -e "$sql"
        fi
    else
        # 宿主机通过docker-compose exec
        if [ -n "$database" ]; then
            docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" exec mysql mysql -u"$DB_USERNAME" -p"$DB_PASSWORD" "$database" -e "$sql"
        else
            docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" exec mysql mysql -u"$DB_USERNAME" -p"$DB_PASSWORD" -e "$sql"
        fi
    fi
}

# 导入SQL文件
import_sql_file() {
    local sql_file="$1"
    local database="${2:-$DB_DATABASE}"

    if [ "$RUN_MODE" = "container" ]; then
        # 容器内直接导入
        mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USERNAME" -p"$DB_PASSWORD" "$database" < "$sql_file"
    else
        # 宿主机通过docker-compose exec
        docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" exec -T mysql mysql -u"$DB_USERNAME" -p"$DB_PASSWORD" "$database" < "$sql_file"
    fi
}

# 检查服务状态
check_services() {
    log_info "检查服务状态..."

    if [ "$RUN_MODE" = "host" ]; then
        # 检查容器是否运行
        if ! docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" ps | grep -q "Up"; then
            log_error "服务未运行，请先启动服务"
            exit 1
        fi
    fi

    # 检查MySQL是否可连接
    if ! exec_mysql "SELECT 1;" >/dev/null 2>&1; then
        log_error "MySQL连接失败，请检查MySQL服务状态"
        log_info "连接参数: $DB_HOST:$DB_PORT, 用户: $DB_USERNAME, 数据库: $DB_DATABASE"
        exit 1
    fi

    # 检查数据库是否存在
    if ! exec_mysql "USE $DB_DATABASE; SELECT 1;" >/dev/null 2>&1; then
        log_error "数据库 $DB_DATABASE 不存在，请检查数据库初始化"
        exit 1
    fi

    log_success "所有服务运行正常"
}

# 检查表结构
check_tables() {
    log_info "检查数据库表结构..."

    TABLES=$(exec_mysql "USE $DB_DATABASE; SHOW TABLES;" 2>/dev/null | wc -l)

    if [ "$TABLES" -lt 5 ]; then
        log_error "数据库表未完全创建（当前: $TABLES 个表），请等待Go应用完全启动"
        if [ "$RUN_MODE" = "host" ]; then
            log_info "您可以运行以下命令检查Go应用日志："
            log_info "docker logs weishi-server-prod"
        fi
        exit 1
    fi

    log_success "数据库表结构正常（共 $TABLES 个表）"
}

# 备份现有数据
backup_data() {
    log_info "备份现有数据..."

    BACKUP_FILE="backup_$(date +%Y%m%d_%H%M%S).sql"

    if [ "$RUN_MODE" = "container" ]; then
        # 容器内备份
        mysqldump -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USERNAME" -p"$DB_PASSWORD" "$DB_DATABASE" > "$BACKUP_FILE" 2>/dev/null
    else
        # 宿主机备份
        docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" exec mysql mysqldump -u"$DB_USERNAME" -p"$DB_PASSWORD" "$DB_DATABASE" > "$BACKUP_FILE" 2>/dev/null
    fi

    if [ $? -eq 0 ]; then
        log_success "数据备份完成: $BACKUP_FILE"
    else
        log_warning "数据备份失败，继续执行导入"
    fi
}

# 导入完整数据
import_data() {
    log_info "开始导入完整数据..."

    # 查找数据文件
    local data_file=""
    if [ "$RUN_MODE" = "container" ]; then
        # 容器内路径
        if [ -f "/app/scripts/database_full.sql" ]; then
            data_file="/app/scripts/database_full.sql"
        elif [ -f "/app/scripts/seed_data.sql" ]; then
            data_file="/app/scripts/seed_data.sql"
        fi
    else
        # 宿主机路径
        if [ -f "server-go/scripts/database_full.sql" ]; then
            data_file="server-go/scripts/database_full.sql"
        elif [ -f "server-go/scripts/seed_data.sql" ]; then
            data_file="server-go/scripts/seed_data.sql"
        fi
    fi

    if [ -z "$data_file" ]; then
        log_error "找不到数据文件，请检查以下路径："
        log_error "  - server-go/scripts/database_full.sql"
        log_error "  - server-go/scripts/seed_data.sql"
        exit 1
    fi

    log_info "使用数据文件: $data_file"
    log_info "正在导入数据，这可能需要几分钟..."

    if import_sql_file "$data_file"; then
        log_success "数据导入完成"
    else
        log_error "数据导入失败"
        exit 1
    fi
}

# 重启服务
restart_services() {
    log_info "重启服务以刷新缓存..."
    
    docker-compose -f docker-compose.prod.yml --env-file docker.env restart server
    
    # 等待服务重启
    sleep 10
    
    log_success "服务重启完成"
}

# 验证导入结果
verify_import() {
    log_info "验证数据导入结果..."
    
    # 检查轮播图数据
    SWIPER_COUNT=$(docker-compose -f docker-compose.prod.yml --env-file docker.env exec mysql mysql -u root -pYdb3344% -e "USE weizhi; SELECT COUNT(*) FROM swipers;" -s -N 2>/dev/null)
    
    # 检查管理员数据
    ADMIN_COUNT=$(docker-compose -f docker-compose.prod.yml --env-file docker.env exec mysql mysql -u root -pYdb3344% -e "USE weizhi; SELECT COUNT(*) FROM admin_users;" -s -N 2>/dev/null)
    
    # 检查服务数据
    SERVICE_COUNT=$(docker-compose -f docker-compose.prod.yml --env-file docker.env exec mysql mysql -u root -pYdb3344% -e "USE weizhi; SELECT COUNT(*) FROM our_services;" -s -N 2>/dev/null)
    
    log_info "数据统计："
    log_info "  轮播图: $SWIPER_COUNT 条"
    log_info "  管理员: $ADMIN_COUNT 个"
    log_info "  服务: $SERVICE_COUNT 个"
    
    if [ "$SWIPER_COUNT" -gt 0 ] && [ "$ADMIN_COUNT" -gt 0 ] && [ "$SERVICE_COUNT" -gt 0 ]; then
        log_success "数据导入验证成功！"
        log_info "您现在可以访问："
        log_info "  前端网站: http://viclink.cn"
        log_info "  管理后台: http://admin.viclink.cn"
        log_info "  默认账号: admin / admin123"
    else
        log_warning "数据导入可能不完整，请检查日志"
    fi
}

# 主函数
main() {
    log_info "=== 蔚之领域数据导入脚本 ==="
    
    # 检查是否在正确的目录
    if [ ! -f "docker-compose.prod.yml" ]; then
        log_error "请在项目根目录运行此脚本"
        exit 1
    fi
    
    # 执行导入流程
    check_services
    check_tables
    backup_data
    import_data
    restart_services
    verify_import
    
    log_success "=== 数据导入完成！ ==="
}

# 执行主函数
main "$@"
