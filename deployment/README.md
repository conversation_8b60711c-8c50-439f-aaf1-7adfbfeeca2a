# 蔚之领域智能科技 - 部署配置

## 项目概述

这是江苏蔚之领域智能科技有限公司的企业级全栈项目的Docker部署配置。

## 📁 目录结构

```
deployment/
├── Dockerfile.*              # Docker镜像构建文件
├── docker-compose.prod.yml  # 生产环境Docker Compose配置
├── production.env.example    # 环境变量模板
├── caddy/                   # Caddy反向代理配置
├── mysql/                   # MySQL配置和初始化脚本
├── scripts/                 # 运维脚本
│   ├── backup.sh            # 数据备份脚本
│   ├── monitor.sh           # 服务监控脚本
│   └── test-mysql-connection.sh  # MySQL连接测试
├── check-services.sh        # 服务健康检查脚本
├── archive/                 # 存档的旧配置文件
└── README.md               # 本文档
```

## 🚀 快速开始

### 1. 环境准备

```bash
# 确保已安装Docker和Docker Compose
docker --version
docker-compose --version

# 复制环境变量配置
cp production.env.example production.env

# 编辑环境变量文件
vim production.env
```

### 2. 配置环境变量

编辑 `production.env` 文件，配置以下关键参数：

```bash
# 镜像仓库配置
REGISTRY_URL=registry.cn-hangzhou.aliyuncs.com
NAMESPACE=weishi
VERSION=latest

# 域名配置
DOMAIN=your-domain.com

# 数据库配置
MYSQL_ROOT_PASSWORD=your-root-password
MYSQL_PASSWORD=your-app-password
MYSQL_DATABASE=weizhi

# JWT安全配置
JWT_SECRET=your-jwt-secret-key

# 管理员账号配置
ADMIN_USERNAME=admin
ADMIN_PASSWORD=your-admin-password
```

### 3. 启动服务

```bash
# 启动所有服务
docker-compose -f docker-compose.prod.yml --env-file production.env up -d

# 查看服务状态
docker-compose -f docker-compose.prod.yml --env-file production.env ps

# 查看日志
docker-compose -f docker-compose.prod.yml --env-file production.env logs -f
```

## 📊 服务说明

### 核心服务

- **weishi-mysql-prod**: MySQL数据库服务
- **weishi-server-prod**: Go后端API服务
- **weishi-web-prod**: Vue3前端网站
- **weishi-admin-prod**: Vue3管理后台
- **weishi-caddy-prod**: Caddy反向代理

### 端口配置

- **80**: HTTP访问（Caddy）
- **443**: HTTPS访问（Caddy）
- **3000**: 前端网站
- **3001**: 后端API
- **3306**: MySQL数据库
- **8080**: 管理后台

## 🔧 运维脚本

### 备份脚本

```bash
# 执行备份
./scripts/backup.sh

# 指定保留天数
./scripts/backup.sh --retention 7
```

### 监控脚本

```bash
# 完整监控检查
./scripts/monitor.sh

# 只检查容器状态
./scripts/monitor.sh --containers

# 只检查服务健康状态
./scripts/monitor.sh --health

# 只检查数据库状态
./scripts/monitor.sh --database

# 只检查资源使用情况
./scripts/monitor.sh --resources
```

### 健康检查

```bash
# 执行完整健康检查
./check-services.sh
```

### MySQL连接测试

```bash
# 测试MySQL连接
./test-mysql-connection.sh
```

## 📝 管理命令

### 服务管理

```bash
# 重启服务
docker-compose -f docker-compose.prod.yml --env-file production.env restart [service]

# 停止服务
docker-compose -f docker-compose.prod.yml --env-file production.env down

# 启动服务
docker-compose -f docker-compose.prod.yml --env-file production.env up -d

# 查看实时日志
docker-compose -f docker-compose.prod.yml --env-file production.env logs -f [service]
```

### 数据库管理

```bash
# 进入MySQL容器
docker-compose -f docker-compose.prod.yml --env-file production.env exec mysql mysql -u root -p

# 备份数据库
docker-compose -f docker-compose.prod.yml --env-file production.env exec mysql mysqldump -u root -p weizhi > backup.sql

# 恢复数据库
docker-compose -f docker-compose.prod.yml --env-file production.env exec -T mysql mysql -u root -p weizhi < backup.sql
```

## 🔍 故障排除

### 常见问题

1. **服务无法启动**
   ```bash
   # 查看详细日志
   docker-compose -f docker-compose.prod.yml --env-file production.env logs
   
   # 检查端口占用
   netstat -tlnp | grep :80
   netstat -tlnp | grep :443
   ```

2. **数据库连接失败**
   ```bash
   # 检查MySQL服务状态
   docker-compose -f docker-compose.prod.yml --env-file production.env logs mysql
   
   # 测试数据库连接
   ./test-mysql-connection.sh
   ```

3. **镜像拉取失败**
   ```bash
   # 手动拉取镜像
   docker pull registry.cn-hangzhou.aliyuncs.com/weishi/weishi-server-go:latest
   docker pull registry.cn-hangzhou.aliyuncs.com/weishi/weishi-web:latest
   docker pull registry.cn-hangzhou.aliyuncs.com/weishi/weishi-admin:latest
   ```

### 日志查看

```bash
# 查看所有服务日志
docker-compose -f docker-compose.prod.yml --env-file production.env logs

# 查看特定服务日志
docker-compose -f docker-compose.prod.yml --env-file production.env logs mysql
docker-compose -f docker-compose.prod.yml --env-file production.env logs server
docker-compose -f docker-compose.prod.yml --env-file production.env logs web
```

## 🔒 安全注意事项

1. **环境变量安全**
   - 不要将敏感信息提交到版本控制
   - 定期更换密码和密钥
   - 使用强密码策略

2. **网络安全**
   - 确保防火墙正确配置
   - 只开放必要的端口
   - 使用HTTPS加密访问

3. **数据安全**
   - 定期备份数据库
   - 设置合适的备份保留期
   - 监控系统日志

## 📈 监控和维护

### 日常维护

```bash
# 检查服务状态
./scripts/monitor.sh

# 执行数据备份
./scripts/backup.sh

# 清理Docker资源
docker system prune -f
```

### 性能监控

- 使用监控脚本检查资源使用情况
- 定期检查磁盘空间
- 监控数据库性能
- 查看应用日志

## 🔄 更新流程

1. 拉取最新镜像
2. 备份当前数据
3. 停止现有服务
4. 启动新版本服务
5. 验证服务状态

## 📞 技术支持

如有问题，请联系技术支持团队。

---

**注意**: 本配置适用于生产环境部署，请确保在部署前充分测试。