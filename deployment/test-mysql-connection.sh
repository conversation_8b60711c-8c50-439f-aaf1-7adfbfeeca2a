#!/bin/bash

# MySQL连接测试脚本
# 用于验证MySQL用户创建和连接是否正常

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# 检查必要的文件
check_requirements() {
    print_info "检查部署环境..."
    
    if [ ! -f "production.env" ]; then
        print_error "production.env 文件不存在"
        exit 1
    fi
    
    if [ ! -f "docker-compose.prod.yml" ]; then
        print_error "docker-compose.prod.yml 文件不存在"
        exit 1
    fi
    
    print_success "环境检查通过"
}

# 加载环境变量
load_environment() {
    print_info "加载环境变量..."
    
    # 加载生产环境变量
    set -a
    source production.env
    set +a
    
    print_success "环境变量已加载"
}

# 测试MySQL连接
test_mysql_connection() {
    print_info "测试MySQL连接..."
    
    # 测试root用户连接
    print_info "测试root用户连接..."
    if docker compose --env-file production.env -f docker-compose.prod.yml exec -T mysql mysql -u root -p"${MYSQL_ROOT_PASSWORD}" -e "SELECT 1;" &>/dev/null; then
        print_success "root用户连接成功"
    else
        print_error "root用户连接失败"
        return 1
    fi
    
    # 测试weizhi用户连接
    print_info "测试weizhi用户连接..."
    if docker compose --env-file production.env -f docker-compose.prod.yml exec -T mysql mysql -u weizhi -p"${MYSQL_PASSWORD}" -e "SELECT 1;" &>/dev/null; then
        print_success "weizhi用户连接成功"
    else
        print_error "weizhi用户连接失败"
        return 1
    fi
    
    # 测试数据库访问权限
    print_info "测试weizhi用户数据库权限..."
    if docker compose --env-file production.env -f docker-compose.prod.yml exec -T mysql mysql -u weizhi -p"${MYSQL_PASSWORD}" -e "USE weizhi; SHOW TABLES;" &>/dev/null; then
        print_success "weizhi用户数据库权限正常"
    else
        print_warning "weizhi用户数据库权限可能有问题，但这在首次部署时是正常的"
    fi
    
    # 显示用户信息
    print_info "显示MySQL用户信息..."
    docker compose --env-file production.env -f docker-compose.prod.yml exec -T mysql mysql -u root -p"${MYSQL_ROOT_PASSWORD}" -e "
        SELECT host, user, authentication_string FROM mysql.user WHERE user IN ('root', 'weizhi');
    "
    
    print_success "MySQL连接测试完成"
}

# 测试应用连接
test_application_connection() {
    print_info "测试应用连接..."
    
    # 检查server容器状态
    if docker compose --env-file production.env -f docker-compose.prod.yml ps | grep -q "weizhi-server-prod.*Up"; then
        print_success "server容器正在运行"
        
        # 测试应用健康检查
        print_info "测试应用健康检查..."
        if docker compose --env-file production.env -f docker-compose.prod.yml exec -T server curl -f http://localhost:3001/api/health &>/dev/null; then
            print_success "应用健康检查通过"
        else
            print_error "应用健康检查失败"
        fi
    else
        print_warning "server容器未运行，跳过应用连接测试"
    fi
}

# 显示诊断信息
show_diagnostics() {
    print_info "显示诊断信息..."
    
    echo "=== 环境变量 ==="
    echo "MYSQL_USER: ${MYSQL_USER}"
    echo "MYSQL_DATABASE: ${MYSQL_DATABASE}"
    echo "MYSQL_PASSWORD: [已设置，长度: ${#MYSQL_PASSWORD}]"
    echo "MYSQL_ROOT_PASSWORD: [已设置，长度: ${#MYSQL_ROOT_PASSWORD}]"
    
    echo ""
    echo "=== 容器状态 ==="
    docker compose --env-file production.env -f docker-compose.prod.yml ps
    
    echo ""
    echo "=== MySQL容器日志（最后10行）==="
    docker compose --env-file production.env -f docker-compose.prod.yml logs --tail=10 mysql
    
    echo ""
    echo "=== Server容器日志（最后10行）==="
    docker compose --env-file production.env -f docker-compose.prod.yml logs --tail=10 server
}

# 主函数
main() {
    print_info "开始MySQL连接测试..."
    echo
    
    check_requirements
    load_environment
    
    # 检查MySQL容器是否运行
    if docker compose --env-file production.env -f docker-compose.prod.yml ps | grep -q "weizhi-mysql-prod.*Up"; then
        print_success "MySQL容器正在运行"
        test_mysql_connection
        test_application_connection
    else
        print_error "MySQL容器未运行，请先启动服务"
        exit 1
    fi
    
    echo ""
    show_diagnostics
    
    print_success "测试完成！"
}

# 显示帮助信息
show_help() {
    echo "MySQL连接测试脚本"
    echo
    echo "用法: $0 [选项]"
    echo
    echo "选项:"
    echo "  -h, --help     显示帮助信息"
    echo "  --diagnostics  仅显示诊断信息"
    echo
}

# 解析命令行参数
case "${1:-}" in
    -h|--help)
        show_help
        exit 0
        ;;
    --diagnostics)
        check_requirements
        load_environment
        show_diagnostics
        ;;
    "")
        main
        ;;
    *)
        print_error "未知选项: $1"
        show_help
        exit 1
        ;;
esac