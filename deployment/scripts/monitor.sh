#!/bin/bash

# 蔚之领域智能科技 - 服务监控脚本（简化版）
# 用于监控服务状态和健康检查

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 配置变量
DEPLOY_DIR="/opt/weishi"
ENV_FILE="production.env"

# 打印消息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 加载环境变量
load_environment() {
    if [ -f "$DEPLOY_DIR/$ENV_FILE" ]; then
        cd "$DEPLOY_DIR"
        set -a
        source "$ENV_FILE"
        set +a
    fi
}

# 检查容器状态
check_containers() {
    print_info "检查容器状态..."
    
    local failed_containers=()
    local containers=("weishi-mysql-prod" "weishi-server-prod" "weishi-web-prod" "weishi-admin-prod" "weishi-caddy-prod")
    
    for container in "${containers[@]}"; do
        if docker ps --format "table {{.Names}}\t{{.Status}}" | grep -q "$container"; then
            status=$(docker ps --format "table {{.Names}}\t{{.Status}}" | grep "$container" | awk '{for(i=2;i<=NF;i++) printf "%s ", $i; print ""}')
            if [[ $status == *"Up"* ]]; then
                print_success "$container: 运行正常"
            else
                print_error "$container: 状态异常"
                failed_containers+=("$container")
            fi
        else
            print_error "$container: 未运行"
            failed_containers+=("$container")
        fi
    done
    
    if [ ${#failed_containers[@]} -gt 0 ]; then
        return 1
    fi
    
    return 0
}

# 检查服务健康状态
check_health() {
    print_info "检查服务健康状态..."
    
    local failed_services=()
    
    # 检查API健康状态
    if ! curl -f -s http://localhost:3001/api/health >/dev/null 2>&1; then
        print_error "API服务健康检查失败"
        failed_services+=("API")
    else
        print_success "API服务健康"
    fi
    
    # 检查Web服务健康状态
    if ! curl -f -s http://localhost:3000/api/health >/dev/null 2>&1; then
        print_error "Web服务健康检查失败"
        failed_services+=("Web")
    else
        print_success "Web服务健康"
    fi
    
    # 检查Caddy健康状态
    if ! curl -f -s http://localhost:80/health >/dev/null 2>&1; then
        print_warning "Caddy服务健康检查失败"
    else
        print_success "Caddy服务健康"
    fi
    
    if [ ${#failed_services[@]} -gt 0 ]; then
        return 1
    fi
    
    return 0
}

# 检查数据库状态
check_database() {
    print_info "检查数据库状态..."
    
    if ! docker exec weishi-mysql-prod mysqladmin ping -h localhost -u root -p"$MYSQL_ROOT_PASSWORD" >/dev/null 2>&1; then
        print_error "数据库连接失败"
        return 1
    fi
    
    print_success "数据库连接正常"
    return 0
}

# 检查资源使用情况
check_resources() {
    print_info "检查资源使用情况..."
    
    # 检查磁盘使用率
    disk_usage=$(df -h / | awk 'NR==2 {print $5}' | sed 's/%//')
    if [ "$disk_usage" -gt 85 ]; then
        print_warning "磁盘使用率过高: ${disk_usage}%"
    else
        print_success "磁盘使用率正常: ${disk_usage}%"
    fi
    
    # 检查内存使用率
    memory_info=$(free | grep Mem)
    total_mem=$(echo $memory_info | awk '{print $2}')
    used_mem=$(echo $memory_info | awk '{print $3}')
    memory_usage=$(echo "scale=1; $used_mem * 100 / $total_mem" | bc)
    
    if (( $(echo "$memory_usage > 85" | bc -l) )); then
        print_warning "内存使用率过高: ${memory_usage}%"
    else
        print_success "内存使用率正常: ${memory_usage}%"
    fi
}

# 主监控函数
monitor() {
    print_info "开始监控检查..."
    
    local check_failed=false
    
    if ! check_containers; then
        check_failed=true
    fi
    
    if ! check_health; then
        check_failed=true
    fi
    
    if ! check_database; then
        check_failed=true
    fi
    
    check_resources
    
    if [ "$check_failed" = true ]; then
        print_error "监控检查发现问题"
        return 1
    else
        print_success "所有监控检查通过"
        return 0
    fi
}

# 显示帮助
show_help() {
    echo "蔚之领域智能科技 - 服务监控脚本（简化版）"
    echo
    echo "用法: $0 [选项]"
    echo
    echo "选项:"
    echo "  -h, --help              显示帮助信息"
    echo "  --containers            只检查容器状态"
    echo "  --health                只检查服务健康状态"
    echo "  --database              只检查数据库状态"
    echo "  --resources             只检查资源使用情况"
    echo
    echo "示例:"
    echo "  $0                      # 执行一次完整监控"
    echo "  $0 --containers        # 只检查容器状态"
}

# 解析参数
case "${1:-}" in
    -h|--help)
        show_help
        exit 0
        ;;
    --containers)
        load_environment
        check_containers
        ;;
    --health)
        load_environment
        check_health
        ;;
    --database)
        load_environment
        check_database
        ;;
    --resources)
        load_environment
        check_resources
        ;;
    "")
        load_environment
        monitor
        ;;
    *)
        print_error "未知选项: $1"
        show_help
        exit 1
        ;;
esac