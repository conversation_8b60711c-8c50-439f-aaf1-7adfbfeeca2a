#!/bin/bash

# 蔚之领域智能科技 - 数据备份脚本（简化版）
# 用于备份数据库和配置文件

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 配置变量
DEPLOY_DIR="/opt/weishi"
BACKUP_DIR="/opt/weishi/backups"
ENV_FILE="production.env"
RETENTION_DAYS=30

# 打印消息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 加载环境变量
load_environment() {
    if [ -f "$DEPLOY_DIR/$ENV_FILE" ]; then
        cd "$DEPLOY_DIR"
        set -a
        source "$ENV_FILE"
        set +a
    fi
}

# 创建备份目录
create_backup_dir() {
    local timestamp=$(date +%Y%m%d_%H%M%S)
    CURRENT_BACKUP_DIR="$BACKUP_DIR/backup_$timestamp"
    mkdir -p "$CURRENT_BACKUP_DIR"
    print_info "创建备份目录: $CURRENT_BACKUP_DIR"
}

# 备份数据库
backup_database() {
    print_info "备份数据库..."
    
    if ! docker ps | grep -q "weishi-mysql-prod"; then
        print_error "MySQL容器未运行"
        return 1
    fi
    
    # 备份主数据库
    docker exec weishi-mysql-prod mysqldump \
        -u root -p"$MYSQL_ROOT_PASSWORD" \
        --single-transaction \
        --routines \
        --triggers \
        "$MYSQL_DATABASE" > "$CURRENT_BACKUP_DIR/weizhi.sql"
    
    # 压缩备份
    gzip "$CURRENT_BACKUP_DIR/weizhi.sql"
    
    print_success "数据库备份完成"
}

# 备份配置文件
backup_configs() {
    print_info "备份配置文件..."
    
    # 备份部署配置
    cp -r "$DEPLOY_DIR"/*.yml "$CURRENT_BACKUP_DIR/" 2>/dev/null || true
    cp -r "$DEPLOY_DIR"/*.env "$CURRENT_BACKUP_DIR/" 2>/dev/null || true
    cp -r "$DEPLOY_DIR"/caddy "$CURRENT_BACKUP_DIR/" 2>/dev/null || true
    cp -r "$DEPLOY_DIR"/mysql "$CURRENT_BACKUP_DIR/" 2>/dev/null || true
    
    print_success "配置文件备份完成"
}

# 创建备份信息
create_backup_info() {
    cat > "$CURRENT_BACKUP_DIR/backup_info.txt" << EOF
蔚之领域智能科技 - 备份信息
================================

备份时间: $(date)
备份类型: 简化备份
服务器: $(hostname)

环境信息:
- 域名: ${DOMAIN:-未设置}
- 数据库: ${MYSQL_DATABASE:-weizhi}

备份内容:
- 数据库备份: weizhi.sql.gz
- 配置文件: $(find "$CURRENT_BACKUP_DIR" -name "*.yml" -o -name "*.env" | wc -l) 个文件

备份大小: $(du -sh "$CURRENT_BACKUP_DIR" | cut -f1)
EOF
}

# 清理旧备份
cleanup_old_backups() {
    print_info "清理旧备份..."
    find "$BACKUP_DIR" -name "backup_*" -type d -mtime +$RETENTION_DAYS -exec rm -rf {} \; 2>/dev/null || true
    print_success "旧备份清理完成"
}

# 显示帮助
show_help() {
    echo "蔚之领域智能科技 - 数据备份脚本（简化版）"
    echo
    echo "用法: $0 [选项]"
    echo
    echo "选项:"
    echo "  -h, --help              显示帮助信息"
    echo "  --retention DAYS        备份保留天数（默认30天）"
    echo
    echo "示例:"
    echo "  $0                      # 备份数据库和配置"
    echo "  $0 --retention 7        # 保留7天备份"
}

# 主函数
main() {
    load_environment
    create_backup_dir
    backup_database
    backup_configs
    create_backup_info
    cleanup_old_backups
    
    print_success "备份完成: $CURRENT_BACKUP_DIR"
}

# 解析参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        --retention)
            RETENTION_DAYS="$2"
            shift 2
            ;;
        *)
            print_error "未知选项: $1"
            show_help
            exit 1
            ;;
    esac
done

main