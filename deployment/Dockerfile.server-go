# 阿里云优化的Go后端服务多阶段构建Dockerfile
# 构建阶段：编译Go应用
# 运行阶段：精简的Alpine运行时环境

# ================================
# 构建阶段
# ================================
FROM docker.cnb.cool/yuandongbin/docker-sync/golang:1.23-alpine-linux-amd64 AS builder

# 设置阿里云Alpine镜像源
RUN sed -i 's/dl-cdn.alpinelinux.org/mirrors.aliyun.com/g' /etc/apk/repositories

# 安装构建依赖
RUN apk add --no-cache git

# 设置Go环境变量
ENV GOPROXY=https://goproxy.cn,direct
ENV GO111MODULE=on
ENV CGO_ENABLED=0
ENV GOOS=linux
ENV GOARCH=amd64

# 设置工作目录
WORKDIR /app

# 复制go.mod和go.sum（优化Docker缓存）
COPY server-go/go.mod server-go/go.sum ./

# 下载依赖
RUN go mod download && go mod verify

# 复制源代码
COPY server-go/ .

# 构建应用
RUN CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o main cmd/main.go

# ================================
# 运行阶段 - 使用最小化镜像
# ================================
FROM docker.cnb.cool/yuandongbin/docker-sync/golang:1.23-alpine-linux-amd64 AS runner

# 设置阿里云Alpine镜像源
RUN sed -i 's/dl-cdn.alpinelinux.org/mirrors.aliyun.com/g' /etc/apk/repositories

# 安装证书、时区和curl（用于健康检查）
RUN apk --no-cache add ca-certificates tzdata curl

# 设置时区
ENV TZ=Asia/Shanghai

WORKDIR /root/

# 从构建阶段复制二进制文件和脚本
COPY --from=builder /app/main .
COPY --from=builder /app/config.yaml .
COPY --from=builder /app/scripts ./scripts/

# 创建配置替换脚本
RUN echo '#!/bin/sh' > /entrypoint.sh && \
    echo 'sed -i "s/\${MYSQL_PASSWORD}/'"'"'${MYSQL_PASSWORD}'"'"'/g" /root/config.yaml' >> /entrypoint.sh && \
    echo 'sed -i "s/\${JWT_SECRET}/'"'"'${JWT_SECRET}'"'"'/g" /root/config.yaml' >> /entrypoint.sh && \
    echo 'exec "$@"' >> /entrypoint.sh && \
    chmod +x /entrypoint.sh

# 创建日志目录
RUN mkdir -p logs

# 暴露端口
EXPOSE 3001

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:3001/api/health || exit 1

# 运行应用
ENTRYPOINT ["/entrypoint.sh"]
CMD ["./main"]
